"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SmsService = void 0;
const axios_1 = require("@nestjs/axios");
const common_1 = require("@nestjs/common");
const rxjs_1 = require("rxjs");
const env_1 = require("../env");
let SmsService = class SmsService {
    constructor(httpService) {
        this.httpService = httpService;
    }
    async sendSMS(to, message) {
        (async () => {
            try {
                await (0, rxjs_1.firstValueFrom)(this.httpService.post(env_1.env.SMS_API_URL, {
                    msisdn: to,
                    message
                }));
                common_1.Logger.log(`SMS sent to ${to}`, 'SmsService');
            }
            catch (error) {
                common_1.Logger.error(`Failed to send SMS: ${error.message}`, 'SmsService');
            }
        })();
    }
};
exports.SmsService = SmsService;
exports.SmsService = SmsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [axios_1.HttpService])
], SmsService);
//# sourceMappingURL=sms.service.js.map