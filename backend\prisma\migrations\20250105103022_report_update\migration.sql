/*
  Warnings:

  - You are about to drop the column `involvedAircraft` on the `Report` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[investigatorEmail]` on the table `Report` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `accessRequirements` to the `Report` table without a default value. This is not possible if the table is not empty.
  - Added the required column `aircraftInfo` to the `Report` table without a default value. This is not possible if the table is not empty.
  - Added the required column `areaCharacteristics` to the `Report` table without a default value. This is not possible if the table is not empty.
  - Added the required column `investigationDelegation` to the `Report` table without a default value. This is not possible if the table is not empty.
  - Added the required column `investigationExtent` to the `Report` table without a default value. This is not possible if the table is not empty.
  - Added the required column `investigatorEmail` to the `Report` table without a default value. This is not possible if the table is not empty.
  - Added the required column `investigatorMobile` to the `Report` table without a default value. This is not possible if the table is not empty.
  - Added the required column `investigatorName` to the `Report` table without a default value. This is not possible if the table is not empty.
  - Added the required column `investigatorTelephone` to the `Report` table without a default value. This is not possible if the table is not empty.
  - Added the required column `originatingAuthority` to the `Report` table without a default value. This is not possible if the table is not empty.
  - Changed the type of `latitude` on the `Report` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `longitude` on the `Report` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- AlterTable
ALTER TABLE "Report" DROP COLUMN "involvedAircraft",
ADD COLUMN     "accessRequirements" TEXT NOT NULL,
ADD COLUMN     "aircraftInfo" JSONB NOT NULL,
ADD COLUMN     "areaCharacteristics" TEXT NOT NULL,
ADD COLUMN     "investigationDelegation" TEXT NOT NULL,
ADD COLUMN     "investigationExtent" TEXT NOT NULL,
ADD COLUMN     "investigatorEmail" TEXT NOT NULL,
ADD COLUMN     "investigatorMobile" TEXT NOT NULL,
ADD COLUMN     "investigatorName" TEXT NOT NULL,
ADD COLUMN     "investigatorTelephone" TEXT NOT NULL,
ADD COLUMN     "originatingAuthority" TEXT NOT NULL,
DROP COLUMN "latitude",
ADD COLUMN     "latitude" DOUBLE PRECISION NOT NULL,
DROP COLUMN "longitude",
ADD COLUMN     "longitude" DOUBLE PRECISION NOT NULL,
ALTER COLUMN "dangerousGoodsPresent" DROP DEFAULT;

-- CreateIndex
CREATE UNIQUE INDEX "Report_investigatorEmail_key" ON "Report"("investigatorEmail");
