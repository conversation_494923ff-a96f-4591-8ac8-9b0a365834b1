import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MessageService, ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { Contact, Occurrence } from '../../../util/@types';
import { OccurrencesService } from './occurrences.service';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-occurrences',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    TableModule,
    DialogModule,
    ToastModule,
    ConfirmDialogModule,
  ],
  providers: [MessageService, ConfirmationService], 
  templateUrl: './occurrences.component.html',
})
export class OccurrencesComponent {
  occurrences: Occurrence[] = [];
  totalOccurrences: number = 0;
  currentPage: number = 1;
  pageSize: number = 10;

  occurrenceModalVisible: boolean = false;
  isEditMode: boolean = false;
  
  currentOccurrence: Partial<Occurrence> = {};
  searchQuery: string = ''


  constructor(
    private occcurrenceService: OccurrencesService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService
  ) {}

  ngOnInit(): void {
    this.loadOccurrences();
  }

  async loadOccurrences(page: number = 1) {
    try {
      const response = await this.occcurrenceService.getOccurrences(page, this.pageSize);
      this.occurrences = response.data;
      this.totalOccurrences = response.total;
    } catch (error) {
      this.handleError('Failed to load occurrences', error);
    }
  }

  onPageChange(event: any) {
    this.currentPage = event.page + 1;
    this.pageSize = event.rows;
    this.loadOccurrences(this.currentPage);
  }

  confirmDelete(occurrence: Occurrence) {
    this.confirmationService.confirm({
      message: `Are you sure you want to delete this occurrence`,
      header: 'Confirm Deletion',
      icon: 'pi pi-info-circle',
      acceptIcon:"none",
      rejectIcon:"none",
      acceptButtonStyleClass:"p-button-danger p-button-text",
      rejectButtonStyleClass:"p-button-text p-button-text mr-4", 
      accept: () => this.deleteOccurrence(occurrence.id!)
    });
  }

  async deleteOccurrence(contactId: string) {
    try {
      await this.occcurrenceService.deleteOccurrence(contactId);
      this.messageService.add({
        severity: 'success', 
        summary: 'Occurrence Deleted', 
        detail: 'Occurrence has been successfully deleted.'
      });
      this.loadOccurrences(this.currentPage);
    } catch (error) {
      this.handleError('Failed to delete occurrence', error);
    }
  }

  async search(){
    try {
      if(this.searchQuery === '') {
        this.loadOccurrences()
        return
      }
      const resp = await this.occcurrenceService.search(this.searchQuery)
      this.occurrences = resp.data
    } catch (error) {
      this.handleError('search failed', error);
    }
  }

  formatDate(date: string): string {
    return new Date(date).toLocaleString();
  }

  private handleError(message: string, error: any) {
    console.error(error);
    this.messageService.add({
      severity: 'error', 
      summary: 'Error', 
      detail: error.response.data.message || error.response.data.message[0] || error.message || message
    });
  }
}
