import { Routes } from '@angular/router';
import { NotfoundComponent } from './notfound/notfound.component';
import { LoginComponent } from './portal/auth/login/login.component';
import { ResetPasswordComponent } from './portal/auth/reset-password/reset-password.component';
import { ActivateAccountComponent } from './portal/auth/activate/activate-account.component';
import { ConfirmResetComponent } from './portal/auth/reset-password/confirm-reset/confirm-reset.component';
import { PublicFormComponent } from './public-form/public-form.component';
import { PortalLayoutComponent } from './portal/app/layout.component';
import { DashboardComponent } from './portal/app/dashboard/dashboard.component';
import { OccurrencesComponent } from './portal/app/occurrences/occurrences.component';
import { DocumentsComponent } from './portal/app/documents/documents.component';
import { ReportsComponent } from './portal/app/reports/reports.component';
import { NotificationsComponent } from './portal/app/notifications/notifications.component';
import { UsersComponent } from './portal/admin/users/users.component';
import { CategoriesComponent } from './portal/admin/categories/categories.component';
import { AdminLayoutComponent } from './portal/admin/layout.component';
import { ContactsComponent } from './portal/admin/contacts/contacts.component';
import { AuthGuard } from './util/guards/auth.guard';
import { RoleGuard } from './util/guards/role.guard';
import { ViewOccurrencesComponent } from './portal/app/occurrences/view/view.component';
import { EditOccurrenceComponent } from './portal/app/occurrences/edit/edit.component';
import { NewReportComponent } from './portal/app/reports/new/new.component';
import { AircraftsComponent } from './portal/admin/aicrafts/aircrafts.component';
import { Verify2faComponent } from './portal/auth/login/2fa/2fa.component';
import { SuccessComponent } from './public-form/success/success.components';
import { EditReportComponent } from './portal/app/reports/edit/edit.component';
import { SignatureComponent } from './portal/admin/signature/signature.component';
import { DGLayoutComponent } from './portal/DG/layout.component';
import { LogsComponent } from './portal/admin/logs/logs.component';
import { DgEditOccurrenceComponent } from './portal/DG/occurrence/edit/edit.component';
import { DgViewOccurrencesComponent } from './portal/DG/occurrence/view/view.component';
import { DgOccurrencesComponent } from './portal/DG/occurrence/occurrences.component';
import { DgEditReportComponent } from './portal/DG/reports/edit/edit.component';
import { DgReportsComponent } from './portal/DG/reports/reports.component';

export const routes: Routes = [
    {
        path: '',
        component: PublicFormComponent
    },
    {
        path: 'success',
        component: SuccessComponent
    },
    {
        path: 'portal',
        children: [
            {
                path: 'auth',
                children: [
                    {
                        path: 'login',
                        children: [
                            {
                                path: '',
                                component: LoginComponent
                            },
                            {
                                path: 'verify',
                                component: Verify2faComponent
                            },
                        ]
                    },
                    {
                        path: 'reset-password',
                        component: ResetPasswordComponent
                    },
                    {
                        path: 'confirm-reset-password',
                        component: ConfirmResetComponent
                    },
                    {
                        path: 'activate',
                        component: ActivateAccountComponent
                    },
                ]
            },
            {
                path: '',
                component: PortalLayoutComponent,
                canActivate: [AuthGuard],
                children: [
                    {
                        path: 'dashboard',
                        component: DashboardComponent
                    },
                    {
                        path: 'occurrences',
                        children: [
                            {
                                path: '',
                                component: OccurrencesComponent,
                            },
                            {
                                path: 'view/:id',
                                component: ViewOccurrencesComponent
                            },
                            {
                                path: 'edit/:id',
                                component: EditOccurrenceComponent
                            }
                        ]
                    },
                    {
                        path: 'documents',
                        component: DocumentsComponent
                    },
                    {
                        path: 'reports',
                        children: [
                            {
                                path: '',
                                component: ReportsComponent
                            },
                            {
                                path: 'new/:occurrence',
                                component: NewReportComponent
                            },
                            {
                                path: 'edit/:id',
                                component: EditReportComponent
                            }
                        ]
                    },
                    {
                        path: 'notifications',
                        component: NotificationsComponent
                    },
                    {
                        path: '',
                        redirectTo: 'dashboard',
                        pathMatch: 'full'
                    }
                ]
            },
        ]
    },
    {
        path: 'admin',
        component: AdminLayoutComponent,
        canActivate: [AuthGuard, RoleGuard],
        data: {role: 'ADMIN'},
        children: [
            {
                path: 'users',
                component: UsersComponent
            },
            {
                path: 'signature',
                component: SignatureComponent
            },
            {
                path: 'contacts',
                component: ContactsComponent
            },
            {
                path: 'categories',
                component: CategoriesComponent
            },
            {
                path: 'aircrafts',
                component: AircraftsComponent
            },
            {
                path: 'logs',
                component: LogsComponent
            },
            {
                path: '',
                redirectTo: 'users',
                pathMatch: 'full'
            }
        ]
    },
    {
        path: 'dg',
        component: DGLayoutComponent,
        canActivate: [AuthGuard, RoleGuard],
        data: {role: 'DG,ACTING_DG'},
        children: [
            {
                path: 'dashboard',
                component: DashboardComponent
            },
            {
                path: 'occurrences',
                children: [
                    {
                        path: '',
                        component: DgOccurrencesComponent,
                    },
                    {
                        path: 'view/:id',
                        component: DgViewOccurrencesComponent
                    },
                    {
                        path: 'edit/:id',
                        component: DgEditOccurrenceComponent
                    },
                    
                ]
            },
            {
                path: 'reports',
                children: [
                    {
                        path: '',
                        component: DgReportsComponent
                    },
                    {
                        path: 'edit/:id',
                        component: DgEditReportComponent
                    }
                ]
            },
            {
                path: '',
                redirectTo: 'dashboard',
                pathMatch: 'full'
            }
        ]
    },
    {
        path: "**",
        component: NotfoundComponent
    }
];
