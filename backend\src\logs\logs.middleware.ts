import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { PrismaService } from '../prisma/prisma.service';
import {UAParser} from 'ua-parser-js';
import { JwtService } from '@nestjs/jwt';
import { env } from 'src/utils/env';

@Injectable()
export class LoggerMiddleware implements NestMiddleware {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService
) {}

  private determineAction(method: string, url: string): string {
    const actionMap = [
      { method: 'POST', urlPattern: 'auth/login', action: 'User Login' },
      { method: 'POST', urlPattern: 'auth/verify-2fa', action: '2FA Verification' },

      { method: 'POST', urlPattern: 'occurrence/public/submit', action: 'Submit Occurrence' },
      { method: 'GET', urlPattern: 'occurrence/search', action: 'Seach Occurrenec' },
      { method: 'PUT', urlPattern: 'occurrence/search', action: 'Update Occurrenec' },
      { method: 'DELETE', urlPattern: 'occurrence/search', action: 'Delete Occurrenec' },

      { method: 'POST', urlPattern: 'occurrence/refnumber', action: 'Generate Ref Number' },

      { method: 'POST', urlPattern: 'users/create', action: 'User Registration' },
      
      { method: 'GET', urlPattern: 'users', action: 'View Users List' },
      { method: 'GET', urlPattern: 'users/', action: 'View User Profile' },
      { method: 'POST', urlPattern: 'users', action: 'Create User' },
      { method: 'PATCH', urlPattern: 'users/', action: 'Update User' },
      { method: 'DELETE', urlPattern: 'users/', action: 'Delete User' },


      { method: 'GET', urlPattern: 'logs', action: 'View logs ' },
      { method: 'GET', urlPattern: 'logs/', action: 'View Logs' },
      { method: 'POST', urlPattern: 'logs', action: 'Create User' },
      { method: 'PATCH', urlPattern: 'logs/', action: 'Update User' },
      { method: 'DELETE', urlPattern: 'logs/', action: 'Delete User' },


      { method: 'POST', urlPattern: 'reports', action: 'Creare report' },
      { method: 'GET', urlPattern: 'reports', action: 'Get report' },
      { method: 'PATCH', urlPattern: 'reports/', action: 'Updare report' },
      { method: 'DELETE', urlPattern: 'reports', action: 'Delete Report' },
      { method: 'PATCH', urlPattern: 'reports/submit', action: 'Submit report' },
      { method: 'PATCH', urlPattern: 'reports/approve', action: 'Approve Report' },
      { method: 'PATCH', urlPattern: 'reports/revert', action: 'Revert Report' },
      
      

    ];

    const matchedAction = actionMap.find(pattern => 
      method === pattern.method && url.includes(pattern.urlPattern)
    );

    if (matchedAction) {
      return matchedAction.action;
    }

    return `${method} ${url}`;
  }

  async use(req: Request, res: Response, next: NextFunction) {
    const userAgent = new UAParser(req.headers['user-agent']);
    const browserInfo = userAgent.getBrowser();
    const osInfo = userAgent.getOS();

    const token = this.extractTokenFromHeader(req);
    
    let id = null
    
    try{
      const payload = await this.jwtService.verifyAsync(token, {
        secret: env.JWT_SECRET,
      });
      id = payload.id
    }catch(error){
      
    }

    try {
      await this.prisma.logs.create({
        data: {
          action: this.determineAction(req.method, req.originalUrl),
          sourceUrl: req.get('origin') || req.get('referer') || 'Direct',
          sourceIpAddress: 
            req.socket.remoteAddress ||
            req.ip || 
            req.connection.remoteAddress || 
            'Unknown',
          sourceOS: `${osInfo.name || 'Unknown'} ${osInfo.version || ''}`.trim(),
          sourceBrowser: `${browserInfo.name || 'Unknown'} ${browserInfo.version || ''}`.trim(),
          url: req.originalUrl,
          method: req.method as any,
          userId: id || null,
        },
      });
    } catch (error) {
      console.error('Failed to create log entry:', error);
    }

    next();
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}