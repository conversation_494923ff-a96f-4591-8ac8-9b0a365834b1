import {
  Injectable,
  NotAcceptableException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from 'src/prisma/prisma.service';
import { ApiResponse } from 'src/utils/@types';
import { AccountStatus, HTTP_Method, User } from '@prisma/client';
import * as bcrypt from 'bcrypt';
import { MailService, MailType } from 'src/utils/mail/mail.service';
import * as useragent from 'express-useragent';
import { SmsService } from 'src/utils/sms/sms.service';
import { env } from 'src/utils/env';
import * as speakeasy from 'speakeasy';
import * as qrcode from 'qrcode';

@Injectable()
export class AuthenticationService {
  constructor(
    private readonly prisma: PrismaService,
    private jwtService: JwtService,
    private mailService: MailService,
    private smsService: SmsService,
  ) { }

  private generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  async sendVerificationCode(
    user: User,
    type: 'ACTIVATION' | 'PASSWORD_RESET'
  ): Promise<ApiResponse<any>> {
    const verificationCode = this.generateVerificationCode();

    await this.prisma.user.update({
      where: { id: user.id },
      data: {
        resetToken: verificationCode,
        resetTokenExpiry: new Date(Date.now() + 45 * 60 * 1000) // 45 minutes expiry
      }
    });

    this.mailService.sendMail(
      type === 'ACTIVATION' ? MailType.VERIFY_ACCOUNT : MailType.RESET_PASSWORD,
      {
        name: user.name,
        to: user.email,
        subject: type === 'ACTIVATION'
          ? 'Activate Your Account | AAID Portal'
          : 'Reset Your Password | AAID Portal',
        values: {
          name: user.name,
          code: verificationCode,
          resetLink: `${env.FE_URL}/portal/auth/${type === 'ACTIVATION' ? 'activate' : 'confirm-reset-password'}?token=${verificationCode}`,
        },
      }
    );

    await this.smsService.sendSMS(
      user.telephone,
      `Hello ${user.name},\nYour ${type === 'ACTIVATION' ? 'account activation invitation code' : 'password reset '} code is: ${verificationCode}`
    );

    return new ApiResponse(true, 'Verification code sent via email and SMS', null);
  }

  async login(
    identifier: string, // can be email or phone
    password: string,
    req: Request,
  ): Promise<ApiResponse<any>> {
    const user = await this.prisma.user.findFirst({
      where: {
        OR: [
          { email: identifier },
          { telephone: identifier }
        ]
      },
    });

    if (!user) {
      throw new NotAcceptableException('Invalid credentials');
    }

    if (user.status !== AccountStatus.ACTIVE) {
      throw new NotAcceptableException('Account is not active');
    }

    const passwordExpired = user.passwordExpiryDate < new Date();
    if (passwordExpired) {
      throw new NotAcceptableException('Password has expired. Please reset your password.');
    }

    const passwordsMatch = await bcrypt.compare(password, user.password);

    if (!passwordsMatch) {
      throw new NotAcceptableException('Invalid credentials');
    }

    const requireVerification = user.multiFactorEnabled;

    delete user.password;
    delete user.resetToken;

    if (requireVerification) {
      // await this.sendVerificationCode(user, 'ACTIVATION');

      return new ApiResponse(true, '2FA REQUIRED', {
        user
      });
    }

    const token = this.generateLoginToken(user);

    await this.logLoginAttempt(user, req);

    return new ApiResponse(true, 'Login successful', {
      token,
      user,
      requireVerification: false
    });
  }

  async verifyMultiFactorCode(
    userId: string,
    code: string
  ): Promise<ApiResponse<{ token: string; user: User }>> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if 2FA is enabled and secret exists
    if (user.multiFactorEnabled && user.multiFactorSecret) {
      const isValidToken = this.verifyTwoFactorToken(
        user.multiFactorSecret,
        code
      );

      if (!isValidToken) {
        throw new NotAcceptableException('Invalid 2FA token');
      }
    } else {
      // Fallback to existing reset token verification
      if (
        user.resetToken !== code ||
        (user.resetTokenExpiry && user.resetTokenExpiry < new Date())
      ) {
        throw new NotAcceptableException('Invalid or expired verification code');
      }

      await this.prisma.user.update({
        where: { id: user.id },
        data: {
          resetToken: null,
          resetTokenExpiry: null
        }
      });
    }

    const token = this.generateLoginToken(user);

    delete user.password;
    delete user.resetToken;

    return new ApiResponse(true, 'Multi-factor authentication successful', {
      token,
      user
    });
  }

  generateTwoFactorSecret(): { secret: string; otpAuthUrl: string } {
    const secret = speakeasy.generateSecret({
      name: "AAID Portal"
    });

    return {
      secret: secret.base32,
      otpAuthUrl: secret.otpauth_url
    };
  }

  async generateQRCode(otpAuthUrl: string): Promise<string> {
    return qrcode.toDataURL(otpAuthUrl);
  }

  private generateLoginToken(user: User): string {
    return this.jwtService.sign({
      id: user.id,
      email: user.email,
      name: user.name,
      telephone: user.telephone,
      profilePicture: user.profilePicture,
      role: user.role,
      expiresIn: '45m'
    });
  }

  verifyTwoFactorToken(
    secret: string,
    token: string
  ): boolean {
    return speakeasy.totp.verify({
      secret: secret,
      encoding: 'base32',
      token: token,
      window: 1
    });
  }

  private async logLoginAttempt(user: User, req: Request): Promise<void> {
    const { browser, os } = useragent.parse(req.headers['user-agent']);

    // await this.prisma.logs.create({
    //   data: {
    //     userId: user.id,
    //     action: 'LOGIN',
    //     sourceBrowser: browser,
    //     method: HTTP_Method.POST,
    //     sourceIpAddress: geoIp[0],
    //     sourceOS: os,
    //     sourceUrl: req.referrer,
    //     url: req.url
    //   }
    // });

    this.mailService.sendMail(MailType.LOGIN, {
      name: user.name,
      to: user.email,
      subject: 'New Login | AAID Notification',
      values: {
        name: user.name,
        time: new Date().toLocaleString(),
        device: `${os} / ${browser}`
      }
    })
  }
}