<div class="w-full h-full p-8 flex flex-col gap-4 overflow-scroll">
  <div class="flex items-center justify-between">
    <h2 class="text-xl font-semibold text-gray-700">Documents</h2>
    <button (click)="openOccurrenceSelectionModal()"
      class="flex items-center gap-2 py-2 px-4 rounded-lg text-sm font-medium text-white bg-primary-500 hover:bg-primary-600 active:scale-95 shadow-md">
      <i class="pi pi-plus"></i>
      Add Document
    </button>
  </div>

  <div class="flex items-center gap-4 pt-12">
    <div class="relative w-full">
      <i class="pi pi-search text-lg absolute top-2 left-3 text-gray-700"></i>
      <input
        class="outline-none bg-transparent py-2 px-3 pl-10 border border-gray-300 rounded-lg  text-gray-700 w-full focus:ring-1 focus:ring-primary-500"
        type="search" placeholder="Search Documents...">
    </div>
  </div>

  <!-- Occurrences Selection Modal -->
  <p-dialog [(visible)]="occurrenceModalVisible" [modal]="true" header="Select Occurrence"
    [style]="{width: '100%', maxWidth: '600px', height: '500px'}" [styleClass]="'occurrence-selection-modal'">
    <div class="flex flex-col justify-between h-full">
      <div class="flex flex-col gap-4 p-4">
        <p-dropdown [options]="occurrences" [(ngModel)]="selectedOccurrence" optionLabel="referenceNumber"
          [filter]="true" filterBy="referenceNumber" placeholder="Select an Occurrence" [styleClass]="'w-full'"
          [ariaLabel]="'Occurrence selection dropdown'" [style]="{'border': '1px solid #3333'}">
          <ng-template let-occurrence pTemplate="item">
            <div class="flex flex-col">
              <span class="font-semibold">{{ occurrence.referenceNumber }}</span>
              <small class="text-gray-600 text-xs">
                {{ occurrence.occurrenceLocation }}
              </small>
            </div>
          </ng-template>
        </p-dropdown>
      </div>
      <div class="flex justify-end space-x-2 mt-4">
        <button (click)="occurrenceModalVisible = false"
          class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded" pRipple>
          Cancel
        </button>
        <button (click)="openDocumentsModal()" [disabled]="!selectedOccurrence"
          class="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded" pRipple>
          Next
        </button>
      </div>
    </div>
  </p-dialog>

  <!-- Document Modal -->
  <p-dialog [(visible)]="documentModalVisible" [modal]="true"
    [header]="modalMode === 'create' ? 'Add Document' : 'Edit Document'" [style]="{width: '450px'}">
    <div class="flex flex-col gap-4">
      <div>
        <label class="block text-sm font-medium text-gray-700">Occurrence</label>
        <input pInputText [value]="selectedOccurrence?.referenceNumber" disabled
          class="w-full mt-1 border border-gray-300 rounded px-3 py-2 focus:ring-primary-500 focus:border-primary-500">
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Name</label>
        <input pInputText [(ngModel)]="currentDocument.name"
          class="w-full mt-1 border border-gray-300 rounded px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
          placeholder="Document Name">
      </div>

      <div>
        <label class="block pb-2 text-sm font-medium text-gray-700">Type</label>
        <p-dropdown [style]="{'border': '1px solid #333333'}" [options]="documentTypes"
          [(ngModel)]="currentDocument.type" optionLabel="label" optionValue="value" placeholder="Select Document Type"
          class="w-full mt-1">
        </p-dropdown>
      </div>

      <div>
        <label class="block pb-2 text-sm font-medium text-gray-700">File</label>
        <p-fileUpload
          accept="application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/zip"
          mode="basic" name="demo[]" (onSelect)="onFileSelect($event)" chooseLabel="Upload"
          [style]="{backgroundColor: '#357190'}" class="mt-1">
        </p-fileUpload>


      </div>

      <div class="flex justify-end gap-2 mt-4">
        <button (click)="closeDocumentModal()" class="text-sm text-gray-500 px-4 py-2 hover:bg-gray-200 rounded-lg">
          Cancel
        </button>
        <button (click)="saveDocument()" [disabled]="!isDocumentValid()"
          class="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-lg">
          {{ modalMode === 'create' ? 'Create' : 'Update' }}
        </button>
      </div>
    </div>
  </p-dialog>

  <div class=" p-4 rounded-lg grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    @if (documents.length < 1) { <p class="font-semibold text-gray-700 px-4">No documents</p>
      }
      <div *ngFor="let doc of documents" class="bg-white shadow-md rounded-lg p-5 flex flex-col gap-3 relative">
        <div class="absolute top-2 right-2 flex gap-2">
          <button (click)="editDocument(doc)" class="text-primary-500 hover:text-primary-600">
            <i class="pi pi-pencil"></i>
          </button>
          <button (click)="deleteDocument(doc.id)" class="text-red-500 hover:text-red-600">
            <i class="pi pi-trash"></i>
          </button>
        </div>
        <div class="preview h-[100px] mt-4">
          <ng-container *ngIf="getFileType(doc.url) === 'image'; else documentPreview">
            <img class="w-full h-full object-cover" [src]="doc.url" alt="Preview Image">
          </ng-container>
          <ng-template #documentPreview>
            <pdf-viewer [src]="doc.url" [render-text]="true" [original-size]="false"
              style="width: 100%; height: 100px"></pdf-viewer>
          </ng-template>
        </div>
        <h3 class="font-medium">{{ doc.name }}</h3>
        <p class="text-gray-600">Occurrence ref number: <a class="underline text-primary-400"
            href="/portal/occurrences/view/{{doc.occurrenceId}}">{{ doc.occurrence.referenceNumber }}</a></p>
        <p class="text-gray-600">Type: {{ doc.type }}</p>
        <p class="text-gray-600 text-sm">Uploaded on: {{ formatDate(doc.createdAt) }}</p>
        <a [href]="doc.url" target="_blank" class="text-primary-500 text-sm hover:underline">
          View Document
        </a>
      </div>
  </div>

  <p-confirmDialog></p-confirmDialog>
  <p-toast position="top-right"></p-toast>
</div>