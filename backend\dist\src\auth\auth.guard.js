"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthGuard = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const prisma_service_1 = require("../prisma/prisma.service");
const env_1 = require("../utils/env");
let AuthGuard = class AuthGuard {
    constructor() {
        this.jwtService = new jwt_1.JwtService();
        this.prismaService = new prisma_service_1.PrismaService();
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        try {
            if (request.url.includes('auth'))
                return true;
            if (request.url.includes('2fa'))
                return true;
            if (request.url.includes('users/activate'))
                return true;
            if (request.url.includes('users/reset-password'))
                return true;
            if (request.url.includes('users/confirm-reset-password'))
                return true;
            if (request.url.includes('occurrence/public/submit'))
                return true;
            if (request.url.includes('countries'))
                return true;
            const token = this.extractTokenFromHeader(request);
            if (!token) {
                throw new common_1.UnauthorizedException();
            }
            const payload = await this.jwtService.verifyAsync(token, {
                secret: env_1.env.JWT_SECRET,
            });
            const user = await this.prismaService.user.findUnique({
                where: {
                    email: payload.email,
                },
            });
            if (!user)
                return false;
            request['user'] = user;
            return true;
        }
        catch (error) {
            if (error.message == 'jwt malformed') {
                return false;
            }
        }
    }
    extractTokenFromHeader(request) {
        const [type, token] = request.headers.authorization?.split(' ') ?? [];
        return type === 'Bearer' ? token : undefined;
    }
};
exports.AuthGuard = AuthGuard;
exports.AuthGuard = AuthGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], AuthGuard);
//# sourceMappingURL=auth.guard.js.map