export enum FlightRules {
    IFR = 'IFR',
    VFR = 'VFR'
}

export enum OperationType {
    SCHEDULED = 'Scheduled', 
    CHARTER = 'Charter', 
    TRAINING = 'Training', 
    PRIVATE = 'Private', 
    DEMONSTRATION = 'Demonstration', 
    AEROBIC = 'Aerobic', 
    AERIAL_TOUR = 'Aerial Tour', 
    FIXED_WING_AIRCRAFT = 'Fixed Wing Aircraft', 
    HELICOPTER = 'Helicopter', 
    BALLOON = 'Balloon', 
    DRONE = 'Drone',
    OTHER = 'Other'
}

export enum MeteologicalCondition {
    VMC = 'VMC', // Visual Meteorological Conditions
    IMC = 'IMC'  // Instrument Meteorological Conditions
}

export enum FlighPhase {
    START_OF_ENGINES = 'Start of Engines',
    TAXI_OUT = 'Taxi-out',
    TAKE_OFF = 'Take-off',
    REJECT_TAKE_OFF = 'Reject take-off',
    CLIMB = 'Climb',
    CRUISE_FLIGHT = 'Cruise flight',
    DESCENT = 'Descent',
    APPROACH = 'Approach',
    HOLDING = 'Holding',
    CIRCUIT = 'Circuit',
    LANDING = 'Landing',
    GO_AROUND = 'Go-around',
    TAX_IN = 'Tax-in',
    PARKED = 'Parked'
}

export interface OccurrenceReport {
    // Reporter Information
    reporterName: string;
    reporterEmail: string;
    reporterPhone: string;
    pilotInCommandName?: string;
    pilotInCommandEmail?: string;
    pilotInCommandPhone?: string;

    // Aircraft Information
    involvedAircraft: Aircraft[];

    // Occurrence Details
    generalWeatherConditions?: string;
    skyCoverage?: string;
    meteologicalCondition: MeteologicalCondition;
    flightRules?: FlightRules;
    occurrenceTime?: Date;
    flightPhase?: String;
    operationType: OperationType;
    latitude?: string;
    longitude?: string;
    occurrenceLocation?: string;
    dangerousGoodCarriedOnBoard?: string;
    groundPeopleInjured?: number;
    groundPeoplePerished?: number;
}

export interface Aircraft{
    model: string;
    manufacturer?: string;
    operator?: string;
    registrationMark?: string;
    operatorNationality?: string;
    intendedLandingDateTime?: Date;
    intendedLandingPoint?: string;
    lastDeparturePoint?: string;
    crewOnBoard?: number;
    crewInjured?: number;
    crewPerished?: number;
    passengersOnBoard?: number;
    passengersInjured?: number;
    passengersPerished?: number;
}