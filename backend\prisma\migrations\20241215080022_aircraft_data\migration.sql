-- CreateTable
CREATE TABLE "AircraftData" (
    "id" TEXT NOT NULL,
    "modelFullName" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "wtc" TEXT NOT NULL,
    "wtg" TEXT NOT NULL,
    "designator" TEXT NOT NULL,
    "manufacturerCode" TEXT NOT NULL,
    "showInPart3Only" BOOLEAN NOT NULL,
    "aircraftDescription" TEXT NOT NULL,
    "engineCount" INTEGER NOT NULL,
    "engineType" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AircraftData_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "aircraft_search_index" ON "AircraftData"("modelFullName", "designator", "manufacturerCode");
