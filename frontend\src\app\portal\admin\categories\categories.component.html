<div class="w-full h-full p-8 flex flex-col gap-4 overflow-scroll">
    <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold text-gray-700">Occurrence Categories</h2>
        <button pRipple (click)="openCategoriesModal('create')"
            class="flex items-center gap-2 py-2 px-3 rounded-lg text-sm font-medium text-text-white bg-primary active:scale-95">
            <i class="pi pi-plus"></i>
            Add category
        </button>
    </div>
    <div class="pt-12">
        <div class="flex items-center gap-4">
            <div class="relative w-full">
                <i class="pi pi-search text-lg absolute top-2 left-3 text-gray-700"></i>
                <input
                    class="outline-none bg-transparent py-2 px-3 pl-10 border border-gray-300 rounded-lg  text-gray-700 w-full focus:ring-1 focus:ring-primary-500"
                    type="search" placeholder="Search categories..">
            </div>
            <button
                class="border border-gray-300 rounded-lg text-gray-700 flex items-center gap-1 p-2 px-3 active:ring ring-primary-500">
                <i class="pi pi-filter"></i>
                Filter
            </button>
        </div>

        <!-- table -->
        <div class="mt-8">
            <p-table [value]="categories" [paginator]="true" [rows]="10" [showCurrentPageReport]="true"
                [totalRecords]="totalCategories" (onPage)="onPageChange($event)"
                currentPageReportTemplate="Showing {first} to {last} of {totalRecords} categories"
                [rowsPerPageOptions]="[10, 25, 50]">
                <ng-template pTemplate="header">
                    <tr>
                        <th class="font-medium text-gray-700">Category name</th>
                        <th class="font-medium text-gray-700">Description</th>
                        <th class="font-medium text-gray-700">Actions</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-category>
                    <tr class="">
                        <td class="font-normal border-b">{{category.category}}</td>
                        <td class="font-normal border-b">{{category.description}}</td>
                        
                        <td class="font-normal border-b " class="flex items-center gap-2">
                            <button (click)="openCategoriesModal('edit', category)"
                                class="bg-primary-50 text-primary-500 py-2 px-3 rounded-lg text-lg active:scale-95 active:ring ring-primary-200"
                                title="edit user">
                                <i class="pi pi-user-edit"></i>
                            </button>
                            <button (click)="confirmDelete(category)"
                                class="bg-red-100 text-red-400 py-2 px-3 rounded-lg text-lg active:scale-95 active:ring ring-red-400"
                                title="delete user">
                                <i class="pi pi-trash"></i>
                            </button>
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="5">No categories</td>
                    </tr>
                </ng-template>
            </p-table>
            <p-dialog [(visible)]="categoriesModalVisible" [modal]="true" [header]="isEditMode ? 'Edit Category' : 'Add New Category'"
                [style]="{width: '45rem'}">
                <ng-template pTemplate="content">
                    <div class="flex flex-col gap-4">
                        <div class="flex flex-col gap-2">
                            <label for="name">Category *</label>
                            <input 
                            placeholder="Category name"
                            class="w-full outline-none border border-gray-300 rounded-md py-2 px-3 text-gray-800 focus:ring-1 focus:ring-primary "
                            id="name" [(ngModel)]="currentCategory.category" type="text" />
                        </div>

                        <div class="flex flex-col gap-2">
                            <label for="description">Description</label>
                            <textarea 
                            placeholder="Description (optional)"
                            class="w-full outline-none border border-gray-300 rounded-md py-2 px-3 text-gray-800 focus:ring-1 focus:ring-primary "
                            id="description" [(ngModel)]="currentCategory.description"></textarea>
                        </div>
                        <div class="flex flex-col gap-2">
                            <label for="explanation">Explanation</label>
                            <textarea 
                            placeholder="explanation (optional)"
                            rows="5"
                            class="w-full outline-none border border-gray-300 rounded-md py-2 px-3 text-gray-800 focus:ring-1 focus:ring-primary "
                            id="explanation" [(ngModel)]="currentCategory.explanation"></textarea>
                        </div>
                        
                    </div>
                </ng-template>
                <ng-template pTemplate="footer">
                    <button class="p-2 px-5 rounded-lg text-text-white bg-gray-600 active:scale-95" (click)="categoriesModalVisible = false">
                        Cancel
                    </button>
                    <button class="p-2 px-4 rounded-lg text-text-white bg-primary-500 ml-4 active:scale-95"
                        (click)="saveCategory()">
                        {{isEditMode ? 'Update' : 'Create'}}
                    </button>
                </ng-template>
            </p-dialog>
            <p-confirmDialog></p-confirmDialog>
            <p-toast position="top-right"></p-toast>
        </div>
    </div>
</div>