import { HttpService } from '@nestjs/axios';
export declare enum MailType {
    CREATE_ACCOUNT = 0,
    LOGIN = 1,
    OCCURENCE_SUBMIT = 2,
    VERIFY_ACCOUNT = 3,
    RESET_PASSWORD = 4,
    ACCOUNT_VERIFIED = 5,
    ACCOUNT_DEACTIVATED = 6,
    PASSWORD_RESET_CONFIRMATION = 7,
    REPORT_APPROVED = 8,
    REPORT_REVERTED = 9,
    APPROVAL_REQUEST = 10
}
export interface ISendMailProps {
    name: string;
    to: string;
    subject: string;
    values: Record<string, string>;
    attachments?: {
        filename: string;
        base64EncodedContent: string;
    }[];
}
export declare class MailService {
    private httpService;
    constructor(httpService: HttpService);
    sendMail(mailType: MailType, props: ISendMailProps): void;
    constructMailBody(template: string, props: Record<string, string>): Promise<string>;
    getMailTemplateAndFromEmail(mailType: MailType): {
        template: string;
    };
}
