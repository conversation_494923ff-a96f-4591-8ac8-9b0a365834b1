-- CreateEnum
CREATE TYPE "ReportOccurrenceType" AS ENUM ('ACCID', 'SINCID', 'INCID');

-- CreateTable
CREATE TABLE "Report" (
    "id" TEXT NOT NULL,
    "referenceNumber" TEXT NOT NULL,
    "occurrenceType" "ReportOccurrenceType" NOT NULL,
    "occurrenceDate" TIMESTAMP(3) NOT NULL,
    "occurrenceTimeUTC" TIMESTAMP(3) NOT NULL,
    "occurrenceTimeLocal" TIMESTAMP(3) NOT NULL,
    "position" TEXT NOT NULL,
    "latitude" TEXT NOT NULL,
    "longitude" TEXT NOT NULL,
    "occurrenceDescription" TEXT NOT NULL,
    "damageExtent" TEXT NOT NULL,
    "dangerousGoodsPresent" BOOLEAN NOT NULL DEFAULT false,
    "dangerousGoodsDescription" TEXT,
    "involvedAircraft" JSONB[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Report_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Report_referenceNumber_key" ON "Report"("referenceNumber");
