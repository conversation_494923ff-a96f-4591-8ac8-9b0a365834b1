import { LogsService } from './logs.service';
import { HTTP_Method, Logs } from '@prisma/client';
export declare class LogsController {
    private readonly logsService;
    constructor(logsService: LogsService);
    getLogs(page: number, pageSize: number, action?: string, sourceUrl?: string, sourceIpAddress?: string, sourceOS?: string, sourceBrowser?: string, url?: string, method?: HTTP_Method, userId?: string, sortBy?: keyof Logs): Promise<import("../utils/@types").ApiResponse<any>>;
    search(query: string): Promise<import("../utils/@types").ApiResponse<{
        data: ({
            user: {
                name: string;
                email: string;
                telephone: string;
            };
        } & {
            id: string;
            createdAt: Date;
            url: string;
            action: string | null;
            sourceUrl: string;
            sourceIpAddress: string;
            sourceOS: string;
            sourceBrowser: string;
            method: import("@prisma/client").$Enums.HTTP_Method;
            userId: string | null;
        })[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>>;
    getUserLogs(userId: string, page: number, pageSize: number, sortBy?: keyof Logs): Promise<import("../utils/@types").ApiResponse<any>>;
}
