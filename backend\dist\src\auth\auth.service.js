"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthenticationService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const prisma_service_1 = require("../prisma/prisma.service");
const _types_1 = require("../utils/@types");
const client_1 = require("@prisma/client");
const bcrypt = require("bcrypt");
const mail_service_1 = require("../utils/mail/mail.service");
const useragent = require("express-useragent");
const sms_service_1 = require("../utils/sms/sms.service");
const env_1 = require("../utils/env");
const speakeasy = require("speakeasy");
const qrcode = require("qrcode");
let AuthenticationService = class AuthenticationService {
    constructor(prisma, jwtService, mailService, smsService) {
        this.prisma = prisma;
        this.jwtService = jwtService;
        this.mailService = mailService;
        this.smsService = smsService;
    }
    generateVerificationCode() {
        return Math.floor(100000 + Math.random() * 900000).toString();
    }
    async sendVerificationCode(user, type) {
        const verificationCode = this.generateVerificationCode();
        await this.prisma.user.update({
            where: { id: user.id },
            data: {
                resetToken: verificationCode,
                resetTokenExpiry: new Date(Date.now() + 45 * 60 * 1000)
            }
        });
        this.mailService.sendMail(type === 'ACTIVATION' ? mail_service_1.MailType.VERIFY_ACCOUNT : mail_service_1.MailType.RESET_PASSWORD, {
            name: user.name,
            to: user.email,
            subject: type === 'ACTIVATION'
                ? 'Activate Your Account | AAID Portal'
                : 'Reset Your Password | AAID Portal',
            values: {
                name: user.name,
                code: verificationCode,
                resetLink: `${env_1.env.FE_URL}/portal/auth/${type === 'ACTIVATION' ? 'activate' : 'confirm-reset-password'}?token=${verificationCode}`,
            },
        });
        await this.smsService.sendSMS(user.telephone, `Hello ${user.name},\nYour ${type === 'ACTIVATION' ? 'account activation invitation code' : 'password reset '} code is: ${verificationCode}`);
        return new _types_1.ApiResponse(true, 'Verification code sent via email and SMS', null);
    }
    async login(identifier, password, req) {
        const user = await this.prisma.user.findFirst({
            where: {
                OR: [
                    { email: identifier },
                    { telephone: identifier }
                ]
            },
        });
        if (!user) {
            throw new common_1.NotAcceptableException('Invalid credentials');
        }
        if (user.status !== client_1.AccountStatus.ACTIVE) {
            throw new common_1.NotAcceptableException('Account is not active');
        }
        const passwordExpired = user.passwordExpiryDate < new Date();
        if (passwordExpired) {
            throw new common_1.NotAcceptableException('Password has expired. Please reset your password.');
        }
        const passwordsMatch = await bcrypt.compare(password, user.password);
        if (!passwordsMatch) {
            throw new common_1.NotAcceptableException('Invalid credentials');
        }
        const requireVerification = user.multiFactorEnabled;
        delete user.password;
        delete user.resetToken;
        if (requireVerification) {
            return new _types_1.ApiResponse(true, '2FA REQUIRED', {
                user
            });
        }
        const token = this.generateLoginToken(user);
        await this.logLoginAttempt(user, req);
        return new _types_1.ApiResponse(true, 'Login successful', {
            token,
            user,
            requireVerification: false
        });
    }
    async verifyMultiFactorCode(userId, code) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        if (user.multiFactorEnabled && user.multiFactorSecret) {
            const isValidToken = this.verifyTwoFactorToken(user.multiFactorSecret, code);
            if (!isValidToken) {
                throw new common_1.NotAcceptableException('Invalid 2FA token');
            }
        }
        else {
            if (user.resetToken !== code ||
                (user.resetTokenExpiry && user.resetTokenExpiry < new Date())) {
                throw new common_1.NotAcceptableException('Invalid or expired verification code');
            }
            await this.prisma.user.update({
                where: { id: user.id },
                data: {
                    resetToken: null,
                    resetTokenExpiry: null
                }
            });
        }
        const token = this.generateLoginToken(user);
        delete user.password;
        delete user.resetToken;
        return new _types_1.ApiResponse(true, 'Multi-factor authentication successful', {
            token,
            user
        });
    }
    generateTwoFactorSecret() {
        const secret = speakeasy.generateSecret({
            name: "AAID Portal"
        });
        return {
            secret: secret.base32,
            otpAuthUrl: secret.otpauth_url
        };
    }
    async generateQRCode(otpAuthUrl) {
        return qrcode.toDataURL(otpAuthUrl);
    }
    generateLoginToken(user) {
        return this.jwtService.sign({
            id: user.id,
            email: user.email,
            name: user.name,
            telephone: user.telephone,
            profilePicture: user.profilePicture,
            role: user.role,
            expiresIn: '45m'
        });
    }
    verifyTwoFactorToken(secret, token) {
        return speakeasy.totp.verify({
            secret: secret,
            encoding: 'base32',
            token: token,
            window: 1
        });
    }
    async logLoginAttempt(user, req) {
        const { browser, os } = useragent.parse(req.headers['user-agent']);
        this.mailService.sendMail(mail_service_1.MailType.LOGIN, {
            name: user.name,
            to: user.email,
            subject: 'New Login | AAID Notification',
            values: {
                name: user.name,
                time: new Date().toLocaleString(),
                device: `${os} / ${browser}`
            }
        });
    }
};
exports.AuthenticationService = AuthenticationService;
exports.AuthenticationService = AuthenticationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        jwt_1.JwtService,
        mail_service_1.MailService,
        sms_service_1.SmsService])
], AuthenticationService);
//# sourceMappingURL=auth.service.js.map