import { Logger, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { json, urlencoded } from 'express';
import * as morgan from 'morgan';
import { AppModule } from './app.module';
import { UnauthorizedExceptionFilter } from './utils/filters/unauthorized.filter';
import { NotFoundExceptionFilter } from './utils/filters/notfound.filter';
import { AuthGuard } from './auth/auth.guard';
import { env } from './utils/env';

const corsConfig = {
  origin: '*',
  methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
  optionsSuccessStatus: 200,
};

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.use(urlencoded());
  app.use(json({ limit: '50mb' }));
  app.setGlobalPrefix('api/v1');
  // app.useGlobalFilters(
  //   new UnauthorizedExceptionFilter(),
  //   new NotFoundExceptionFilter(),
  // );

  const config = new DocumentBuilder()
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        in: 'Header',
        bearerFormat: 'JWT',
      },
      'Bearer',
    )
    .setTitle('AAID Notification System API')
    .setDescription('API Endpoints for v1 AAID Notification System')
    .setVersion('1.0')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api-docs', app, document);

  app.enableCors(corsConfig);
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
    }),
  );

  app.useGlobalGuards(new AuthGuard());
  app.use(morgan('dev'));
  await app.listen(env.PORT);
  Logger.log(
    `Bootstrap  Server running on port ${env.PORT}`,
    'NestApplication',
  );
}
bootstrap();
