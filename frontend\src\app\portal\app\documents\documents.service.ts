import { Injectable } from "@angular/core";
import { AxiosService } from "../../../util/axios/axios.service";
import { Document, PaginatedResponse } from "../../../util/@types";

@Injectable({
    providedIn: 'root'
})
export class DocumentsService {

    constructor(
        private axiosService: AxiosService
    ) { }

    async getDocuments(page: number = 1, limit: number = 10): Promise<PaginatedResponse<Document>> {
        try {
            const response = await this.axiosService.axios.get('/document', {
                params: { page, limit }
            });
            return response.data as PaginatedResponse<Document>;
        } catch (error) {
            throw error;
        }
    }

    async getDocument(id: string): Promise<Document> {
        try {
            const response = await this.axiosService.axios.get(`/document/${id}`);
            return response.data.data as Document;
        } catch (error) {
            throw error;
        }
    }

    async createDocument(documentData: Document): Promise<Document> {
        try {
            const response = await this.axiosService.axios.post('/document', documentData);
            return response.data.data as Document;
        } catch (error) {
            throw error;
        }
    }

    async deleteDocument(id: string): Promise<Document> {
        try {
            const response = await this.axiosService.axios.delete(`/document/${id}`)
            return response.data.data as Document
        } catch (error) {
            throw error
        }
    }

    async updateDocument(id: string, DocumentData: Partial<Document>): Promise<Document> {
        try {
            const response = await this.axiosService.axios.put(`/document/${id}`, DocumentData);
            return response.data.data as Document;
        } catch (error) {
            throw error;
        }
    }
}