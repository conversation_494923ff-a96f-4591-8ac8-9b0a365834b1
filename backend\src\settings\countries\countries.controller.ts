import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { CountriesService } from './countries.service';

@ApiTags('REST Countries')
@Controller('countries')
export class CountriesController {

    constructor(
        private countriesService: CountriesService
    ){ }

    @Get()
    async getAll() {
        return this.countriesService.getCountries()
    }
}
