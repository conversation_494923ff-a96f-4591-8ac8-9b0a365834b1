import { BadRequestException, Injectable, InternalServerErrorException, NotAcceptableException, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateOccurrenceDto, UpdateOccurrenceDto } from './dto';
import { ApiResponse } from 'src/utils/@types';
import { MailService, MailType } from 'src/utils/mail/mail.service';
import { OccurrenceType, Role } from '@prisma/client';

@Injectable()
export class OccurrenceService {
  constructor(private readonly prisma: PrismaService, private mailService: MailService) { }

  async createOccurrence(data: CreateOccurrenceDto): Promise<ApiResponse<any>> {
    try {
      const occurrence = await this.prisma.occurrence.create({
        data: {
          ...data,
          involvedAircraft: {
            create: data.involvedAircraft.map((aircraft) => ({
              ...aircraft,
            })),
          },
        },
        include: {
          involvedAircraft: true,
        },
      });

      //email
      const investigators = await this.prisma.user.findMany({
        where: {
          role: Role.INVESTIGATOR
        }
      })

      investigators.forEach((investigator) => {
        this.mailService.sendMail(MailType.OCCURENCE_SUBMIT, {
          name: investigator.name,
          to: investigator.email,
          subject: "New occurence | AAID Notification",
          values: {
            reporterName: occurrence.reporterName,
            reporterEmail: occurrence.reporterEmail,
            occurrenceTime: occurrence.occurrenceTime?.toLocaleString() || 'N/A',
            occurrenceLocation: occurrence.occurrenceLocation || 'N/A'
          }
        })
      })

      return new ApiResponse(true, 'Occurrence successfully created.', occurrence);
    } catch (error) {
      console.log(error)
      throw new InternalServerErrorException(error.message)
    }
  }

  async getOccurrences(): Promise<ApiResponse<any>> {
    try {
      const occurrences = await this.prisma.occurrence.findMany({
        orderBy: {
          createdAt: "desc"
        },
        include: {
          involvedAircraft: true,
          occurrenceCategory: true
        }
      })
      return new ApiResponse(true, 'Occurrences', occurrences);
    } catch (error) {
      throw new InternalServerErrorException(error.message)
    }
  }

  async getOccurrencesById(id: string): Promise<ApiResponse<any>> {
    const occurence = await this.prisma.occurrence.findUnique({
      where: {
        id
      },
      include: {
        involvedAircraft: true,
        occurrenceCategory: true
      }
    })

    if (!occurence) {
      throw new NotFoundException("Occurrence not found")
    }
    return new ApiResponse(true, 'Occurrences', occurence);
  }

  async updateOccurrence(id: string, data: UpdateOccurrenceDto) {
    const {
      involvedAircraft,
      occurrenceCategory_id,
      ...occurrenceData
    } = data;
  
    const existingOccurrence = await this.prisma.occurrence.findUnique({
      where: { id },
    });
  
    if (!existingOccurrence) {
      throw new NotFoundException(`Occurrence with ID ${id} not found`);
    }
  
    // Begin transaction for complex update
    return this.prisma.$transaction(async (prisma) => {
      // Update main occurrence record
      const updatedOccurrence = await prisma.occurrence.update({
        where: { id },
        data: {
          ...occurrenceData,
          ...(occurrenceCategory_id
            ? {
                occurrenceCategory: {
                  connect: { id: occurrenceCategory_id },
                },
              }
            : {}),
        },
      });
  
      if (involvedAircraft) {

        await this.prisma.aircraft.deleteMany({
          where: {
            occurrenceId: existingOccurrence.id
          }
        })

        await Promise.all(
          involvedAircraft.map(async (item) => {
              
              await this.prisma.aircraft.create({
                data: {
                  model: item.model,
                  crewInjured: item.crewInjured,
                  crewOnBoard: item.crewOnBoard,
                  crewPerished: item.crewPerished,
                  passengersInjured: item.passengersInjured,
                  passengersOnBoard: item.passengersOnBoard,
                  passengersPerished: item.passengersPerished,
                  intendedLandingDateTime: item.intendedLandingDateTime,
                  intendedLandingPoint: item.intendedLandingPoint,
                  lastDeparturePoint: item.lastDeparturePoint,
                  manufacturer: item.manufacturer,
                  operator: item.operator,
                  operatorNationality: item.operatorNationality,
                  registrationMark: item.registrationMark,
                  occurrence: { connect: { id } },
                },
              });
          }),
        );
      }      
  
      return updatedOccurrence;
    });
  }
  

  async deleteOccurrence(id: string): Promise<ApiResponse<any>> {

    const occurence = await this.prisma.occurrence.findUnique({
      where: {
        id
      }
    })

    if (!occurence) {
      throw new NotFoundException("Occurrence not found")
    }

    const deleted = await this.prisma.occurrence.delete({
      where: {
        id
      }
    })

    return new ApiResponse(true, 'Occurrence deleted', deleted);

  }

  async generateReferenceNumber(id: string) {
    const occurrence = await this.prisma.occurrence.findUnique({
      where: { id },
      include: { occurrenceCategory: true },
    });
  
    if (!occurrence) {
      throw new NotFoundException('Occurrence not found');
    }

    if (!occurrence.type) {
      throw new NotAcceptableException('The occurrence must have a type first');
    }
  
    if (occurrence.referenceNumber) {
      await this.prisma.occurrence.update({
        where: { id },
        data: { referenceNumber: null },
      });
    }
  
    const lastNumber = await this.prisma.occurrence.count({
      where: { referenceNumber: { not: null } },
    });
  
    const { createdAt, type } = occurrence;
    const year = createdAt.getFullYear();
  
    const typeMapping: Record<string, string> = {
      [OccurrenceType.ACCIDENT]: 'ACCID',
      [OccurrenceType.MAJOR_ACCIDENT]: 'ACCID',
      [OccurrenceType.INCIDENT]: 'INCID',
      [OccurrenceType.INCIDENT_TO_BE_INVESTIGATED]: 'INCID',
      [OccurrenceType.SERIOUS_INCIDENT]: 'SINCID',
    };
  
    const referenceType = typeMapping[type] || 'OCC';
  
    const newRefNumber = `${year}-${referenceType}-${String(lastNumber + 1).padStart(4, '0')}`;
  
    const updatedOccurrence = await this.prisma.occurrence.update({
      where: { id },
      data: { referenceNumber: newRefNumber },
    });
  
    return new ApiResponse(true, 'Reference number generated successfully', updatedOccurrence);
  }

  async searchOccurrence(query: string): Promise<ApiResponse<any>> {
    const results = await this.prisma.occurrence.findMany({
      where: {
        OR: [
          {
            referenceNumber: {
              contains: query,
              mode: 'insensitive',
            },
          },
          {
            reporterName: {
              contains: query,
              mode: 'insensitive',
            },
          },
          {
            reporterEmail: {
              contains: query,
              mode: 'insensitive',
            },
          },
          {
            occurrenceLocation: {
              contains: query,
              mode: 'insensitive',
            },
          },
          {
            occurrenceCategory: {
              category: {
                contains: query,
                mode: 'insensitive',
              },
              description: {
                contains: query,
                mode: 'insensitive',
              },
            }
          }
        ],
      },
      include: {
        occurrenceCategory: true
      }
    });

    console.log(`results for (): ${query}` + results.length)

    return new ApiResponse(true, 'Search results', {data: results, count: results.length});
  }
  
}