{"version": 3, "file": "logs.middleware.js", "sourceRoot": "", "sources": ["../../../src/logs/logs.middleware.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4D;AAE5D,6DAAyD;AACzD,+CAAsC;AACtC,qCAAyC;AACzC,sCAAoC;AAG7B,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAC3B,YACU,MAAqB,EACrB,UAAsB;QADtB,WAAM,GAAN,MAAM,CAAe;QACrB,eAAU,GAAV,UAAU,CAAY;IAC/B,CAAC;IAEM,eAAe,CAAC,MAAc,EAAE,GAAW;QACjD,MAAM,SAAS,GAAG;YAChB,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,EAAE,YAAY,EAAE;YAClE,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,iBAAiB,EAAE,MAAM,EAAE,kBAAkB,EAAE;YAE7E,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,0BAA0B,EAAE,MAAM,EAAE,mBAAmB,EAAE;YACvF,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,mBAAmB,EAAE,MAAM,EAAE,kBAAkB,EAAE;YAC9E,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,mBAAmB,EAAE,MAAM,EAAE,mBAAmB,EAAE;YAC/E,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,mBAAmB,EAAE,MAAM,EAAE,mBAAmB,EAAE;YAElF,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,sBAAsB,EAAE,MAAM,EAAE,qBAAqB,EAAE;YAErF,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,EAAE,mBAAmB,EAAE;YAE3E,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,iBAAiB,EAAE;YACjE,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,mBAAmB,EAAE;YACpE,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE;YAC9D,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE;YAChE,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE;YAGjE,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE;YAC3D,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE;YAC3D,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE;YAC7D,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE;YAC/D,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE;YAGhE,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,eAAe,EAAE;YAClE,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE;YAC9D,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,eAAe,EAAE;YACpE,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,eAAe,EAAE;YACpE,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,gBAAgB,EAAE,MAAM,EAAE,eAAe,EAAE;YAC1E,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,iBAAiB,EAAE,MAAM,EAAE,gBAAgB,EAAE;YAC5E,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,gBAAgB,EAAE,MAAM,EAAE,eAAe,EAAE;SAI3E,CAAC;QAEF,MAAM,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAC7C,MAAM,KAAK,OAAO,CAAC,MAAM,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAC9D,CAAC;QAEF,IAAI,aAAa,EAAE,CAAC;YAClB,OAAO,aAAa,CAAC,MAAM,CAAC;QAC9B,CAAC;QAED,OAAO,GAAG,MAAM,IAAI,GAAG,EAAE,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QACvD,MAAM,SAAS,GAAG,IAAI,uBAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;QAC1D,MAAM,WAAW,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;QAC3C,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;QAEjC,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;QAE/C,IAAI,EAAE,GAAG,IAAI,CAAA;QAEb,IAAG,CAAC;YACF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE;gBACvD,MAAM,EAAE,SAAG,CAAC,UAAU;aACvB,CAAC,CAAC;YACH,EAAE,GAAG,OAAO,CAAC,EAAE,CAAA;QACjB,CAAC;QAAA,OAAM,KAAK,EAAC,CAAC;QAEd,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC5B,IAAI,EAAE;oBACJ,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,WAAW,CAAC;oBACzD,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,QAAQ;oBAC9D,eAAe,EACb,GAAG,CAAC,MAAM,CAAC,aAAa;wBACxB,GAAG,CAAC,EAAE;wBACN,GAAG,CAAC,UAAU,CAAC,aAAa;wBAC5B,SAAS;oBACX,QAAQ,EAAE,GAAG,MAAM,CAAC,IAAI,IAAI,SAAS,IAAI,MAAM,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE;oBACtE,aAAa,EAAE,GAAG,WAAW,CAAC,IAAI,IAAI,SAAS,IAAI,WAAW,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE;oBACrF,GAAG,EAAE,GAAG,CAAC,WAAW;oBACpB,MAAM,EAAE,GAAG,CAAC,MAAa;oBACzB,MAAM,EAAE,EAAE,IAAI,IAAI;iBACnB;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAEO,sBAAsB,CAAC,OAAgB;QAC7C,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACtE,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;IAC/C,CAAC;CACF,CAAA;AAvGY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;qCAGO,8BAAa;QACT,gBAAU;GAHrB,gBAAgB,CAuG5B"}