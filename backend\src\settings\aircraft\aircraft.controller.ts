import { Controller, Get, Post, Body, Query, Param, Patch, Delete } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { AircraftService } from './aircraft.service';
import { CreateAircraftDto, UpdateAircraftDto, SearchAircraftDto } from './dto';
import { ApiResponse } from 'src/utils/@types';
import { SearchDto } from 'src/users/dto';

@ApiTags('Aircraft Settings')
@Controller('settings/aircraft')
export class AircraftController {
    constructor(private readonly aircraftService: AircraftService) { }

    @Post()
    @ApiOperation({ summary: 'Create a new aircraft' })
    create(@Body() createAircraftDto: CreateAircraftDto) {
        return this.aircraftService.create(createAircraftDto);
    }

    @Get()
    @ApiOperation({ summary: 'get all aircrafts [paginated]' })
    async getAll(
        @Query('page') page: string,
        @Query('limit') limit: string,
    ): Promise<ApiResponse<any>> {
        const pageNumber = parseInt(page, 10) || 1; // Default to page 1
        const limitNumber = parseInt(limit, 10) || 10; // Default to 10 items per page
        return await this.aircraftService.findAll(pageNumber, limitNumber);
    } Ï

    @Get('id/:id')
    @ApiOperation({ summary: 'Get a single aircraft by ID' })
    findOne(@Param('id') id: string) {
        return this.aircraftService.findOne(id);
    }

    @Patch(':id')
    @ApiOperation({ summary: 'Update an aircraft' })
    update(@Param('id') id: string, @Body() updateAircraftDto: UpdateAircraftDto) {
        return this.aircraftService.update(id, updateAircraftDto);
    }

    @Delete(':id')
    @ApiOperation({ summary: 'Delete an aircraft' })
    remove(@Param('id') id: string) {
        return this.aircraftService.remove(id);
    }

    @Get('search')
    @ApiOperation({ summary: 'Search for aircrafts' })
    search(@Query() searchDto: SearchDto) {
        const { query, page, limit } = searchDto;
        return this.aircraftService.search(query, page, limit);
    }

    @Get('manufacturers')
    searchManufacturers(@Query('q') query: string) {
        return this.aircraftService.searchManufacturers(query);
    }

    @Get('models')
    searchModels(
        @Query('q') query: string,
        @Query('manufacturer') manufacturer: string
    ) {
        return this.aircraftService.searchModels(query, manufacturer);
    }
}
