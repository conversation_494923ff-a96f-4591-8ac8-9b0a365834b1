"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
const _types_1 = require("../utils/@types");
const mail_service_1 = require("../utils/mail/mail.service");
const env_1 = require("../utils/env");
let ReportsService = class ReportsService {
    constructor(prisma, mailService) {
        this.prisma = prisma;
        this.mailService = mailService;
    }
    async create(data) {
        const existingReport = await this.prisma.report.findUnique({
            where: { referenceNumber: data.referenceNumber }
        });
        if (existingReport) {
            throw new common_1.ConflictException(`Report with reference number ${data.referenceNumber} already exists.`);
        }
        const report = await this.prisma.report.create({
            data: {
                addressedTo: data.addressedTo,
                referenceNumber: data.referenceNumber,
                stateOfDesign: data.stateOfDesign,
                stateOfRegistry: data.stateOfRegistry,
                stateOfManufacturer: data.stateOfManufacturer,
                stateOfOperator: data.stateOfOperator,
                occurrenceType: data.occurrenceType,
                aircraftInfo: data.aircraftInfo,
                groundPeopleInjured: data.groundPeopleInjured,
                groundPeoplePerished: data.groundPeoplePerished,
                occurrenceDate: data.occurrenceDate,
                occurrenceTimeUTC: data.occurrenceTimeUTC,
                occurrenceTimeLocal: data.occurrenceTimeLocal,
                position: data.position,
                latitude: data.latitude,
                longitude: data.longitude,
                occurrenceDescription: data.occurrenceDescription,
                damageExtent: data.damageExtent,
                investigationDelegation: data.investigationDelegation,
                investigationExtent: data.investigationExtent,
                areaCharacteristics: data.areaCharacteristics,
                accessRequirements: data.accessRequirements,
                originatingAuthority: data.originatingAuthority ?? 'AAID-Rwanda',
                investigatorName: data.investigatorName,
                investigatorEmail: data.investigatorEmail,
                investigatorMobile: data.investigatorMobile,
                dangerousGoodsPresent: data.dangerousGoodsPresent,
                dangerousGoodsDescription: data.dangerousGoodsDescription
            }
        });
        return new _types_1.ApiResponse(true, 'Report created successfully', report);
    }
    async findAll() {
        const data = await this.prisma.report.findMany({
            orderBy: {
                createdAt: 'desc'
            }
        });
        return new _types_1.ApiResponse(true, 'Reports fetched successfully', data);
    }
    async findOne(id) {
        try {
            const data = await this.prisma.report.findUnique({ where: { id } });
            if (!data) {
                throw new common_1.NotFoundException(`Report with ID ${id} not found.`);
            }
            return new _types_1.ApiResponse(true, 'Report fetched successfully', data);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to fetch report');
        }
    }
    async update(id, updateReportDto) {
        try {
            const existingReport = await this.prisma.report.findUnique({ where: { id } });
            if (!existingReport) {
                throw new common_1.NotFoundException(`Report with ID ${id} not found.`);
            }
            const data = await this.prisma.report.update({
                where: { id },
                data: {
                    addressedTo: updateReportDto.addressedTo,
                    referenceNumber: updateReportDto.referenceNumber,
                    stateOfDesign: updateReportDto.stateOfDesign,
                    stateOfRegistry: updateReportDto.stateOfRegistry,
                    stateOfManufacturer: updateReportDto.stateOfManufacturer,
                    stateOfOperator: updateReportDto.stateOfOperator,
                    occurrenceType: updateReportDto.occurrenceType,
                    aircraftInfo: updateReportDto.aircraftInfo,
                    groundPeopleInjured: updateReportDto.groundPeopleInjured,
                    groundPeoplePerished: updateReportDto.groundPeoplePerished,
                    occurrenceDate: updateReportDto.occurrenceDate,
                    occurrenceTimeUTC: updateReportDto.occurrenceTimeUTC,
                    occurrenceTimeLocal: updateReportDto.occurrenceTimeLocal,
                    position: updateReportDto.position,
                    latitude: updateReportDto.latitude,
                    longitude: updateReportDto.longitude,
                    occurrenceDescription: updateReportDto.occurrenceDescription,
                    damageExtent: updateReportDto.damageExtent,
                    investigationExtent: updateReportDto.investigationExtent,
                    investigationDelegation: updateReportDto.investigationDelegation,
                    areaCharacteristics: updateReportDto.areaCharacteristics,
                    accessRequirements: updateReportDto.accessRequirements,
                    originatingAuthority: updateReportDto.originatingAuthority ?? 'AAID-Rwanda',
                    investigatorName: updateReportDto.investigatorName,
                    investigatorEmail: updateReportDto.investigatorEmail,
                    investigatorMobile: updateReportDto.investigatorMobile,
                    dangerousGoodsPresent: updateReportDto.dangerousGoodsPresent,
                    dangerousGoodsDescription: updateReportDto.dangerousGoodsDescription ?
                        status : existingReport.status !== null ? client_1.ReportStatus.PENDING : null
                }
            });
            return new _types_1.ApiResponse(true, 'Report updated successfully', data);
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new common_1.ConflictException('A report with this reference number already exists.');
                }
            }
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to update report');
        }
    }
    async remove(id) {
        try {
            const existingReport = await this.prisma.report.findUnique({ where: { id } });
            if (!existingReport) {
                throw new common_1.NotFoundException(`Report with ID ${id} not found.`);
            }
            const data = await this.prisma.report.delete({ where: { id } });
            return new _types_1.ApiResponse(true, 'Report deleted successfully', data);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to delete report');
        }
    }
    async search(query) {
        try {
            const searchQuery = query?.trim();
            if (!searchQuery) {
                return await this.findAll();
            }
            const data = await this.prisma.report.findMany({
                where: {
                    OR: [
                        { referenceNumber: { contains: searchQuery, mode: 'insensitive' } },
                        { stateOfDesign: { contains: searchQuery, mode: 'insensitive' } },
                        { stateOfRegistry: { contains: searchQuery, mode: 'insensitive' } },
                        { stateOfManufacturer: { contains: searchQuery, mode: 'insensitive' } },
                        { stateOfOperator: { contains: searchQuery, mode: 'insensitive' } },
                        { position: { contains: searchQuery, mode: 'insensitive' } },
                        { occurrenceDescription: { contains: searchQuery, mode: 'insensitive' } },
                        { damageExtent: { contains: searchQuery, mode: 'insensitive' } },
                        { areaCharacteristics: { contains: searchQuery, mode: 'insensitive' } },
                        { investigatorName: { contains: searchQuery, mode: 'insensitive' } },
                        { investigatorEmail: { contains: searchQuery, mode: 'insensitive' } },
                        { originatingAuthority: { contains: searchQuery, mode: 'insensitive' } }
                    ]
                },
                orderBy: {
                    createdAt: 'desc'
                }
            });
            if (data.length === 0) {
                return new _types_1.ApiResponse(true, 'No matching reports found', []);
            }
            return new _types_1.ApiResponse(true, 'Reports fetched successfully', data);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to search reports');
        }
    }
    async submit(id, req) {
        const report = await this.prisma.report.findUnique({
            where: {
                id
            },
        });
        if (!report) {
            throw new common_1.NotFoundException('Report not found');
        }
        if (report.status === client_1.ReportStatus.PENDING) {
            throw new common_1.BadRequestException('Report already submitted');
        }
        if (report.status === client_1.ReportStatus.APPROVED) {
            throw new common_1.BadRequestException('Report already approved');
        }
        const updated = await this.prisma.report.update({
            where: {
                id
            },
            data: {
                status: client_1.ReportStatus.PENDING,
                submittedById: req.user.id
            }
        });
        const _DGs = await this.prisma.user.findMany({
            where: {
                role: client_1.Role.DG
            }
        });
        for (const user of _DGs) {
            this.mailService.sendMail(mail_service_1.MailType.APPROVAL_REQUEST, {
                to: user.email,
                name: user.name,
                subject: 'Request for approval | AAID Notification',
                values: {
                    name: user.name,
                    by: req.user.name,
                    date: `${new Date().toLocaleString()}`,
                    refNo: updated.referenceNumber,
                    link: `${env_1.env.FE_URL}/dg/reports/edit/${report.id}`
                }
            });
        }
        return new _types_1.ApiResponse(true, '', updated);
    }
    async approve(id, data, req) {
        const report = await this.prisma.report.findUnique({
            where: {
                id
            },
            include: {
                submittedBy: true
            }
        });
        if (!report) {
            throw new common_1.NotFoundException('Report not found');
        }
        if (report.status === null) {
            throw new common_1.BadRequestException('Report not submitted yet');
        }
        if (report.status === client_1.ReportStatus.APPROVED) {
            throw new common_1.BadRequestException('Report already approved');
        }
        const user = await this.prisma.user.findUnique({
            where: {
                id: req.user.id
            }
        });
        const approved = await this.prisma.report.update({
            where: {
                id
            },
            data: {
                status: client_1.ReportStatus.APPROVED,
                comment: data?.comment ?? null,
                approvedById: user.id ?? null
            },
        });
        this.mailService.sendMail(mail_service_1.MailType.REPORT_APPROVED, {
            to: report.submittedBy.email,
            name: report.submittedBy.name,
            subject: 'Report approved | AAID Notification',
            values: {
                name: report.submittedBy.name,
                refNo: report.referenceNumber,
                by: `${user.name} (${user.role.split('_').join(' ')})`,
                comment: data.comment ?? '',
                link: `${env_1.env.FE_URL}/portal/reports/edit/${report.id}`
            }
        });
        return new _types_1.ApiResponse(true, 'approved', approved);
    }
    async revert(id, data, req) {
        const report = await this.prisma.report.findUnique({
            where: {
                id
            },
            include: {
                submittedBy: true
            }
        });
        if (!report) {
            throw new common_1.NotFoundException('Report not found');
        }
        if (report.status === null) {
            throw new common_1.BadRequestException('Report not submitted yet');
        }
        if (report.status === client_1.ReportStatus.REVERTED) {
            throw new common_1.BadRequestException('Report already reverted');
        }
        if (report.status === client_1.ReportStatus.APPROVED) {
            throw new common_1.BadRequestException('Report already approved');
        }
        const user = await this.prisma.user.findUnique({
            where: {
                id: req.user.id
            }
        });
        const reverted = await this.prisma.report.update({
            where: {
                id
            },
            data: {
                status: client_1.ReportStatus.REVERTED,
                comment: data?.comment ?? null,
                approvedById: user.id ?? null
            }
        });
        this.mailService.sendMail(mail_service_1.MailType.REPORT_REVERTED, {
            to: report.submittedBy.email,
            name: report.submittedBy.name,
            subject: 'Report reverted | AAID Notification',
            values: {
                name: report.submittedBy.name,
                refNo: report.referenceNumber,
                by: `${user.name} (${user.role.split('_').join(' ')})`,
                comment: data.comment ?? ''
            }
        });
        return new _types_1.ApiResponse(true, 'reverted', reverted);
    }
};
exports.ReportsService = ReportsService;
exports.ReportsService = ReportsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        mail_service_1.MailService])
], ReportsService);
//# sourceMappingURL=reports.service.js.map