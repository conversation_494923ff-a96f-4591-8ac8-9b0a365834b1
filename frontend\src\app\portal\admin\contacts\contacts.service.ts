import { Injectable } from "@angular/core";
import { Contact, PaginatedResponse } from "../../../util/@types";
import { AxiosService } from "../../../util/axios/axios.service";

@Injectable({
    providedIn: 'root'
})
export class ContactsService{
    constructor(private axiosService: AxiosService) {}

    async getContacts(page: number = 1, limit: number = 10): Promise<PaginatedResponse<Contact>> {
      try {
        const response = await this.axiosService.axios.get('/settings/contacts', {
          params: { page, limit }
        });
        return response.data as PaginatedResponse<Contact>;
      } catch (error) {
        throw error;
      }
    }
  
    async getContact(id: string): Promise<Contact> {
      try {
        const response = await this.axiosService.axios.get(`/settings/contacts/${id}`);
        return response.data.data as Contact;
      } catch (error) {
        throw error;
      }
    }
  
    async createContact(name: string, description: string, email: string, telephone: string): Promise<Contact>{
      try {
        const response = await this.axiosService.axios.post('/settings/contacts', {
          name, 
          description,
          email,
          telephone
        })
        return response.data.data as Contact
      } catch (error) {
        throw error
      }
    }
  
    async updateContact(id: string, name: string, description: string, email: string, telephone: string): Promise<Contact>{
      try {
        const response = await this.axiosService.axios.patch(`/settings/contacts/${id}`, {
          name,
          description, 
          email,
          telephone
        })
        return response.data.data as Contact
      } catch (error) {
        throw error
      }
    }
  
    async deleteContact(id: string): Promise<Contact> {
      try {
        const response = await this.axiosService.axios.delete(`/settings/contacts/${id}`)
        return response.data.data as Contact
      } catch (error) {
        throw error
      }
    }
}