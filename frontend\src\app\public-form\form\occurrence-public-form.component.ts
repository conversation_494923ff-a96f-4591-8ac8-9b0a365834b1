import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import {
  FlightRules,
  OperationType,
  MeteologicalCondition,
  Aircraft,
  FlighPhase,
} from '../models/enums';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DialogModule } from 'primeng/dialog';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { RippleModule } from 'primeng/ripple';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { OccurrencePublicFormService } from './occurrence-public-form.service';
import { HttpClientModule } from '@angular/common/http';
import { Router } from '@angular/router';
import { AircraftsService } from '../../portal/admin/aicrafts/aircrafts.service';
import { AutoComplete, AutoCompleteModule } from 'primeng/autocomplete';
import { DropdownModule } from 'primeng/dropdown';

@Component({
  selector: 'app-occurrence-public-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    DialogModule,
    ToastModule,
    ConfirmDialogModule,
    ButtonModule,
    RippleModule,
    ProgressSpinnerModule,
    AutoCompleteModule,
    DropdownModule
  ],
  providers: [ConfirmationService, MessageService],
  templateUrl: './occurrence-public-form.component.html',
})
export class OccurrencePublicFormComponent implements OnInit {
  // Form steps
  currentStep = 1;
  totalSteps = 4;

  // Forms
  reporterForm!: FormGroup;
  aircraftForm!: FormGroup;
  occurrenceDetailsForm!: FormGroup;
  summaryForm!: FormGroup;

  loading = false;

  // Dialog and Aircraft Management
  aircraftDialog = false;
  submitted = false;
  editMode = false;
  selectedIndex = -1;
  currentAircraft: Aircraft = {
    model: '',
    manufacturer: '',
    operator: '',
    registrationMark: '',
    operatorNationality: '',
    intendedLandingDateTime: undefined,
    intendedLandingPoint: '',
    lastDeparturePoint: '',
    crewOnBoard: 0,
    crewInjured: 0,
    crewPerished: 0,
    passengersOnBoard: 0,
    passengersInjured: 0,
    passengersPerished: 0,
  };

  // Enum Options
  flightRulesOptions = Object.values(FlightRules);
  operationTypeOptions = Object.entries(OperationType).map(([key, value]) => ({ key, value }));;
  meteologicalConditionOptions = Object.values(MeteologicalCondition);
  flightPhaseOptions = Object.entries(FlighPhase).map(([key, value]) => ({ key, value }));

  filteredManufacturers: string[] = [];
  filteredModels: string[] = [];

  constructor(
    private fb: FormBuilder,
    private confirmationService: ConfirmationService,
    private messageService: MessageService,
    private occurrenceService: OccurrencePublicFormService,
    private router: Router,
    private aircraftService: AircraftsService
  ) { }

  ngOnInit() {
    this.initializeForms();
  }

  initializeForms() {
    this.reporterForm = this.fb.group({
      reporterName: [
        '',
        [Validators.required, Validators.minLength(2)],
      ],
      reporterEmail: ['', [Validators.required, Validators.email]],
      reporterPhone: [
        '',
        [Validators.required, Validators.pattern(/^\d{10}$/)],
      ],
      pilotInCommandName: [''],
      pilotInCommandEmail: [null, Validators.email],
      pilotInCommandPhone: [''],
    });

    this.aircraftForm = this.fb.group({
      involvedAircraft: this.fb.array([]),
    });

    this.occurrenceDetailsForm = this.fb.group({
      generalWeatherConditions: [''],
      skyCoverage: [''],
      meteologicalCondition: [null, ],
      flightRules: [null, ],
      occurrenceTime: [null, ],
      flightPhase: [null],
      operationType: [null, ],
      latitude: [
        '',
      ],
      longitude: [
        '',
      ],
      occurrenceLocation: ['', ],
      dangerousGoodCarriedOnBoard: [''],
      groundPeopleInjured: [null, [Validators.min(0)]],
      groundPeoplePerished: [null, [Validators.min(0)]],
    });

    this.summaryForm = this.fb.group({
      termsAccepted: [false, Validators.requiredTrue],
    });
  }

  // Step Management
  nextStep() {
    if (this.validateCurrentStep()) {
      this.currentStep++;
      if(this.currentStep == 2){
        this.openNew();
      }
    }
  }

  prevStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  validateCurrentStep(): boolean {
    switch (this.currentStep) {
      case 1:
        return this.reporterForm.valid;
      case 2:
        return this.aircraftForm.valid;
      case 3:
        return this.occurrenceDetailsForm.valid;
      case 4:
        return this.summaryForm.valid;
      default:
        return false;
    }
  }

  validateAllForms(): boolean {
    return (
      this.reporterForm.valid &&
      this.aircraftForm.valid &&
      this.occurrenceDetailsForm.valid &&
      this.summaryForm.valid
    );
  }

  async onSubmit() {
    if (this.validateAllForms()) {
      const formData = {
        ...this.reporterForm.value,
        ...this.aircraftForm.value,
        ...this.occurrenceDetailsForm.value,
      };
  
      try {
        this.loading = true;
        const result = await this.occurrenceService.submitOccurrenceReport(formData);
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Occurrence report submitted successfully.',
        });
        this.resetForms()
        this.router.navigate(['/success']);
      } catch (error) {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to submit occurrence report.',
        });
      } finally {
        this.loading = false;
      }
    }
  }
  

  resetForms() {
    this.reporterForm.reset();
    this.aircraftForm.setControl('involvedAircraft', this.fb.array([]));
    this.occurrenceDetailsForm.reset();
    this.summaryForm.reset();
    this.currentStep = 1; // Reset to the first step
  }

  // Aircraft Management
  createAircraftGroup(): FormGroup {
    return this.fb.group({
      model: ['', Validators.required],
      manufacturer: ['',],
      operator: [''],
      registrationMark: [''],
      operatorNationality: [''],
      intendedLandingDateTime: [''],
      intendedLandingPoint: [''],
      lastDeparturePoint: [''],
      crewOnBoard: [0, [Validators.min(0)]],
      crewInjured: [0, [Validators.min(0)]],
      crewPerished: [0, [Validators.min(0)]],
      passengersOnBoard: [0, [Validators.min(0)]],
      passengersInjured: [0, [Validators.min(0)]],
      passengersPerished: [0, [Validators.min(0)]],
    });
  }

  addAircraft() {
    const aircraftArray = this.aircraftForm.get(
      'involvedAircraft'
    ) as FormArray;
    aircraftArray.push(this.createAircraftGroup());
  }

  openNew() {
    this.currentAircraft = { ...this.createAircraftGroup().value };
    this.aircraftDialog = true;
    this.editMode = false;
  }

  editAircraft(aircraft: any, index: number) {
    this.currentAircraft = { ...aircraft.value };
    this.selectedIndex = index;
    this.aircraftDialog = true;
    this.editMode = true;
  }

  removeAircraft(index: number) {
    const aircraftArray = this.aircraftForm.get(
      'involvedAircraft'
    ) as FormArray;
    this.confirmationService.confirm({
      message: 'Are you sure you want to delete this aircraft?',
      header: 'Confirm',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        // if (aircraftArray.length > 1) {
          aircraftArray.removeAt(index);
          this.messageService.add({
            severity: 'success',
            summary: 'Successful',
            detail: 'Aircraft Deleted',
            life: 3000,
          });
        // }

        console.log(aircraftArray)
      },
    });
  }

  saveAircraft() {
    this.submitted = true;

    if (this.currentAircraft.manufacturer) {
      const aircraftArray = this.aircraftForm.get(
        'involvedAircraft'
      ) as FormArray;

      if (this.editMode) {
        aircraftArray.at(this.selectedIndex).patchValue(this.currentAircraft);
      } else {
        aircraftArray.push(this.fb.group(this.currentAircraft));
      }

      this.messageService.add({
        severity: 'success',
        summary: 'Successful',
        detail: `Aircraft ${this.editMode ? 'Updated' : 'Added'}`,
        life: 3000,
      });

      this.hideDialog();
    }
  }

  hideDialog() {
    this.aircraftDialog = false;
    this.submitted = false;
  }

  get involvedAircraftControls() {
    return (this.aircraftForm.get('involvedAircraft') as FormArray).controls;
  }

  async searchManufacturer(event: any) {
    try {
      const response = await this.aircraftService.searchManufacturers(event.query);
      this.filteredManufacturers = response.data.data;
    } catch (error) {
      this.handleError('Failed to search manufacturers', error);
    }
  }


  async loadModelsByManufacturer(manufacturer: string) {
    try {
      const response = await this.aircraftService.searchModels('', manufacturer);
      this.filteredModels = response.data.data.map((model: any) => ({
        label: model,
        value: model
      }));
    } catch (error) {
      this.handleError('Failed to load models', error);
      this.filteredModels = [];
    }
  }

  async searchModel(event?: any) {
    const manufacturer = this.currentAircraft.manufacturer;
    if (!manufacturer) {
      this.messageService.add({ severity: 'warn', detail: 'Please select manufacturer first' });
      this.filteredModels = [];
      return;
    }

    try {
      const response = await this.aircraftService.searchModels(event?.query || '', manufacturer);
      this.filteredModels = response.data.data.map((model: any) => ({
        label: model,
        value: model
      }));
    } catch (error) {
      this.handleError('Failed to search models', error);
      this.filteredModels = [];
    }
  }

  onManufacturerSelect(event: any) {
    this.currentAircraft.manufacturer = event.value;
    this.aircraftForm.get('manufacturer')?.setValue(event.value);

    console.log(this.currentAircraft.manufacturer)
    
    this.loadModelsByManufacturer(event.value);

  }

  private handleError(message: string, error: any) {
    console.error(error);
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: error.response?.data?.message || error.message || message
    });
  }
}
