# AAID Notification System

A notification system built with backend

## Prerequisites

- Node.js (>= 16.x)
- pnpm, yarn or npm
- PostgreSQL (>= 13.x)

## Getting Started


### 1. Install dependencies
```bash
pnpm install
```

### 3. Configure environment variables
copy `.env.example` to `.env` and edit the file accordingly

### 4. Set up the database
Run Prisma migrations to set up the database schema.
```bash
npx prisma migrate dev
```

### 5. Start the server
```bash
pnpm run start:dev
```

The server will start on `http://localhost:<PORT>`.

## Scripts

- **`pnpm run start`**: Start the application in production mode
- **`pnpm run start:dev`**: Start the application in development mode
- **`pnpm run build`**: Build the application
- **`npx prisma studio`**: Launch Prisma Studio to manage the database

## Project Structure

```
src/
├── modules/          # Feature modules
├── prisma/           # Prisma schema and migrations
├── app.module.ts     # Main application module
└── main.ts           # Application entry point
```

## API Endpoints

Refer to the API documentation (e.g., Swagger) available at `http://localhost:<PORT>/api-docs`.