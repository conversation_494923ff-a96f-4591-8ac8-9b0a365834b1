import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { ApiResponse } from 'src/utils/@types';
import { CreateAircraftDto, SearchAircraftDto, UpdateAircraftDto } from './dto';

@Injectable()
export class AircraftService {
  constructor(private readonly prisma: PrismaService) { }

  async create(data: CreateAircraftDto): Promise<ApiResponse<any>> {
    try {
      const aircraft = await this.prisma.aircraftData.create({ data });
      return new ApiResponse(true, 'Aircraft created successfully', aircraft);
    } catch (error) {
      throw new BadRequestException('Error creating aircraft');
    }
  }

  async findAll(page: number = 1, limit: number = 10): Promise<ApiResponse<any>> {
    const offset = (page - 1) * limit;

    const [aircrafts, total] = await Promise.all([
      this.prisma.aircraftData.findMany({
        skip: offset,
        take: limit,
      }),
      this.prisma.aircraftData.count(),
    ]);

    const totalPages = Math.ceil(total / limit);

    return new ApiResponse(true, 'Aircraft data retrieved successfully', {
      data: aircrafts,
      page,
      totalPages,
      total,
    });
  }


  async findOne(id: string): Promise<ApiResponse<any>> {
    const aircraft = await this.prisma.aircraftData.findUnique({ where: { id } });
    if (!aircraft) throw new NotFoundException('Aircraft not found');
    return new ApiResponse(true, 'Aircraft retrieved successfully', aircraft);
  }

  async update(id: string, data: UpdateAircraftDto): Promise<ApiResponse<any>> {
    try {
      const aircraft = await this.prisma.aircraftData.findUnique({ where: { id } });
      if (!aircraft) throw new NotFoundException('Aircraft not found');

      const updatedAircraft = await this.prisma.aircraftData.update({
        where: { id },
        data,
      });
      return new ApiResponse(true, 'Aircraft updated successfully', updatedAircraft);
    } catch (error) {
      throw new BadRequestException('Error updating aircraft');
    }
  }

  async remove(id: string): Promise<ApiResponse<any>> {
    try {
      const aircraft = await this.prisma.aircraftData.findUnique({ where: { id } });
      if (!aircraft) throw new NotFoundException('Aircraft not found');

      await this.prisma.aircraftData.delete({ where: { id } });
      return new ApiResponse(true, 'Aircraft deleted successfully', null);
    } catch (error) {
      throw new BadRequestException('Error deleting aircraft');
    }
  }

  async search(query: string, page: number, limit: number = 10): Promise<ApiResponse<any>> {
    const results = await this.prisma.aircraftData.findMany({
      where: {
        OR: [
          { modelFullName: { contains: query, mode: 'insensitive' } },
          { manufacturerCode: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } },
          { designator: { contains: query, mode: 'insensitive' } },
          { engineType: { contains: query, mode: 'insensitive' } },
        ]
      },
    })

    const total = await this.prisma.aircraftData.count({
      where: {
        OR: [
          { modelFullName: { contains: query, mode: 'insensitive' } },
          { manufacturerCode: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } },
          { designator: { contains: query, mode: 'insensitive' } },
          { engineType: { contains: query, mode: 'insensitive' } },
        ]
      },
    })

    return new ApiResponse(true, 'Search results retrieved successfully', {
      data: results,
      total,
      page
    });
  }

  async searchManufacturers(query: string) {
    const manufacturers = await this.prisma.aircraftData.findMany({
      where: {
        manufacturerCode: {
          contains: query.toUpperCase(),
          mode: 'insensitive'
        }
      },
      distinct: ['manufacturerCode'],
      select: { manufacturerCode: true },
      take: 10
    });

    return { data: manufacturers.map(m => m.manufacturerCode) };
  }

  async searchModels(query: string, manufacturer: string) {
    const models = await this.prisma.aircraftData.findMany({
      where: {
        AND: [
          {
            manufacturerCode: {
              equals: manufacturer,
              mode: 'insensitive'
            }
          },
          {
            OR: [
              { modelFullName: { contains: query, mode: 'insensitive' } },
              { designator: { contains: query.toUpperCase(), mode: 'insensitive' } }
            ]
          }
        ]
      },
      select: { modelFullName: true, designator: true },
      take: 10
    });

    return {
      data: models.map(m => m.modelFullName)
    };
  }
}
