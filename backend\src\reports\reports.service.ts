import { Injectable, NotFoundException, InternalServerErrorException, ConflictException, BadRequestException} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ApproveDTO, CreateReportDto, UpdateReportDto } from './dto';
import { Prisma, ReportStatus, Role } from '@prisma/client';
import { ApiResponse } from 'src/utils/@types';
import { MailService, MailType } from 'src/utils/mail/mail.service';
import { env } from 'src/utils/env';

@Injectable()
export class ReportsService {
    constructor(
        private prisma: PrismaService,
        private mailService: MailService
    ) { }

    async create(data: CreateReportDto) {
        const existingReport = await this.prisma.report.findUnique({
            where: { referenceNumber: data.referenceNumber }
        });

        if (existingReport) {
            throw new ConflictException(`Report with reference number ${data.referenceNumber} already exists.`);
        }
        const report = await this.prisma.report.create({
            data: {
                addressedTo: data.addressedTo,
                referenceNumber: data.referenceNumber,
                stateOfDesign: data.stateOfDesign,
                stateOfRegistry: data.stateOfRegistry,
                stateOfManufacturer: data.stateOfManufacturer,
                stateOfOperator: data.stateOfOperator,
                occurrenceType: data.occurrenceType,
                aircraftInfo: data.aircraftInfo as unknown as Prisma.JsonArray,
                groundPeopleInjured: data.groundPeopleInjured,
                groundPeoplePerished: data.groundPeoplePerished,
                occurrenceDate: data.occurrenceDate,
                occurrenceTimeUTC: data.occurrenceTimeUTC,
                occurrenceTimeLocal: data.occurrenceTimeLocal,
                position: data.position,
                latitude: data.latitude,
                longitude: data.longitude,
                occurrenceDescription: data.occurrenceDescription,
                damageExtent: data.damageExtent,
                investigationDelegation: data.investigationDelegation,
                investigationExtent: data.investigationExtent,
                areaCharacteristics: data.areaCharacteristics,
                accessRequirements: data.accessRequirements,
                originatingAuthority: data.originatingAuthority ?? 'AAID-Rwanda',
                investigatorName: data.investigatorName,
                investigatorEmail: data.investigatorEmail,
                investigatorMobile: data.investigatorMobile,
                dangerousGoodsPresent: data.dangerousGoodsPresent,
                dangerousGoodsDescription: data.dangerousGoodsDescription
            }
        });
        return new ApiResponse(true, 'Report created successfully', report);
    }

    async findAll() {
        const data = await this.prisma.report.findMany({
            orderBy: {
                createdAt: 'desc'
            }
        });
        return new ApiResponse(true, 'Reports fetched successfully', data);
    }

    async findOne(id: string) {
        try {
            const data = await this.prisma.report.findUnique({ where: { id } });
            if (!data) {
                throw new NotFoundException(`Report with ID ${id} not found.`);
            }
            return new ApiResponse(true, 'Report fetched successfully', data);
        } catch (error) {
            if (error instanceof NotFoundException) {
                throw error;
            }
            throw new InternalServerErrorException('Failed to fetch report');
        }
    }

    async update(id: string, updateReportDto: UpdateReportDto) {
        try {
            const existingReport = await this.prisma.report.findUnique({ where: { id } });
            if (!existingReport) {
                throw new NotFoundException(`Report with ID ${id} not found.`);
            }

            const data = await this.prisma.report.update({
                where: { id },
                data: {
                    addressedTo: updateReportDto.addressedTo,
                    referenceNumber: updateReportDto.referenceNumber,
                    stateOfDesign: updateReportDto.stateOfDesign,
                    stateOfRegistry: updateReportDto.stateOfRegistry,
                    stateOfManufacturer: updateReportDto.stateOfManufacturer,
                    stateOfOperator: updateReportDto.stateOfOperator,
                    occurrenceType: updateReportDto.occurrenceType,
                    aircraftInfo: updateReportDto.aircraftInfo as unknown as Prisma.JsonArray,
                    groundPeopleInjured: updateReportDto.groundPeopleInjured,
                    groundPeoplePerished: updateReportDto.groundPeoplePerished,
                    occurrenceDate: updateReportDto.occurrenceDate,
                    occurrenceTimeUTC: updateReportDto.occurrenceTimeUTC,
                    occurrenceTimeLocal: updateReportDto.occurrenceTimeLocal,
                    position: updateReportDto.position,
                    latitude: updateReportDto.latitude,
                    longitude: updateReportDto.longitude,
                    occurrenceDescription: updateReportDto.occurrenceDescription,
                    damageExtent: updateReportDto.damageExtent,
                    investigationExtent: updateReportDto.investigationExtent,
                    investigationDelegation: updateReportDto.investigationDelegation,
                    areaCharacteristics: updateReportDto.areaCharacteristics,
                    accessRequirements: updateReportDto.accessRequirements,
                    originatingAuthority: updateReportDto.originatingAuthority ?? 'AAID-Rwanda',
                    investigatorName: updateReportDto.investigatorName,
                    investigatorEmail: updateReportDto.investigatorEmail,
                    investigatorMobile: updateReportDto.investigatorMobile,
                    dangerousGoodsPresent: updateReportDto.dangerousGoodsPresent,
                    dangerousGoodsDescription: updateReportDto.dangerousGoodsDescription?
                    status: existingReport.status !== null? ReportStatus.PENDING : null
                }
            });
            return new ApiResponse(true, 'Report updated successfully', data);
        } catch (error) {
            if (error instanceof Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new ConflictException('A report with this reference number already exists.');
                }
            }
            if (error instanceof NotFoundException) {
                throw error;
            }
            throw new InternalServerErrorException('Failed to update report');
        }
    }

    async remove(id: string) {
        try {
            const existingReport = await this.prisma.report.findUnique({ where: { id } });
            if (!existingReport) {
                throw new NotFoundException(`Report with ID ${id} not found.`);
            }
            const data = await this.prisma.report.delete({ where: { id } });
            return new ApiResponse(true, 'Report deleted successfully', data);
        } catch (error) {
            if (error instanceof NotFoundException) {
                throw error;
            }
            throw new InternalServerErrorException('Failed to delete report');
        }
    }
    async search(query: string) {
        try {
            const searchQuery = query?.trim();
            
            if (!searchQuery) {
                return await this.findAll();
            }
    
            const data = await this.prisma.report.findMany({
                where: {
                    OR: [
                        { referenceNumber: { contains: searchQuery, mode: 'insensitive' } },
                        { stateOfDesign: { contains: searchQuery, mode: 'insensitive' } },
                        { stateOfRegistry: { contains: searchQuery, mode: 'insensitive' } },
                        { stateOfManufacturer: { contains: searchQuery, mode: 'insensitive' } },
                        { stateOfOperator: { contains: searchQuery, mode: 'insensitive' } },
                        { position: { contains: searchQuery, mode: 'insensitive' } },
                        { occurrenceDescription: { contains: searchQuery, mode: 'insensitive' } },
                        { damageExtent: { contains: searchQuery, mode: 'insensitive' } },
                        { areaCharacteristics: { contains: searchQuery, mode: 'insensitive' } },
                        { investigatorName: { contains: searchQuery, mode: 'insensitive' } },
                        { investigatorEmail: { contains: searchQuery, mode: 'insensitive' } },
                        { originatingAuthority: { contains: searchQuery, mode: 'insensitive' } }
                    ]
                },
                orderBy: {
                    createdAt: 'desc'
                }
            });
    
            if (data.length === 0) {
                return new ApiResponse(true, 'No matching reports found', []);
            }
    
            return new ApiResponse(true, 'Reports fetched successfully', data);
        } catch (error) {
            throw new InternalServerErrorException('Failed to search reports');
        }
    }

    async submit(id: string, req: Request & { user: any }){
        const report = await this.prisma.report.findUnique({
            where: {
                id
            },
        })

        if(!report){
            throw new NotFoundException('Report not found')
        }

        if(report.status === ReportStatus.PENDING){
            throw new BadRequestException('Report already submitted')
        }

        if(report.status === ReportStatus.APPROVED){
            throw new BadRequestException('Report already approved')
        }

        const updated = await this.prisma.report.update({
            where: {
                id
            },
            data: {
                status: ReportStatus.PENDING,
                submittedById: req.user.id
            }
        })

        const _DGs = await this.prisma.user.findMany({
            where: {
                role: Role.DG
            }
        })

        for(const user of _DGs){
            this.mailService.sendMail(MailType.APPROVAL_REQUEST, {
                to: user.email,
                name: user.name,
                subject: 'Request for approval | AAID Notification',
                values: {
                    name: user.name,
                    by: req.user.name,
                    date: `${new Date().toLocaleString()}`,
                    refNo: updated.referenceNumber,
                    link: `${env.FE_URL}/dg/reports/edit/${report.id}`
                }
            })
        }

        return new ApiResponse(true, '', updated)
    }

    async approve(id: string, data: ApproveDTO, req: Request & { user: any }){
        const report = await this.prisma.report.findUnique({
            where: {
                id
            },
            include: {
                submittedBy: true
            }
        })

        if(!report){
            throw new NotFoundException('Report not found')
        }

        if(report.status === null){
            throw new BadRequestException('Report not submitted yet')
        }

        if(report.status === ReportStatus.APPROVED){
            throw new BadRequestException('Report already approved')
        }

        const user = await this.prisma.user.findUnique({
            where: {
                id: req.user.id
            }
        })

        const approved = await this.prisma.report.update({
            where: {
                id
            },
            data:{
                status: ReportStatus.APPROVED,
                comment: data?.comment??null,
                approvedById: user.id??null
            },
        })

        this.mailService.sendMail(MailType.REPORT_APPROVED, {
            to: report.submittedBy.email,
            name: report.submittedBy.name,
            subject: 'Report approved | AAID Notification',
            values: {
                name: report.submittedBy.name,
                refNo: report.referenceNumber,
                by: `${user.name} (${user.role.split('_').join(' ')})`,
                comment: data.comment??'',
                link: `${env.FE_URL}/portal/reports/edit/${report.id}`
            }
        })

        return new ApiResponse(true, 'approved', approved)
    }

    async revert(id: string, data: ApproveDTO, req: Request & { user: any }){
        const report = await this.prisma.report.findUnique({
            where: {
                id
            },
            include: {
                submittedBy: true
            }
        })

        if(!report){
            throw new NotFoundException('Report not found')
        }

        if(report.status === null){
            throw new BadRequestException('Report not submitted yet')
        }

        if(report.status === ReportStatus.REVERTED){
            throw new BadRequestException('Report already reverted')
        }

        if(report.status === ReportStatus.APPROVED){
            throw new BadRequestException('Report already approved')
        }

        const user = await this.prisma.user.findUnique({
            where: {
                id: req.user.id
            }
        })

        const reverted = await this.prisma.report.update({
            where: {
                id
            },
            data:{
                status: ReportStatus.REVERTED,
                comment: data?.comment??null,
                approvedById: user.id??null
            }
        })

        this.mailService.sendMail(MailType.REPORT_REVERTED, {
            to: report.submittedBy.email,
            name: report.submittedBy.name,
            subject: 'Report reverted | AAID Notification',
            values: {
                name: report.submittedBy.name,
                refNo: report.referenceNumber,
                by: `${user.name} (${user.role.split('_').join(' ')})`,
                comment: data.comment??''
            }
        })

        return new ApiResponse(true, 'reverted', reverted)
    }
}
