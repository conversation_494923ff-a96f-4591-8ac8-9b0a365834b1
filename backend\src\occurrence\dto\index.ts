import {
  IsString,
  IsEmail,
  IsOptional,
  IsDateString,
  IsEnum,
  IsInt,
  IsArray,
  ValidateNested,
  IsDate,
  IsUUID,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import {
  FlightRules,
  OperationType,
  OccurrenceType,
  MeteologicalCondition,
  OccurrenceStatus,
} from '@prisma/client';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

// Sub-DTO for Aircraft details
export class AircraftDto {
  @ApiProperty({ example: 'Boeing 737', description: 'The model of the aircraft.' })
  @IsString()
  model: string;

  @ApiProperty({ example: 'Boeing', description: 'The manufacturer of the aircraft.' })
  @IsString()
  @IsOptional()
  manufacturer?: string;

  @ApiProperty({ example: 'Airline A', description: 'The operator of the aircraft.', required: false })
  @IsOptional()
  @IsString()
  operator?: string;

  @ApiProperty({ example: 'N12345', description: 'The registration mark of the aircraft.', required: false })
  @IsOptional()
  @IsString()
  registrationMark?: string;

  @ApiProperty({ example: 'USA', description: 'The nationality of the operator.', required: false })
  @IsOptional()
  @IsString()
  operatorNationality?: string;

  @ApiProperty({ example: '2024-11-21T10:30:00Z', description: 'The intended landing date and time.', required: false })
  @IsOptional()
  @Transform(({ value }) => {
    return value ? new Date(value).toISOString() : null;
  })
  intendedLandingDateTime?: Date;

  @ApiProperty({ example: 'JFK Airport', description: 'The intended landing point.', required: false })
  @IsOptional()
  @IsString()
  intendedLandingPoint?: string;

  @ApiProperty({ example: 'LAX Airport', description: 'The last departure point.', required: false })
  @IsOptional()
  @IsString()
  lastDeparturePoint?: string;

  @ApiProperty({ example: 5, description: 'Number of crew members on board.', required: false })
  @IsOptional()
  @IsInt()
  crewOnBoard?: number;

  @ApiProperty({ example: 2, description: 'Number of crew members injured.', required: false })
  @IsOptional()
  @IsInt()
  crewInjured?: number;

  @ApiProperty({ example: 1, description: 'Number of crew members perished.', required: false })
  @IsOptional()
  @IsInt()
  crewPerished?: number;

  @ApiProperty({ example: 150, description: 'Number of passengers on board.', required: false })
  @IsOptional()
  @IsInt()
  passengersOnBoard?: number;

  @ApiProperty({ example: 10, description: 'Number of passengers injured.', required: false })
  @IsOptional()
  @IsInt()
  passengersInjured?: number;

  @ApiProperty({ example: 5, description: 'Number of passengers perished.', required: false })
  @IsOptional()
  @IsInt()
  passengersPerished?: number;
}

export class CreateOccurrenceDto {
  //general information
  @ApiProperty({ example: 'John Doe', description: 'The name of the reporter.' })
  @IsString()
  reporterName: string;

  @ApiProperty({ example: '<EMAIL>', description: 'The email address of the reporter.' })
  @IsEmail()
  reporterEmail: string;

  @ApiProperty({ example: '************', description: 'The phone number of the reporter.' })
  @IsString()
  reporterPhone: string;

  @ApiProperty({ example: 'Jane Smith', description: 'The name of the pilot in command.', required: false })
  @IsOptional()
  @IsString()
  pilotInCommandName?: string;

  @ApiProperty({ example: '<EMAIL>', description: 'The email address of the pilot in command.', required: false })
  @IsOptional()
  @IsEmail()
  pilotInCommandEmail?: string;

  @ApiProperty({ example: '************', description: 'The phone number of the pilot in command.', required: false })
  @IsOptional()
  @IsString()
  pilotInCommandPhone?: string;


  //Occurrence
  @ApiProperty({ type: [AircraftDto], description: 'List of involved aircraft details.' })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AircraftDto)
  involvedAircraft: AircraftDto[];

  @ApiProperty({ example: 0, description: 'Number of ground people perished.', required: false })
  @IsOptional()
  @IsInt()
  groundPeoplePerished?: number;

  @ApiProperty({ example: 0, description: 'Number of ground people injured.', required: false })
  @IsOptional()
  @IsInt()
  groundPeopleInjured?: number;

  //details
  @ApiProperty({ example: 'Clear', description: 'General weather conditions at the time of the occurrence.', required: false })
  @IsOptional()
  @IsString()
  generalWeatherConditions?: string;

  @ApiProperty({ example: 'Few clouds', description: 'Sky coverage at the time of the occurrence.', required: false })
  @IsOptional()
  @IsString()
  skyCoverage?: string;

  @ApiProperty({ enum: MeteologicalCondition, description: 'Meteorological conditions (e.g., IMC or VMC).' })
  @IsEnum(MeteologicalCondition)
  @IsOptional()
  meteologicalCondition?: MeteologicalCondition;

  @ApiProperty({ enum: FlightRules, description: 'Flight rules (e.g., IFR or VFR).' })
  @IsEnum(FlightRules)
  @IsOptional()
  flightRules?: FlightRules;

  @ApiProperty({ example: '2024-11-21T10:30:00Z', description: 'The date and time of the occurrence.' })
  @Transform(({ value }) => {
    return value ? new Date(value).toISOString() : null;
  })
  @IsOptional()
  occurrenceTime?: Date;

  @ApiProperty({ enum: OperationType, description: 'Type of operation being conducted.' })
  @IsEnum(OperationType)
  @IsOptional()
  operationType?: OperationType;

  @ApiProperty({ example: 'Startn engine(s)', required: false })
  @IsOptional()
  @IsString()
  flightPhase?: string;

  @ApiProperty({ example: '34.0522', description: 'Latitude of the occurrence location.' })
  @IsString()
  @IsOptional()
  latitude?: string;

  @ApiProperty({ example: '-118.2437', description: 'Longitude of the occurrence location.' })
  @IsString()
  @IsOptional()
  longitude?: string;

  @ApiProperty({ example: 'Los Angeles, CA', description: 'Specific location of the occurrence.' })
  @IsString()
  @IsOptional()
  occurrenceLocation?: string;

  @ApiProperty({ example: 'None', description: 'Dangerous goods carried on board.', required: false })
  @IsOptional()
  @IsString()
  dangerousGoodCarriedOnBoard?: string;
}

export class UpdateAircraftDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsUUID()
  id: string;

  // @IsString()
  // occurrenceId: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  manufacturer: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  model: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  registrationMark: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  operator: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  operatorNationality: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  lastDeparturePoint: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  intendedLandingPoint: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  intendedLandingDateTime: Date;

  @ApiPropertyOptional()
  @IsOptional()
  crewOnBoard: number;

  @ApiPropertyOptional()
  @IsOptional()
  crewInjured: number;

  @ApiPropertyOptional()
  @IsOptional()
  crewPerished: number;

  @ApiPropertyOptional()
  @IsOptional()
  passengersOnBoard: number;

  @ApiPropertyOptional()
  @IsOptional()
  passengersInjured: number;

  @ApiPropertyOptional()
  @IsOptional()
  passengersPerished: number;
}

export class UpdateOccurrenceDto {
  @ApiPropertyOptional({ enum: OccurrenceType })
  @IsOptional()
  @IsEnum(OccurrenceType)
  type?: OccurrenceType;

  @ApiPropertyOptional()
  @IsOptional()
  @IsUUID()
  occurrenceCategory_id?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  reporterName?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEmail()
  reporterEmail?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  reporterPhone?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  pilotInCommandName?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEmail()
  pilotInCommandEmail?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  pilotInCommandPhone?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  occurrenceLocation?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  latitude?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  longitude?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  occurrenceTime?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(OperationType)
  operationType?: OperationType;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(FlightRules)
  flightRules?: FlightRules;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(MeteologicalCondition)
  meteologicalCondition?: MeteologicalCondition;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  flightPhase?: string;

  @ApiPropertyOptional({
    type: [UpdateAircraftDto],
    description: 'List of involved aircraft'
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateAircraftDto)
  involvedAircraft?: UpdateAircraftDto[];

  @ApiPropertyOptional({
    description: 'Occurrence Status',
    enum: OccurrenceStatus
  })
  @IsOptional()
  @IsEnum(OccurrenceStatus)
  status?: OccurrenceStatus;
}