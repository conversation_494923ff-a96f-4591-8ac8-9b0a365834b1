import { PrismaService } from "src/prisma/prisma.service";
import { ApiResponse } from "src/utils/@types";
import { CreateSignatureDto, UpdateSignatureDto } from "./dto";
export declare class SignatureService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    create(createSignatureDto: CreateSignatureDto): Promise<ApiResponse<{
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        imageUrl: string;
    }>>;
    findAll(page?: number, pageSize?: number): Promise<ApiResponse<{
        data: {
            id: string;
            name: string;
            createdAt: Date;
            updatedAt: Date;
            imageUrl: string;
        }[];
        total: number;
        pageSize: number;
    }>>;
    findOne(id: string): Promise<ApiResponse<{
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        imageUrl: string;
    }>>;
    update(id: string, updateSignatureDto: UpdateSignatureDto): Promise<ApiResponse<{
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        imageUrl: string;
    }>>;
    remove(id: string): Promise<ApiResponse<{
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        imageUrl: string;
    }>>;
}
