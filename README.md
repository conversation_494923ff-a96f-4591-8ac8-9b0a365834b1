# AAID-NOTIFICATION
AAID Notification System

## Setup & Running Instructions

### 0. Environment
### Backend
In `backend` folder run
```bash
cp .env.example .env
```
to copy the .env structure and replace with the real values

NOTE: The default admin user must be configured in that .env file

### File Server
In `file-server` the .env file is already there!

### Frontend
In `frontend` folder, the environment values are located in `src/environments/`
change and verify the env values in both `environment.development.ts` and `environment.ts` just to be sure.


```typescript
export const environment = {
    production: false,
    API_BASE_URL: 'http://localhost:5000/api/v1',
    FILE_SERVER_URL: 'http://localhost:3016',
    FE_URL: 'https://localhost:4200'
};
```

```typescript
export const environment = {
    production: true,
    API_BASE_URL: 'http://localhost:5000/api/v1',
    FILE_SERVER_URL: 'http://localhost:3016',
    FE_URL: 'https://localhost:4200'
};
```

### 1. Using Docker
To set up and run the AAID Notification System using Docker, follow these steps:

1. **Clone the repository**:
   ```bash
   git clone https://github.com/MININFRA-GOV-RW/AAID-NOTIFICATION.git
   cd AAID-NOTIFICATION
   ```
    And set environment variable for each service (.env)
2. **Ensure Docker and Docker Compose are installed** on your machine.

3. **Build and start the services**:
   In the root directory of the project, run:
   ```bash
   docker-compose up --build
   ```

4. **Access the services**:
   - Backend: `http://localhost:5000`
   - Frontend: `http://localhost:4200`
   - File server: `http://localhost:3016`

5. **Check logs for any errors**:
   If there are any issues, you can check the logs of the services by running:
   ```bash
   docker-compose logs <service-name>
   ```

6. **Stop the services**:
   When done, you can stop the services using:
   ```bash
   docker-compose down
   ```

### 2. Running Each Service Manually
If you prefer to run the services manually (without Docker), follow the steps below:

1. **Backend Service**:
   - Install dependencies:
     ```bash
     cd backend
     npm install
     ```

   - Set environment variables:
     Ensure your environment variables are correctly set in the `.env` file or use the `docker-compose.yml` configuration.

   - Start the backend:
     ```bash
     npm run start:dev
     ```

2. **Frontend Service**:
   - Install dependencies:
     ```bash
     cd frontend
     npm install
     ```

   - Start the frontend:
     ```bash
     npm start
     ```

3. **File Server Service**:
   - Install dependencies:
     ```bash
     cd file-server
     npm install
     ```

   - Start the file server:
     ```bash
     npm start
     ```

4. **Database Service**:
   - If you're running PostgreSQL manually, ensure it's running on the correct port.
