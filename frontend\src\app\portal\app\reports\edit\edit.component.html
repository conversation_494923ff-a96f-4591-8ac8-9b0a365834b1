<div class="w-full h-full p-8 flex flex-col gap-4 overflow-scroll">
    <div class="flex items-center justify-between">
      <h2 class="text-xl font-semibold text-gray-700">NOTIFICATION FORM OF ACCIDENT OR SERIOUS INCIDENT</h2>
    </div>

    <!-- <div *ngIf="report.comment" class="p-4 bg-white rounded-lg">
      <p class="font-medium text-lg text-gray-700">Status: {{report.status}}</p>
      <p class="font-medium mt-4 text-gray-700">Comment</p>
      <p class="text-gray-700">{{report.comment}}</p>
    </div> -->
  
    <div class="bg-white shadow rounded-lg">
      <form [formGroup]="notificationForm" (ngSubmit)="onSubmit()" class="p-4">
        <div class="flex flex-col gap-6">
  
          <!-- Replace the existing addressed to section in new.component.html -->
          <div class="space-y-4">
            <label class="text-sm font-medium">The notification addressed to: <span class="text-red-300">*</span></label>
            <div class="mt-2 flex flex-col gap-2">
              <!-- State of Registry -->
              <div class="flex items-center gap-2">
                <div class="flex items-center gap-2 w-48">
                  <input type="checkbox" id="addressedToRegistry" formControlName="addressedToRegistry"
                    class="w-4 h-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                  <label for="addressedToRegistry" class="text-sm">State of Registry</label>
                </div>
                <div class="flex-1" *ngIf="notificationForm.get('addressedToRegistry')?.value">
                  <p-dropdown formControlName="stateOfRegistry" [style]="{'border': '1px solid #3333'}"
                    [options]="countries" optionLabel="name" [filter]="true" filterBy="name" [showClear]="true"
                    placeholder="Select state of registry" styleClass="w-full">
                    <ng-template pTemplate="item" let-country>
                      <div class="flex align-items-center gap-2">
                        <img [src]="country.flag" [alt]="country.name" class="w-5" />
                        <div>{{country.name}}</div>
                      </div>
                    </ng-template>
                  </p-dropdown>
                </div>
              </div>
  
              <!-- State of Design -->
              <div class="flex items-center gap-2">
                <div class="flex items-center gap-2 w-48">
                  <input type="checkbox" id="addressedToDesign" formControlName="addressedToDesign"
                    class="w-4 h-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                  <label for="addressedToDesign" class="text-sm">State of Design</label>
                </div>
                <div class="flex-1" *ngIf="notificationForm.get('addressedToDesign')?.value">
                  <p-dropdown formControlName="stateOfDesignCountry" [style]="{'border': '1px solid #3333'}"
                    [options]="countries" optionLabel="name" [filter]="true" filterBy="name" [showClear]="true"
                    placeholder="Select state of design" styleClass="w-full">
                    <ng-template pTemplate="item" let-country>
                      <div class="flex align-items-center gap-2">
                        <img [src]="country.flag" [alt]="country.name" class="w-5" />
                        <div>{{country.name}}</div>
                      </div>
                    </ng-template>
                  </p-dropdown>
                </div>
              </div>
  
              <!-- State of Manufacturer -->
              <div class="flex items-center gap-2">
                <div class="flex items-center gap-2 w-48">
                  <input type="checkbox" id="addressedToManufacturer" formControlName="addressedToManufacturer"
                    class="w-4 h-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                  <label for="addressedToManufacturer" class="text-sm">State of Manufacturer</label>
                </div>
                <div class="flex-1" *ngIf="notificationForm.get('addressedToManufacturer')?.value">
                  <p-dropdown formControlName="stateOfManufacturerCountry" [style]="{'border': '1px solid #3333'}"
                    [options]="countries" optionLabel="name" [filter]="true" filterBy="name" [showClear]="true"
                    placeholder="Select state of manufacturer" styleClass="w-full">
                    <ng-template pTemplate="item" let-country>
                      <div class="flex align-items-center gap-2">
                        <img [src]="country.flag" [alt]="country.name" class="w-5" />
                        <div>{{country.name}}</div>
                      </div>
                    </ng-template>
                  </p-dropdown>
                </div>
              </div>
  
              <!-- State of Operator -->
              <div class="flex items-center gap-2">
                <div class="flex items-center gap-2 w-48">
                  <input type="checkbox" id="addressedToOperator" formControlName="addressedToOperator"
                    class="w-4 h-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                  <label for="addressedToOperator" class="text-sm">State of Operator</label>
                </div>
                <div class="flex-1" *ngIf="notificationForm.get('addressedToOperator')?.value">
                  <p-dropdown formControlName="stateOfOperatorCountry" [style]="{'border': '1px solid #3333'}"
                    [options]="countries" optionLabel="name" [filter]="true" filterBy="name" [showClear]="true"
                    placeholder="Select state of operator" styleClass="w-full">
                    <ng-template pTemplate="item" let-country>
                      <div class="flex align-items-center gap-2">
                        <img [src]="country.flag" [alt]="country.name" class="w-5" />
                        <div>{{country.name}}</div>
                      </div>
                    </ng-template>
                  </p-dropdown>
                </div>
              </div>
  
              <!-- ICAO -->
              <div class="flex items-center gap-2">
                <div class="flex items-center gap-2 w-48">
                  <input type="checkbox" id="addressedToICAO" formControlName="addressedToICAO"
                    class="w-4 h-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                  <label for="addressedToICAO" class="text-sm">ICAO</label>
                </div>
              </div>
            </div>
  
            <!-- Validation error message -->
            <div *ngIf="notificationForm.errors?.['noAddressedTo'] && notificationForm.touched"
              class="text-red-500 text-sm">
              Please select at least one recipient.
            </div>
          </div>
  
          <div class="flex flex-col gap-2">
            <label class="text-sm font-medium">Reference Number <span class="text-red-300">*</span></label>
            <input type="text" formControlName="referenceNumber" placeholder="e.g INCID-2024-001"
              [class.border-red-500]="notificationForm.get('referenceNumber')?.invalid && notificationForm.get('referenceNumber')?.touched"
              class="p-2 border bg-white rounded-lg outline-none focus:ring-1 ring-primary-500" />
            <div
              *ngIf="notificationForm.get('referenceNumber')?.invalid && notificationForm.get('referenceNumber')?.touched"
              class="text-sm text-red-500">
              Reference Number is required.
            </div>
          </div>
  
          <!-- Section A - Occurrence Type -->
          <div class="flex flex-col gap-2">
            <label class="text-sm font-medium">a) Occurrence Type <span class="text-red-300">*</span></label>
            <select formControlName="occurrenceType"
              [class.border-red-500]="notificationForm.get('occurrenceType')?.invalid && notificationForm.get('occurrenceType')?.touched"
              class="p-2 border bg-white rounded-lg outline-none focus:ring-1 ring-primary-500">
              <option value="">Select Type</option>
              <option value="ACCID">ACCID - Accident</option>
              <option value="SINCID">SINCID - Serious Incident</option>
              <option value="INCID">INCID - Incident</option>
            </select>
            <div
              *ngIf="notificationForm.get('occurrenceType')?.invalid && notificationForm.get('occurrenceType')?.touched"
              class="text-sm text-red-500">
              Occurrence Type is required.
            </div>
          </div>
  
          <!-- Aircraft Information Group (Sections B, C, E, G) -->
          <div formArrayName="aircraftInfo" class="space-y-6">
            <div *ngFor="let aircraft of aircraftInfo.controls; let i = index" [formGroupName]="i"
              class="p-6 border rounded-lg space-y-6 bg-gray-50">
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold">Aircraft {{i + 1}} Information</h3>
                <div class="flex gap-2">
                  <button type="button" *ngIf="aircraftInfo.length > 1" (click)="removeAircraft(i)"
                    class="px-3 py-1 text-sm text-red-600 border border-red-600 rounded hover:bg-red-50">
                    Remove Aircraft
                  </button>
                  <button type="button" *ngIf="i === aircraftInfo.length - 1" (click)="addAircraft()"
                    class="px-3 py-1 text-sm text-primary-600 border border-primary-600 rounded hover:bg-primary-50">
                    Add Another Aircraft
                  </button>
                </div>
              </div>
  
              <!-- Section B - Aircraft Details -->
              <div formGroupName="aircraftDetails" class="space-y-4">
                <label class="text-sm font-medium">b) Aircraft Details</label>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <input formControlName="manufacturer" placeholder="Manufacturer"
                    [class.border-red-500]="aircraft.get('aircraftDetails.manufacturer')?.invalid && aircraft.get('aircraftDetails.manufacturer')?.touched"
                    class="p-2 border bg-white rounded-lg outline-none focus:ring-1 ring-primary-500" />
                  <div
                    *ngIf="aircraft.get('aircraftDetails.manufacturer')?.invalid && aircraft.get('aircraftDetails.manufacturer')?.touched"
                    class="text-sm text-red-500">
                    Manufacturer is required.
                  </div>
  
                  <input formControlName="model" placeholder="Model"
                    [class.border-red-500]="aircraft.get('aircraftDetails.model')?.invalid && aircraft.get('aircraftDetails.model')?.touched"
                    class="p-2 border bg-white rounded-lg outline-none focus:ring-1 ring-primary-500" />
                  <div
                    *ngIf="aircraft.get('aircraftDetails.model')?.invalid && aircraft.get('aircraftDetails.model')?.touched"
                    class="text-sm text-red-500">
                    Model is required.
                  </div>
  
                  <input formControlName="nationality" placeholder="Nationality"
                    [class.border-red-500]="aircraft.get('aircraftDetails.nationality')?.invalid && aircraft.get('aircraftDetails.nationality')?.touched"
                    class="p-2 border bg-white rounded-lg outline-none focus:ring-1 ring-primary-500" />
                  <div
                    *ngIf="aircraft.get('aircraftDetails.nationality')?.invalid && aircraft.get('aircraftDetails.nationality')?.touched"
                    class="text-sm text-red-500">
                    Nationality is required.
                  </div>
  
                  <input formControlName="registrationMarks" placeholder="Registration Marks"
                    [class.border-red-500]="aircraft.get('aircraftDetails.registrationMarks')?.invalid && aircraft.get('aircraftDetails.registrationMarks')?.touched"
                    class="p-2 border bg-white rounded-lg outline-none focus:ring-1 ring-primary-500" />
                  <div
                    *ngIf="aircraft.get('aircraftDetails.registrationMarks')?.invalid && aircraft.get('aircraftDetails.registrationMarks')?.touched"
                    class="text-sm text-red-500">
                    Registration Marks is required.
                  </div>
  
                  <input formControlName="serialNumber" placeholder="Serial Number"
                    [class.border-red-500]="aircraft.get('aircraftDetails.serialNumber')?.invalid && aircraft.get('aircraftDetails.serialNumber')?.touched"
                    class="p-2 border bg-white rounded-lg outline-none focus:ring-1 ring-primary-500" />
                  <div
                    *ngIf="aircraft.get('aircraftDetails.serialNumber')?.invalid && aircraft.get('aircraftDetails.serialNumber')?.touched"
                    class="text-sm text-red-500">
                    Serial Number is required.
                  </div>
                </div>
  
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <input formControlName="owner" placeholder="Owner"
                    [class.border-red-500]="aircraft.get('aircraftDetails.owner')?.invalid && aircraft.get('aircraftDetails.owner')?.touched"
                    class="p-2 border bg-white rounded-lg outline-none focus:ring-1 ring-primary-500" />
                  <div
                    *ngIf="aircraft.get('aircraftDetails.owner')?.invalid && aircraft.get('aircraftDetails.owner')?.touched"
                    class="text-sm text-red-500">
                    Owner is required.
                  </div>
  
                  <input formControlName="operator" placeholder="Operator"
                    [class.border-red-500]="aircraft.get('aircraftDetails.operator')?.invalid && aircraft.get('aircraftDetails.operator')?.touched"
                    class="p-2 border bg-white rounded-lg outline-none focus:ring-1 ring-primary-500" />
                  <div
                    *ngIf="aircraft.get('aircraftDetails.operator')?.invalid && aircraft.get('aircraftDetails.operator')?.touched"
                    class="text-sm text-red-500">
                    Operator is required.
                  </div>
  
                  <input formControlName="hirer" placeholder="Hirer (if any)"
                    [class.border-red-500]="aircraft.get('aircraftDetails.hirer')?.invalid && aircraft.get('aircraftDetails.hirer')?.touched"
                    class="p-2 border bg-white rounded-lg outline-none focus:ring-1 ring-primary-500" />
                  <div
                    *ngIf="aircraft.get('aircraftDetails.hirer')?.invalid && aircraft.get('aircraftDetails.hirer')?.touched"
                    class="text-sm text-red-500">
                    Hirer is required.
                  </div>
                </div>
              </div>
  
              <!-- Section C - Crew Details -->
              <div formGroupName="crewDetails" class="space-y-4">
                <label class="text-sm font-medium">c) Crew and Passenger Information <span
                    class="text-red-300">*</span></label>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <input formControlName="pilotQualification" placeholder="Pilot-in-Command Qualification"
                    [class.border-red-500]="aircraft.get('crewDetails.pilotQualification')?.invalid && aircraft.get('crewDetails.pilotQualification')?.touched"
                    class="p-2 border bg-white rounded-lg outline-none focus:ring-1 ring-primary-500" />
                  <div
                    *ngIf="aircraft.get('crewDetails.pilotQualification')?.invalid && aircraft.get('crewDetails.pilotQualification')?.touched"
                    class="text-sm text-red-500">
                    Pilot Qualification is required.
                  </div>
  
                  <input formControlName="crewNationality" placeholder="Crew Nationality"
                    [class.border-red-500]="aircraft.get('crewDetails.crewNationality')?.invalid && aircraft.get('crewDetails.crewNationality')?.touched"
                    class="p-2 border bg-white rounded-lg outline-none focus:ring-1 ring-primary-500" />
                  <div
                    *ngIf="aircraft.get('crewDetails.crewNationality')?.invalid && aircraft.get('crewDetails.crewNationality')?.touched"
                    class="text-sm text-red-500">
                    Crew Nationality is required.
                  </div>
  
                  <input formControlName="passengerNationality" placeholder="Passenger Nationality"
                    [class.border-red-500]="aircraft.get('crewDetails.passengerNationality')?.invalid && aircraft.get('crewDetails.passengerNationality')?.touched"
                    class="p-2 border bg-white rounded-lg outline-none focus:ring-1 ring-primary-500" />
                  <div
                    *ngIf="aircraft.get('crewDetails.passengerNationality')?.invalid && aircraft.get('crewDetails.passengerNationality')?.touched"
                    class="text-sm text-red-500">
                    Passenger Nationality is required.
                  </div>
                </div>
              </div>
  
              <!-- Section E - Flight Path -->
              <div formGroupName="flightDetails" class="space-y-4">
                <label class="text-sm font-medium">e) Flight Path</label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <input formControlName="lastDeparturePoint" placeholder="Last Point of Departure"
                    [class.border-red-500]="aircraft.get('flightDetails.lastDeparturePoint')?.invalid && aircraft.get('flightDetails.lastDeparturePoint')?.touched"
                    class="p-2 border bg-white rounded-lg outline-none focus:ring-1 ring-primary-500" />
                  <div
                    *ngIf="aircraft.get('flightDetails.lastDeparturePoint')?.invalid && aircraft.get('flightDetails.lastDeparturePoint')?.touched"
                    class="text-sm text-red-500">
                    Last Point of Departure is required.
                  </div>
  
                  <input formControlName="intendedLandingPoint" placeholder="Point of Intended Landing"
                    [class.border-red-500]="aircraft.get('flightDetails.intendedLandingPoint')?.invalid && aircraft.get('flightDetails.intendedLandingPoint')?.touched"
                    class="p-2 border bg-white rounded-lg outline-none focus:ring-1 ring-primary-500" />
                  <div
                    *ngIf="aircraft.get('flightDetails.intendedLandingPoint')?.invalid && aircraft.get('flightDetails.intendedLandingPoint')?.touched"
                    class="text-sm text-red-500">
                    Intended Landing Point is required.
                  </div>
                </div>
              </div>
  
              <!-- Section G - Casualties -->
              <div formGroupName="casualties" class="space-y-4">
                <label class="text-sm font-medium">g) Casualties</label>
                <div class="overflow-x-auto">
                  <table class="w-full border-collapse bg-white">
                    <thead>
                      <tr class="bg-gray-50">
                        <th class="p-2 border text-left">Category</th>
                        <th class="p-2 border text-center">Aboard</th>
                        <th class="p-2 border text-center">Killed</th>
                        <th class="p-2 border text-center">Seriously Injured</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr formGroupName="crew">
                        <td class="p-2 border">Crew</td>
                        <td class="p-2 border">
                          <input type="number" formControlName="aboard" min="0"
                            class="w-full p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500" />
                        </td>
                        <td class="p-2 border">
                          <input type="number" formControlName="killed" min="0"
                            class="w-full p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500" />
                        </td>
                        <td class="p-2 border">
                          <input type="number" formControlName="injured" min="0"
                            class="w-full p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500" />
                        </td>
                      </tr>
                      <tr formGroupName="passengers">
                        <td class="p-2 border">Passengers</td>
                        <td class="p-2 border">
                          <input type="number" formControlName="aboard" min="0"
                            class="w-full p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500" />
                        </td>
                        <td class="p-2 border">
                          <input type="number" formControlName="killed" min="0"
                            class="w-full p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500" />
                        </td>
                        <td class="p-2 border">
                          <input type="number" formControlName="injured" min="0"
                            class="w-full p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500" />
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <div class="py-2 grid grid-col-1 md:grid-cols-2">
            <div class="flex flex-wrap gap-2 items-center">
              <label for="groundPeopleInjured">Ground people injured</label>
              <input type="number" formControlName="groundPeopleInjured" placeholder="Grounded People Injured"
              [ngClass]="{'border-red-500': notificationForm.get('groundPeopleInjured')?.invalid && notificationForm.get('groundPeopleInjured')?.touched}"
              class="p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500" />
            </div>
            <div class="flex flex-wrap gap-2 items-center">
              <label for="groundPeoplePerished">Ground people perished</label>
              <input type="number" formControlName="groundPeoplePerished" placeholder="Grounded People Perished"
              [ngClass]="{'border-red-500': notificationForm.get('groundPeoplePerished')?.invalid && notificationForm.get('groundPeopleInjured')?.touched}"
              class="p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500" />
            </div>
          </div>
          <!-- Section D - Date and Time -->
          <div class="space-y-4">
            <label class="text-sm font-medium">d) Occurrence Date and Time <span class="text-red-300">*</span></label>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div class="space-y-2">
                <label class="text-xs">Date</label>
                <input type="date" formControlName="occurrenceDate"
                  [ngClass]="{'border-red-500': notificationForm.get('occurrenceDate')?.invalid && notificationForm.get('occurrenceDate')?.touched}"
                  class="w-full p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500" />
                <div
                  *ngIf="notificationForm.get('occurrenceDate')?.invalid && notificationForm.get('occurrenceDate')?.touched"
                  class="text-red-500 text-xs">
                  <p *ngIf="notificationForm.get('occurrenceDate')?.errors?.['required']">Date is required.</p>
                </div>
              </div>
              <div class="space-y-2">
                <label class="text-xs">UTC Time</label>
                <input type="time" formControlName="occurrenceTimeUTC" (change)="onUTCTimeChange($event)"
                  [ngClass]="{'border-red-500': notificationForm.get('occurrenceTimeUTC')?.invalid && notificationForm.get('occurrenceTimeUTC')?.touched}"
                  class="w-full p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500" />
                <div
                  *ngIf="notificationForm.get('occurrenceTimeUTC')?.invalid && notificationForm.get('occurrenceTimeUTC')?.touched"
                  class="text-red-500 text-xs">
                  <p *ngIf="notificationForm.get('occurrenceTimeUTC')?.errors?.['required']">UTC time is required.</p>
                </div>
              </div>
              <div class="space-y-2">
                <label class="text-xs">Local Time</label>
                <input type="time" formControlName="occurrenceTimeLocal" (change)="onLocalTimeChange($event)"
                  [ngClass]="{'border-red-500': notificationForm.get('occurrenceTimeLocal')?.invalid && notificationForm.get('occurrenceTimeLocal')?.touched}"
                  class="w-full p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500" />
                <div
                  *ngIf="notificationForm.get('occurrenceTimeLocal')?.invalid && notificationForm.get('occurrenceTimeLocal')?.touched"
                  class="text-red-500 text-xs">
                  <p *ngIf="notificationForm.get('occurrenceTimeLocal')?.errors?.['required']">Local time is required.</p>
                </div>
              </div>
            </div>
          </div>
  
          <!-- Section F - Location -->
          <div class="space-y-4">
            <label class="text-sm font-medium">f) Aircraft Location</label>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <input formControlName="position" placeholder="Position Reference"
                [ngClass]="{'border-red-500': notificationForm.get('position')?.invalid && notificationForm.get('position')?.touched}"
                class="p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500" />
              <div *ngIf="notificationForm.get('position')?.invalid && notificationForm.get('position')?.touched"
                class="text-red-500 text-xs">
                <p *ngIf="notificationForm.get('position')?.errors?.['required']">Position Reference is required.</p>
              </div>
  
              <div class="space-y-2">
                <label class="text-xs">Latitude (format: DD°MM'SS"N/S) </label>
                <input formControlName="latitude" placeholder="e.g., 51°30'30 N"
                  [ngClass]="{'border-red-500': notificationForm.get('latitude')?.invalid && notificationForm.get('latitude')?.touched}"
                  class="w-full p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500" />
                <div *ngIf="notificationForm.get('latitude')?.invalid && notificationForm.get('latitude')?.touched"
                  class="text-red-500 text-xs">
                  <p *ngIf="notificationForm.get('latitude')?.errors?.['required']">Latitude is required.</p>
                </div>
              </div>
  
              <div class="space-y-2">
                <label class="text-xs">Longitude (format: DDD°MM'SS"E/W) </label>
                <input formControlName="longitude" placeholder="e.g., 000°10'15 W"
                  [ngClass]="{'border-red-500': notificationForm.get('longitude')?.invalid && notificationForm.get('longitude')?.touched}"
                  class="w-full p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500" />
                <div *ngIf="notificationForm.get('longitude')?.invalid && notificationForm.get('longitude')?.touched"
                  class="text-red-500 text-xs">
                  <p *ngIf="notificationForm.get('longitude')?.errors?.['required']">Longitude is required.</p>
                </div>
              </div>
            </div>
          </div>
  
          <!-- Section H - Description -->
          <div class="space-y-4">
            <label class="text-sm font-medium">h) Description and Damage <span class="text-red-300">*</span></label>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label class="text-xs">Description of the Occurrence <span class="text-red-300">*</span></label>
                <textarea formControlName="occurrenceDescription" rows="4"
                  [ngClass]="{'border-red-500': notificationForm.get('occurrenceDescription')?.invalid && notificationForm.get('occurrenceDescription')?.touched}"
                  class="w-full p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500"></textarea>
                <div
                  *ngIf="notificationForm.get('occurrenceDescription')?.invalid && notificationForm.get('occurrenceDescription')?.touched"
                  class="text-red-500 text-xs">
                  <p *ngIf="notificationForm.get('occurrenceDescription')?.errors?.['required']">Description is required.
                  </p>
                </div>
              </div>
  
              <div class="space-y-2">
                <label class="text-xs">Extent of Damage <span class="text-red-300">*</span></label>
                <textarea formControlName="damageExtent" rows="4"
                  [ngClass]="{'border-red-500': notificationForm.get('damageExtent')?.invalid && notificationForm.get('damageExtent')?.touched}"
                  class="w-full p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500"></textarea>
                <div
                  *ngIf="notificationForm.get('damageExtent')?.invalid && notificationForm.get('damageExtent')?.touched"
                  class="text-red-500 text-xs">
                  <p *ngIf="notificationForm.get('damageExtent')?.errors?.['required']">Extent of damage is required.</p>
                </div>
              </div>
            </div>
          </div>
  
          <!-- Section I - Investigation Details -->
          <div class="space-y-4">
            <label class="text-sm font-medium">i) Investigation Details</label>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <textarea formControlName="investigationExtent" placeholder="Investigation Extent"
                [ngClass]="{'border-red-500': notificationForm.get('investigationExtent')?.invalid && notificationForm.get('investigationExtent')?.touched}"
                class="p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500">
              </textarea>
              <div
                *ngIf="notificationForm.get('investigationExtent')?.invalid && notificationForm.get('investigationExtent')?.touched"
                class="text-red-500 text-xs">
                <p *ngIf="notificationForm.get('investigationExtent')?.errors?.['required']">Investigation extent is
                  required.</p>
              </div>
  
              <textarea formControlName="investigationDelegation" placeholder="Investigation Delegation"
                [ngClass]="{'border-red-500': notificationForm.get('investigationDelegation')?.invalid && notificationForm.get('investigationDelegation')?.touched}"
                class="p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500">
  
              </textarea>
              <div
                *ngIf="notificationForm.get('investigationDelegation')?.invalid && notificationForm.get('investigationDelegation')?.touched"
                class="text-red-500 text-xs">
                <p *ngIf="notificationForm.get('investigationDelegation')?.errors?.['required']">Investigation delegation
                  is
                  required.</p>
              </div>
            </div>
          </div>
  
          <!-- Section J - Physical Characteristics -->
          <div class="space-y-4">
            <label class="text-sm font-medium">J) Physical Characteristics</label>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <textarea formControlName="areaCharacteristics" placeholder="Area Characteristics"
                [ngClass]="{'border-red-500': notificationForm.get('areaCharacteristics')?.invalid && notificationForm.get('areaCharacteristics')?.touched}"
                class="p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500">
              </textarea>
              <div
                *ngIf="notificationForm.get('areaCharacteristics')?.invalid && notificationForm.get('areaCharacteristics')?.touched"
                class="text-red-500 text-xs">
                <p *ngIf="notificationForm.get('areaCharacteristics')?.errors?.['required']">Area characteristics are
                  required.</p>
              </div>
  
              <textarea formControlName="accessRequirements" placeholder="Access Requirements"
                [ngClass]="{'border-red-500': notificationForm.get('accessRequirements')?.invalid && notificationForm.get('accessRequirements')?.touched}"
                class="p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500">
              </textarea>
              <div
                *ngIf="notificationForm.get('accessRequirements')?.invalid && notificationForm.get('accessRequirements')?.touched"
                class="text-red-500 text-xs">
                <p *ngIf="notificationForm.get('accessRequirements')?.errors?.['required']">Access requirements are
                  required.
                </p>
              </div>
            </div>
          </div>
  
          <!-- Section K - Investigation Authority -->
          <div class="flex flex-col gap-6 md:flex-row">
            <div class="space-y-4 w-full md:w-1/2">
              <label class="text-sm font-medium">K) Investigation Authority <span class="text-red-300">*</span></label>
              <div class="grid grid-cols-1 gap-4">
                <input formControlName="originatingAuthority" placeholder="Originating Authority"
                  [ngClass]="{'border-red-500': notificationForm.get('originatingAuthority')?.invalid && notificationForm.get('originatingAuthority')?.touched}"
                  class="p-2 border rounded-lg outline-none block h-fit focus:ring-1 ring-primary-500 w-full" />
                <div
                  *ngIf="notificationForm.get('originatingAuthority')?.invalid && notificationForm.get('originatingAuthority')?.touched"
                  class="text-red-500 text-xs">
                  <p *ngIf="notificationForm.get('originatingAuthority')?.errors?.['required']">Originating authority is
                    required.</p>
                </div>
              </div>
            </div>
  
            <div formGroupName="investigatorInCharge" class="w-full md:w-1/2">
              <label class="text-sm font-medium">Investigator In Charge <span class="text-red-300">*</span></label>
              <div class="w-full mt-2">
                <p-dropdown formControlName="name" [style]="{'border': '1px solid #3333'}"
                  (onChange)="onInvestigatorChange($event)" [options]="investigators" optionLabel="name" [filter]="true"
                  filterBy="name" [showClear]="true" placeholder="Select an investigator" styleClass="w-full">
                </p-dropdown>
                <div
                  *ngIf="notificationForm.get('investigatorInCharge.name')?.invalid && notificationForm.get('investigatorInCharge.name')?.touched"
                  class="text-red-500 text-xs">
                  <p *ngIf="notificationForm.get('investigatorInCharge.name')?.errors?.['required']">Investigator's name
                    is
                    required.</p>
                </div>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-2 w-full mt-2">
                <div class="w-full">
                  <input formControlName="mobile" placeholder="Mobile"
                    [ngClass]="{'border-red-500': notificationForm.get('investigatorInCharge.mobile')?.invalid && notificationForm.get('investigatorInCharge.mobile')?.touched}"
                    class="p-2 border rounded-lg  w-full outline-none focus:ring-1 ring-primary-500" />
                  <div
                    *ngIf="notificationForm.get('investigatorInCharge.mobile')?.invalid && notificationForm.get('investigatorInCharge.mobile')?.touched"
                    class="text-red-500 text-xs">
                    <p *ngIf="notificationForm.get('investigatorInCharge.mobile')?.errors?.['required']">Mobile is
                      required.
                    </p>
                  </div>
                </div>
  
                <div class="w-full">
                  <input formControlName="email" placeholder="Email"
                    [ngClass]="{'border-red-500': notificationForm.get('investigatorInCharge.email')?.invalid && notificationForm.get('investigatorInCharge.email')?.touched}"
                    class="p-2 border rounded-lg  w-full outline-none focus:ring-1 ring-primary-500" />
                  <div
                    *ngIf="notificationForm.get('investigatorInCharge.email')?.invalid && notificationForm.get('investigatorInCharge.email')?.touched"
                    class="text-red-500 text-xs">
                    <p *ngIf="notificationForm.get('investigatorInCharge.email')?.errors?.['required']">Email is required.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
  
          <!-- Section L - Dangerous Goods -->
          <div class="space-y-4">
            <label class="text-sm font-medium">l) Dangerous Goods</label>
            <div class="space-y-4">
              <div class="flex items-center gap-2">
                <input type="checkbox" formControlName="dangerousGoodsPresent" id="dangerousGoodsPresent"
                  class="w-4 h-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                <label for="dangerousGoodsPresent" class="text-sm">Dangerous Goods Present</label>
              </div>
              <div *ngIf="notificationForm.get('dangerousGoodsPresent')?.value">
                <textarea formControlName="dangerousGoodsDescription" rows="4" placeholder="Description of Dangerous Goods"
                  [ngClass]="{'border-red-500': notificationForm.get('dangerousGoods.description')?.invalid && notificationForm.get('dangerousGoods.description')?.touched}"
                  class="w-full p-2 border rounded-lg outline-none focus:ring-1 ring-primary-500"></textarea>
                <div
                  *ngIf="notificationForm.get('dangerousGoodsDescription')?.invalid && notificationForm.get('dangerousGoodsDescription')?.touched"
                  class="text-red-500 text-xs">
                  <p *ngIf="notificationForm.get('dangerousGoodsDescription')?.errors?.['required']">Description is
                    required.</p>
                </div>
              </div>
            </div>
          </div>
  
          <!-- Form Actions -->
          <div class="flex justify-end gap-4 pt-6">
            @if(report.status === 'APPROVED'){
              <button (click)="downloadPdf()" type="button"  [disabled]="loading"
              class="px-4 py-2 text-sm font-medium flex items-center gap-2 text-white bg-gray-500 border border-transparent rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="pi pi-download"></i>
                <span>Download PDF</span>
            </button>
            }@else {
              <button (click)="preview()" type="button"  [disabled]="loading"
              class="px-4 py-2 text-sm font-medium flex items-center gap-2 text-white bg-gray-500 border border-transparent rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="pi pi-download"></i>
                <span>Preview</span>
            </button>
            }
            <button type="submit" [disabled]="loading"
              class="px-4 py-2 text-sm font-medium text-white bg-primary-500 border border-transparent rounded-lg hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
              <span *ngIf="!loading">Save</span>
              <span *ngIf="loading">Saving...</span>
            </button>

            <button *ngIf="report.status !== 'APPROVED'" type="button" (click)="confirmSubmit()" [disabled]="loading || report.status === 'PENDING'"
              class="px-4 py-2 text-sm font-medium text-white disabled:opacity-50 disabled:cursor-not-allowed bg-primary-500 border border-transparent rounded-lg hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
              <span *ngIf="!loading">Submit for approval</span>
              <span *ngIf="loading">submitting...</span>
            </button>
          </div>
        </div>
      </form>
    </div>
    <p-confirmDialog></p-confirmDialog>
    <p-toast position="top-right"></p-toast>
  </div>