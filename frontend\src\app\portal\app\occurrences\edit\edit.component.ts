import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { TableModule } from 'primeng/table';
import { DialogModule } from 'primeng/dialog';
import { ToastModule } from 'primeng/toast';

import { OccurrencesService } from '../occurrences.service';
import { Occurrence, Aircraft, FlightRules, OperationType, MeteologicalCondition } from '../../../../util/@types';
import { CategoriesService } from '../../../admin/categories/categories.service';
import { TooltipModule } from 'primeng/tooltip';
import { FlighPhase } from '../../../../public-form/models/enums';
import { AircraftsService } from '../../../admin/aicrafts/aircrafts.service';
import { AutoCompleteModule } from 'primeng/autocomplete';

@Component({
  selector: 'app-edit-occurrence',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonModule,
    InputTextModule,
    DropdownModule,
    TableModule,
    DialogModule,
    ToastModule,
    TooltipModule,
    AutoCompleteModule
  ],
  providers: [MessageService],
  templateUrl: './edit.component.html',
})
export class EditOccurrenceComponent implements OnInit {
  occurrenceForm!: FormGroup;
  occurrence!: Occurrence;
  loading: boolean = true;

  // Dropdown options
  typeOptions = [
    { label: 'Major Accident', value: 'MAJOR_ACCIDENT' },
    { label: 'Accident', value: 'ACCIDENT' },
    { label: 'Serious Incident', value: 'SERIOUS_INCIDENT' },
    { label: 'Incident to be Investigated', value: 'INCIDENT_TO_BE_INVESTIGATED' },
    { label: 'Incident', value: 'INCIDENT' },
    { label: 'Abnormal Occurrence', value: 'ABNORMAL_OCCURRENCE' },
  ];

  categoryOptions: { label: string, value: string, explanation: string }[] = [];

  statusOptions = [
    { label: 'Open', value: 'OPEN' },
    { label: 'Under Investigation', value: 'UNDER_INVESTIGATION' },
    { label: 'Closed', value: 'CLOSED' }
  ];

  // Enum Options
  flightRulesOptions = Object.values(FlightRules);
  operationTypeOptions = Object.entries(OperationType).map(([key, value]) => ({ key, value }));;
  meteologicalConditionOptions = Object.values(MeteologicalCondition);
  flightPhaseOptions = Object.entries(FlighPhase).map(([key, value]) => ({ key, value }));

  displayAircraftDialog: boolean = false;
  selectedAircraft: Aircraft | null = null;
  aircraftForm!: FormGroup;
  involvedAircraft: Aircraft[] = [];

  activeExplanation: string | null = null;

  filteredManufacturers: string[] = [];
  filteredModels: string[] = [];

  showExplanation(item: any) {
    this.activeExplanation = item.explanation;
  }

  hideExplanation() {
    this.activeExplanation = null;
  }

  constructor(
    private occurrenceService: OccurrencesService,
    private route: ActivatedRoute,
    private router: Router,
    private fb: FormBuilder,
    private messageService: MessageService,
    private categoriesService: CategoriesService,
    private aircraftService: AircraftsService
  ) { }

  ngOnInit(): void {
    this.initializeForms();
    this.loadOccurrence();
    this.loadCategories()

    this.aircraftForm.get('manufacturer')?.valueChanges.subscribe(manufacturer => {
      if (manufacturer) {
        this.loadModelsByManufacturer(manufacturer);
      }
    });
  }

  initializeForms() {
    this.occurrenceForm = this.fb.group({
      referenceNumber: ['',],
      type: ['', Validators.required],
      occurrenceCategory_id: ['', Validators.required],
      status: [''],
      reporterName: ['', Validators.required],
      reporterEmail: ['', [Validators.required, Validators.email]],
      reporterPhone: [''],
      pilotInCommandName: [''],
      pilotInCommandEmail: ['', Validators.email],
      pilotInCommandPhone: [''],
      occurrenceTime: [null, Validators.required],
      operationType: [null],
      flightRules: [null],
      meteologicalCondition: [''],
      occurrenceLocation: [''],
      latitude: [''],
      longitude: [''],
      flightPhase: [''],
      generalWeatherConditions: [''],
      skyCoverage: [''],
      groundPeopleInjured: [''],
      groundPeoplePerished: ['']
    });

    this.aircraftForm = this.fb.group({
      manufacturer: ['',],
      model: ['', Validators.required],
      registrationMark: [''],
      operator: [''],
      operatorNationality: [''],
      lastDeparturePoint: [''],
      intendedLandingPoint: [''],
      intendedLandingDateTime: [''],
      crewOnBoard: [''],
      crewInjured: [''],
      crewPerished: [''],
      passengersOnBoard: [''],
      passengersInjured: [''],
      passengersPerished: ['']
    });
  }

  async searchManufacturer(event: any) {
    try {
      const response = await this.aircraftService.searchManufacturers(event.query);
      this.filteredManufacturers = response.data.data;
    } catch (error) {
      this.handleError('Failed to search manufacturers', error);
    }
  }


  async loadModelsByManufacturer(manufacturer: string) {
    try {
      const response = await this.aircraftService.searchModels('', manufacturer);
      this.filteredModels = response.data.data.map((model: any) => ({
        label: model,
        value: model
      }));
    } catch (error) {
      this.handleError('Failed to load models', error);
      this.filteredModels = [];
    }
  }

  async searchModel(event?: any) {
    const manufacturer = this.aircraftForm.get('manufacturer')?.value;
    if (!manufacturer) {
      this.messageService.add({ severity: 'warn', detail: 'Please select manufacturer first' });
      this.filteredModels = [];
      return;
    }

    try {
      const response = await this.aircraftService.searchModels(event?.query || '', manufacturer);
      this.filteredModels = response.data.data.map((model: any) => ({
        label: model,
        value: model
      }));
    } catch (error) {
      this.handleError('Failed to search models', error);
      this.filteredModels = [];
    }
  }

  async loadCategories() {
    try {
      const response = await this.categoriesService.getCategories();
      this.categoryOptions = response.data.map((item) => {

        return { label: item.category, value: item.id, description: item.description, explanation: item.explanation }
      });
    } catch (error) {
      this.handleError('Failed to load contacts', error);
    }
  }

  async loadOccurrence() {
    try {
      this.loading = true;
      const id = this.route.snapshot.paramMap.get('id') || '';

      if (!id) {
        throw new Error('No occurrence ID provided');
      }

      this.occurrence = await this.occurrenceService.getOccurrence(id);

      this.occurrenceForm.patchValue({
        ...this.occurrence,
        occurrenceTime: this.formatDateForInput(this.occurrence.occurrenceTime ?? undefined),
        occurrenceCategory: {
          category: this.occurrence.occurrenceCategory?.category || ''
        }
      });

      this.involvedAircraft = this.occurrence.involvedAircraft || [];

      this.loading = false;
    } catch (error) {
      this.handleError('Failed to load occurrence details', error);
      this.loading = false;
    }
  }

  // Getter methods to safely access form controls
  get typeControl() {
    return this.occurrenceForm.get('type');
  }

  get categoryControl() {
    return this.occurrenceForm.get('occurrenceCategory_id');
  }

  get statusControl() {
    return this.occurrenceForm.get('status');
  }

  formatDateForInput(date: Date | string | undefined): string {
    if (!date) return '';
    const d = new Date(date);
    return new Date(d.getTime() - d.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
  }

  openAircraftDialog(aircraft?: Aircraft) {
    if (aircraft) {
      this.selectedAircraft = aircraft;
      const formattedAircraft = {
        ...aircraft,
        intendedLandingDateTime: this.formatDateForInput(aircraft.intendedLandingDateTime ?? undefined)
      };
      this.aircraftForm.patchValue(formattedAircraft);
    } else {
      this.selectedAircraft = null;
      this.aircraftForm.reset();
    }
    this.displayAircraftDialog = true;
  }

  saveAircraft() {
    if (this.aircraftForm.valid) {
      const aircraftData = this.aircraftForm.value;

      if (this.selectedAircraft) {
        // Update existing aircraft
        const index = this.involvedAircraft.findIndex(a =>
          a.registrationMark === this.selectedAircraft?.registrationMark
        );
        if (index !== -1) {
          this.involvedAircraft[index] = aircraftData;
        }
      } else {
        // Add new aircraft
        this.involvedAircraft.push(aircraftData);
      }

      this.displayAircraftDialog = false;
    }
  }

  removeAircraft(aircraft: Aircraft) {
    this.involvedAircraft = this.involvedAircraft.filter(a =>
      a.registrationMark !== aircraft.registrationMark
    );
  }

  async getRefNumber() {
    try {
      const occurrence = await this.occurrenceService.generateRefNumber(this.occurrence.id);
      this.messageService.add({
        severity: 'success',
        summary: 'Reference number generated',
        detail: 'Reference number has been successfully generated.'
      });
      this.loadOccurrence()
    } catch (error) {
      this.handleError('Failed to generate refence number', error);
    }
  }

  async saveOccurrence() {
    console.log(this.occurrenceForm.value)
    if (this.occurrenceForm.valid) {
      try {
        const formData = {
          ...this.occurrenceForm.value,
          involvedAircraft: this.involvedAircraft
        };

        // Actual update method call
        await this.occurrenceService.updateOccurrence(this.occurrence.id, formData);

        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Occurrence updated successfully'
        });

        if (this.occurrence.referenceNumber === null) {
          this.getRefNumber()
        } else {
          this.loadOccurrence();
        }
      } catch (error) {
        this.handleError('Failed to save occurrence', error);
      }
    } else {
      this.markFormGroupTouched(this.occurrenceForm)
      this.messageService.add({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Please fill in all required fields'
      });
    }
  }

  private handleError(message: string, error: any) {
    console.error(error);
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: error.response?.data?.message || error.message || message
    });
  }

  private markFormGroupTouched(formGroup: FormGroup | FormArray): void {
    Object.values(formGroup.controls).forEach(control => {
      if (control instanceof FormGroup || control instanceof FormArray) {
        this.markFormGroupTouched(control);
      } else {
        control.markAsTouched();
      }
    });
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.occurrenceForm.get(fieldName);
    return field ? field.invalid && (field.dirty || field.touched) : false;
  }

  getErrorMessage(fieldName: string): string {
    const control = this.occurrenceForm.get(fieldName);
    if (control?.errors) {
      if (control.errors['required']) return 'This field is required';
      if (control.errors['email']) return 'Invalid email format';
    }
    return '';
  }
}