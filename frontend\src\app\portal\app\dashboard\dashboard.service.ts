import { Injectable } from "@angular/core";
import { AxiosService } from "../../../util/axios/axios.service";

@Injectable({
    providedIn: 'root'
})
export class DashboardService {
    constructor(
        private axiosService: AxiosService
    ) { }

    async getAll() {
        try {
            const response = await this.axiosService.axios.get('/dashboard');
            return response.data
        } catch (error) {
            throw error;
        }
    }

}