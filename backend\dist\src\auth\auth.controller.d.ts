import { AuthenticationService } from './auth.service';
import { LoginDTO, Verify2faDto } from './dto';
export declare class AuthenticationController {
    private authService;
    constructor(authService: AuthenticationService);
    login(data: LoginDTO, req: Request): Promise<import("../utils/@types").ApiResponse<any>>;
    verify2fa(data: Verify2faDto): Promise<import("../utils/@types").ApiResponse<{
        token: string;
        user: import("@prisma/client").User;
    }>>;
}
