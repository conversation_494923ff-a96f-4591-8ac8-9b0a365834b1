import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SettingsService } from './settings.service';
import { CreateContactInfoDto, CreateOccurrenceCategoryDto, UpdateContactInfoDto, UpdateOccurrenceCategoryDto } from './dto';
import { RolesGuard } from 'src/utils/guards/role.guard';
import { Role } from '@prisma/client';
import { Roles } from 'src/utils/decorators/role.decorator';

@ApiTags("Admin Settings")
@UseGuards(RolesGuard)
@Controller('settings')
export class SettingsController {

    constructor(private settingsService: SettingsService) { }

    @Roles(Role.ADMIN)
    @Post("contacts")
    async createContactInfo(@Body() createDto: CreateContactInfoDto) {
        return this.settingsService.createContactInfo(createDto);
    }

    @Get("contacts")
    async getAllContactInfo() {
        return this.settingsService.getAllContactInfo();
    }

    @Get('contacts/:id')
    async getContactInfoById(@Param('id') id: string) {
        return this.settingsService.getContactInfoById(id);
    }

    @Roles(Role.ADMIN)
    @Patch('contacts/:id')
    async updateContactInfo(
        @Param('id') id: string,
        @Body() updateDto: UpdateContactInfoDto,
    ) {
        return this.settingsService.updateContactInfo(id, updateDto);
    }

    @Roles(Role.ADMIN)
    @Delete('contact/:id')
    async deleteContactInfo(@Param('id') id: string) {
        return this.settingsService.deleteContactInfo(id);
    }

    @Roles(Role.ADMIN)
    @Post("category")
    async createOccurrenceCategory(@Body() createDto: CreateOccurrenceCategoryDto) {
        return this.settingsService.createOccurrenceCategory(createDto);
    }

    @Get("category")
    async getAllOccurrenceCategories() {
        return this.settingsService.getAllOccurrenceCategories();
    }

    @Get('category/:id')
    async getOccurrenceCategoryById(@Param('id') id: string) {
        return this.settingsService.getOccurrenceCategoryById(id);
    }

    @Roles(Role.ADMIN)
    @Patch('category/:id')
    async updateOccurrenceCategory(
        @Param('id') id: string,
        @Body() updateDto: UpdateOccurrenceCategoryDto,
    ) {
        return this.settingsService.updateOccurrenceCategory(id, updateDto);
    }

    @Roles(Role.ADMIN)
    @Delete('category/:id')
    async deleteOccurrenceCategory(@Param('id') id: string) {
        return this.settingsService.deleteOccurrenceCategory(id);
    }
}
