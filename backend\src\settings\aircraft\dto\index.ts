import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateAircraftDto {
  @ApiProperty({ description: 'Full model name of the aircraft' })
  @IsString()
  modelFullName: string;

  @ApiProperty({ description: 'Aircraft description' })
  @IsString()
  description: string;

  @ApiPropertyOptional({ description: 'Wake turbulence category' })
  @IsOptional()
  @IsString()
  wtc?: string;

  @ApiPropertyOptional({ description: 'Weight group' })
  @IsOptional()
  @IsString()
  wtg?: string;

  @ApiProperty({ description: 'Aircraft designator' })
  @IsString()
  designator: string;

  @ApiProperty({ description: 'Manufacturer code' })
  @IsString()
  manufacturerCode: string;

  @ApiProperty({ description: 'Description of aircraft type' })
  @IsString()
  aircraftDescription: string;

  @ApiPropertyOptional({ description: 'Number of engines' })
  @IsOptional()
  @IsNumber()
  engineCount?: number;

  @ApiPropertyOptional({ description: 'Type of engine' })
  @IsOptional()
  @IsString()
  engineType?: string;
}

export class UpdateAircraftDto extends PartialType(CreateAircraftDto) { }

export class SearchAircraftDto {
  @ApiPropertyOptional({ description: 'Search by full model name of the aircraft' })
  @IsOptional()
  @IsString()
  modelFullName?: string;

  @ApiPropertyOptional({ description: 'Search by aircraft designator' })
  @IsOptional()
  @IsString()
  designator?: string;

  @ApiPropertyOptional({ description: 'Search by manufacturer code' })
  @IsOptional()
  @IsString()
  manufacturerCode?: string;
}