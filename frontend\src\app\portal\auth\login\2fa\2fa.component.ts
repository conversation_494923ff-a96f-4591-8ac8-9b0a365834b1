import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink, RouterModule } from '@angular/router';
import { MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import { AuthService } from '../../auth.service';

@Component({
  selector: 'app-verify-2fa',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ToastModule,
    RouterModule
  ],
  templateUrl: './2fa.component.html',
  providers: [MessageService],
})
export class Verify2faComponent implements OnInit{
  verifyForm!: FormGroup;
  showPassword = false;
  loading = false;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private messageService: MessageService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    
  }
  ngOnInit(): void {
    this.verifyForm = this.fb.group({
      code: ['', [Validators.required, Validators.minLength(6), Validators.maxLength(6)]],
    });
  }

  togglePassword(): void {
    this.showPassword = !this.showPassword;
  }

  async onSubmit(): Promise<void> {
    if (this.verifyForm.valid) {
      this.loading = true;
      const { code } = this.verifyForm.value;
      const id = this.route.snapshot.queryParamMap.get('id')

      if(!id){
        alert('Id not provided')
        return
      }

      try {
        await this.authService.verify2fa(id, code);
        
        const userInfo = this.authService.getUserInfo();
        this.messageService.add({
          severity: 'success',
          summary: 'Login Successful',
          detail: `Welcome, ${userInfo?.name}!`,
        });

        const returnUrl = this.route.snapshot.queryParamMap.get('returnUrl')
        console.log(returnUrl)
        
        if(returnUrl){
          this.router.navigate([returnUrl])
        }else{
          this.router.navigate([
            this.authService.getRole() === 'ADMIN' ? 'admin' : 
            this.authService.getRole() === 'INVESTIGATOR' ? 'portal' : 'dg'
          ]);
        }

      } catch (error: any) {
        console.log(error.response.data.message)
        const errorMessage = error.response.data.message || error.response.data.message[0] || error.message 
        this.messageService.add({
          severity: 'error',
          summary: 'Verification Failed',
          detail: errorMessage,
        });
      } finally {
        this.loading = false;
      }
    } else {
      Object.keys(this.verifyForm.controls).forEach((key) => {
        const control = this.verifyForm.get(key);
        if (control?.invalid) {
          control.markAsTouched();
        }
      });
      this.messageService.add({
        severity: 'warn',
        summary: 'Form Incomplete',
        detail: 'Please fill in all required fields.',
      });
    }
  }
  
}
