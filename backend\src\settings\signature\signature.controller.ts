import { Controller, Get, Post, Body, Patch, Param, Delete, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiResponse, ApiOperation } from '@nestjs/swagger';
import { SignatureService } from './signature.service';
import { CreateSignatureDto, UpdateSignatureDto } from './dto';


@ApiTags('Signatures')
@Controller('signatures')
export class SignatureController {
  constructor(private readonly signatureService: SignatureService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new signature' })
  @ApiResponse({ status: 201, description: 'Signature created successfully.' })
  async create(@Body() createSignatureDto: CreateSignatureDto) {
    return this.signatureService.create(createSignatureDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all signatures' })
  @ApiResponse({ status: 200, description: 'List of all signatures.' })
  async findAll() {
    return this.signatureService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a signature by ID' })
  @ApiResponse({ status: 200, description: 'Signature fetched successfully.' })
  async findOne(@Param('id') id: string) {
    return this.signatureService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a signature by ID' })
  @ApiResponse({ status: 200, description: 'Signature updated successfully.' })
  async update(@Param('id') id: string, @Body() updateSignatureDto: UpdateSignatureDto) {
    return this.signatureService.update(id, updateSignatureDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a signature by ID' })
  @ApiResponse({ status: 200, description: 'Signature deleted successfully.' })
  async remove(@Param('id') id: string) {
    return this.signatureService.remove(id);
  }
}
