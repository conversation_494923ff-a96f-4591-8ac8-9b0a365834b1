import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { UsersService } from './users.service';
import { MessageService } from 'primeng/api';
import { ConfirmationService } from 'primeng/api';
import { User } from '../../../util/@types';
import { AuthService } from '../../auth/auth.service';

@Component({
  selector: 'app-users',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    TableModule,
    DialogModule,
    ToastModule,
    ConfirmDialogModule
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './users.component.html',
})
export class UsersComponent implements OnInit {
  users: User[] = [];
  totalUsers: number = 0;
  currentPage: number = 1;
  pageSize: number = 10;

  searchQuery: string = ''

  userModalVisible: boolean = false;
  isEditMode: boolean = false;
  
  currentUser: Partial<User> = {};
  me!: User | null
  profilePictureFile: File | undefined = undefined
  profilePicturePreview: string | null = null

  roleOptions = [
    { label: 'Investigator', value: 'INVESTIGATOR' },
    { label: 'DG', value: 'DG' },
    { label: 'Acting DG', value: 'ACTING_DG' },
    { label: 'Admin', value: 'ADMIN' }
  ];

  constructor(
    private usersService: UsersService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.loadUsers();
    this.me = this.authService.getUserInfo()
  }

  onFileSelected(event: Event) {
    const file = (event.target as HTMLInputElement).files?.[0];
    this.profilePictureFile = file
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        this.profilePicturePreview = reader.result as string;
        console.log(this.profilePicturePreview)
      };
      reader.readAsDataURL(file);
    }
  }

  async loadUsers(page: number = 1) {
    try {
      const response = await this.usersService.getUsers(page, this.pageSize);
      console.log(response)
      this.users = response.data;
      this.totalUsers = response.total;
    } catch (error) {
      this.handleError('Failed to load users', error);
    }
  }

  onPageChange(event: any) {
    this.currentPage = event.page + 1;
    this.pageSize = event.rows;
    this.loadUsers(this.currentPage);
  }

  openUserModal(mode: 'create' | 'edit', user?: User) {
    this.profilePictureFile = undefined
    this.profilePicturePreview =  null
    this.isEditMode = mode === 'edit';
    this.currentUser = this.isEditMode 
      ? { ...user } 
      : { name: '', email: '', role: '' };
    this.userModalVisible = true;
  }

  async search(){
    try {
      if(this.searchQuery === '') {
        this.loadUsers
        return
      }
      const resp = await this.usersService.search(this.searchQuery, this.currentPage)
      this.users = resp.data
    } catch (error) {
      this.handleError('search failed', error);
    }
  }

  async saveUser() {
    try {
      if (this.isEditMode) {
        let imageUrl
        if(this.profilePictureFile){
          const formData = new FormData()
          formData.append('file', this.profilePictureFile)
          imageUrl = await this.usersService.uploadProfilePicture(formData)
          console.log(imageUrl)
        }
        
        await this.usersService.updateUser(
          this.currentUser.id!,
          this.currentUser.name!, 
          this.currentUser.email!, 
          this.currentUser.telephone!,
          this.currentUser.role!,
          imageUrl??this.currentUser.profilePicture
        );
        this.messageService.add({
          severity: 'success', 
          summary: 'User Updated', 
          detail: 'User has been successfully updated.'
        });
      } else {
        let imageUrl
        if(this.profilePictureFile){
          const formData = new FormData()
          formData.append('file', this.profilePictureFile)
          imageUrl = await this.usersService.uploadProfilePicture(formData)
          console.log(imageUrl)
        }
    
        await this.usersService.createUser(
          this.currentUser.name!, 
          this.currentUser.email!, 
          this.currentUser.telephone!,
          this.currentUser.role!,
          imageUrl??this.currentUser.profilePicture
        );
        this.messageService.add({
          severity: 'success', 
          summary: 'User Created', 
          detail: `New user has been successfully created. An email was sent to ${this.currentUser.name} for account activation instructions`,
          life: 1000
        });
      }
      
      this.userModalVisible = false;
      this.loadUsers(this.currentPage);
    } catch (error) {
      this.handleError('Failed to save user', error);
    }
  }

  confirmDelete(user: User) {
    this.confirmationService.confirm({
      message: `Are you sure you want to delete user ${user.name}?`,
      header: 'Confirm Deletion',
      icon: 'pi pi-info-circle',
      acceptIcon:"none",
      rejectIcon:"none",
      acceptButtonStyleClass:"p-button-danger p-button-text",
      rejectButtonStyleClass:"p-button-text p-button-text mr-4", 
      accept: () => this.deleteUser(user.id!)
    });
  }

  confirmDeactivate(user: User){
    this.confirmationService.confirm({
      message: `Are you sure you want to deactivate user ${user.name}?`,
      header: 'Confirm Deactivation',
      icon: 'pi pi-info-circle',
      acceptIcon:"none",
      rejectIcon:"none",
      acceptButtonStyleClass:"p-button-danger p-button-text",
      rejectButtonStyleClass:"p-button-text p-button-text mr-4", 
      accept: () => this.deactivate(user.id!)
    });
  }
  confirmActivate(user: User){
    this.confirmationService.confirm({
      message: `Are you sure you want to activate user ${user.name}?`,
      header: 'Confirm Activation',
      icon: 'pi pi-info-circle',
      acceptIcon:"none",
      rejectIcon:"none",
      acceptButtonStyleClass:"p-button-danger p-button-text",
      rejectButtonStyleClass:"p-button-text p-button-text mr-4", 
      accept: () => this.activate(user.id!)
    });
  }

  async activate(userId: string){
    try {
      await this.usersService.activate(userId);
      this.messageService.add({
        severity: 'success', 
        summary: 'User Activated', 
        detail: 'User has been successfully activated.'
      });
      this.loadUsers(this.currentPage);
    } catch (error) {
      this.handleError('Failed to activate user', error);
    }
  }
  async deactivate(userId: string){
    try {
      await this.usersService.deactivate(userId);
      this.messageService.add({
        severity: 'success', 
        summary: 'User Deactivated', 
        detail: 'User has been successfully deactivated.'
      });
      this.loadUsers(this.currentPage);
      
    } catch (error) {
      this.handleError('Failed to deactivate user', error);
    }
  }

  async deleteUser(userId: string) {
    try {
      await this.usersService.deleteUser(userId);
      this.messageService.add({
        severity: 'success', 
        summary: 'User Deleted', 
        detail: 'User has been successfully deleted.'
      });
      this.loadUsers(this.currentPage);
    } catch (error) {
      this.handleError('Failed to delete user', error);
    }
  }

  private handleError(message: string, error: any) {
    console.error(error);
    this.messageService.add({
      severity: 'error', 
      summary: 'Error', 
      detail: error.response.data.message || error.response.data.message[0] || error.message
    });
  }
}