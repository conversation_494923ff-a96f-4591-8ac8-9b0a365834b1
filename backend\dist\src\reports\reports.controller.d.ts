import { ApproveDTO, CreateReportDto, UpdateReportDto } from './dto';
import { ReportsService } from './reports.service';
export declare class ReportsController {
    private readonly reportsService;
    constructor(reportsService: ReportsService);
    create(createReportDto: CreateReportDto): Promise<import("../utils/@types").ApiResponse<{
        id: string;
        status: import("@prisma/client").$Enums.ReportStatus | null;
        createdAt: Date;
        updatedAt: Date;
        groundPeoplePerished: number | null;
        groundPeopleInjured: number | null;
        latitude: string | null;
        longitude: string | null;
        referenceNumber: string;
        addressedTo: import("@prisma/client").$Enums.AddressedTo[];
        stateOfRegistry: string | null;
        stateOfDesign: string | null;
        stateOfManufacturer: string | null;
        stateOfOperator: string | null;
        occurrenceType: import("@prisma/client").$Enums.ReportOccurrenceType;
        occurrenceDate: Date | null;
        occurrenceTimeUTC: Date | null;
        occurrenceTimeLocal: Date | null;
        position: string | null;
        occurrenceDescription: string;
        damageExtent: string;
        dangerousGoodsPresent: boolean;
        dangerousGoodsDescription: string | null;
        aircraftInfo: import("@prisma/client/runtime/library").JsonValue | null;
        investigationExtent: string | null;
        investigationDelegation: string | null;
        areaCharacteristics: string | null;
        accessRequirements: string | null;
        originatingAuthority: string;
        investigatorName: string;
        investigatorMobile: string;
        investigatorEmail: string;
        comment: string | null;
        submittedById: string | null;
        approvedById: string | null;
    }>>;
    findAll(): Promise<import("../utils/@types").ApiResponse<{
        id: string;
        status: import("@prisma/client").$Enums.ReportStatus | null;
        createdAt: Date;
        updatedAt: Date;
        groundPeoplePerished: number | null;
        groundPeopleInjured: number | null;
        latitude: string | null;
        longitude: string | null;
        referenceNumber: string;
        addressedTo: import("@prisma/client").$Enums.AddressedTo[];
        stateOfRegistry: string | null;
        stateOfDesign: string | null;
        stateOfManufacturer: string | null;
        stateOfOperator: string | null;
        occurrenceType: import("@prisma/client").$Enums.ReportOccurrenceType;
        occurrenceDate: Date | null;
        occurrenceTimeUTC: Date | null;
        occurrenceTimeLocal: Date | null;
        position: string | null;
        occurrenceDescription: string;
        damageExtent: string;
        dangerousGoodsPresent: boolean;
        dangerousGoodsDescription: string | null;
        aircraftInfo: import("@prisma/client/runtime/library").JsonValue | null;
        investigationExtent: string | null;
        investigationDelegation: string | null;
        areaCharacteristics: string | null;
        accessRequirements: string | null;
        originatingAuthority: string;
        investigatorName: string;
        investigatorMobile: string;
        investigatorEmail: string;
        comment: string | null;
        submittedById: string | null;
        approvedById: string | null;
    }[]>>;
    findOne(id: string): Promise<import("../utils/@types").ApiResponse<{
        id: string;
        status: import("@prisma/client").$Enums.ReportStatus | null;
        createdAt: Date;
        updatedAt: Date;
        groundPeoplePerished: number | null;
        groundPeopleInjured: number | null;
        latitude: string | null;
        longitude: string | null;
        referenceNumber: string;
        addressedTo: import("@prisma/client").$Enums.AddressedTo[];
        stateOfRegistry: string | null;
        stateOfDesign: string | null;
        stateOfManufacturer: string | null;
        stateOfOperator: string | null;
        occurrenceType: import("@prisma/client").$Enums.ReportOccurrenceType;
        occurrenceDate: Date | null;
        occurrenceTimeUTC: Date | null;
        occurrenceTimeLocal: Date | null;
        position: string | null;
        occurrenceDescription: string;
        damageExtent: string;
        dangerousGoodsPresent: boolean;
        dangerousGoodsDescription: string | null;
        aircraftInfo: import("@prisma/client/runtime/library").JsonValue | null;
        investigationExtent: string | null;
        investigationDelegation: string | null;
        areaCharacteristics: string | null;
        accessRequirements: string | null;
        originatingAuthority: string;
        investigatorName: string;
        investigatorMobile: string;
        investigatorEmail: string;
        comment: string | null;
        submittedById: string | null;
        approvedById: string | null;
    }>>;
    submit(id: string, req: Request & {
        user: any;
    }): Promise<import("../utils/@types").ApiResponse<{
        id: string;
        status: import("@prisma/client").$Enums.ReportStatus | null;
        createdAt: Date;
        updatedAt: Date;
        groundPeoplePerished: number | null;
        groundPeopleInjured: number | null;
        latitude: string | null;
        longitude: string | null;
        referenceNumber: string;
        addressedTo: import("@prisma/client").$Enums.AddressedTo[];
        stateOfRegistry: string | null;
        stateOfDesign: string | null;
        stateOfManufacturer: string | null;
        stateOfOperator: string | null;
        occurrenceType: import("@prisma/client").$Enums.ReportOccurrenceType;
        occurrenceDate: Date | null;
        occurrenceTimeUTC: Date | null;
        occurrenceTimeLocal: Date | null;
        position: string | null;
        occurrenceDescription: string;
        damageExtent: string;
        dangerousGoodsPresent: boolean;
        dangerousGoodsDescription: string | null;
        aircraftInfo: import("@prisma/client/runtime/library").JsonValue | null;
        investigationExtent: string | null;
        investigationDelegation: string | null;
        areaCharacteristics: string | null;
        accessRequirements: string | null;
        originatingAuthority: string;
        investigatorName: string;
        investigatorMobile: string;
        investigatorEmail: string;
        comment: string | null;
        submittedById: string | null;
        approvedById: string | null;
    }>>;
    approve(id: string, data: ApproveDTO, req: Request & {
        user: any;
    }): Promise<import("../utils/@types").ApiResponse<{
        id: string;
        status: import("@prisma/client").$Enums.ReportStatus | null;
        createdAt: Date;
        updatedAt: Date;
        groundPeoplePerished: number | null;
        groundPeopleInjured: number | null;
        latitude: string | null;
        longitude: string | null;
        referenceNumber: string;
        addressedTo: import("@prisma/client").$Enums.AddressedTo[];
        stateOfRegistry: string | null;
        stateOfDesign: string | null;
        stateOfManufacturer: string | null;
        stateOfOperator: string | null;
        occurrenceType: import("@prisma/client").$Enums.ReportOccurrenceType;
        occurrenceDate: Date | null;
        occurrenceTimeUTC: Date | null;
        occurrenceTimeLocal: Date | null;
        position: string | null;
        occurrenceDescription: string;
        damageExtent: string;
        dangerousGoodsPresent: boolean;
        dangerousGoodsDescription: string | null;
        aircraftInfo: import("@prisma/client/runtime/library").JsonValue | null;
        investigationExtent: string | null;
        investigationDelegation: string | null;
        areaCharacteristics: string | null;
        accessRequirements: string | null;
        originatingAuthority: string;
        investigatorName: string;
        investigatorMobile: string;
        investigatorEmail: string;
        comment: string | null;
        submittedById: string | null;
        approvedById: string | null;
    }>>;
    revert(id: string, data: ApproveDTO, req: Request & {
        user: any;
    }): Promise<import("../utils/@types").ApiResponse<{
        id: string;
        status: import("@prisma/client").$Enums.ReportStatus | null;
        createdAt: Date;
        updatedAt: Date;
        groundPeoplePerished: number | null;
        groundPeopleInjured: number | null;
        latitude: string | null;
        longitude: string | null;
        referenceNumber: string;
        addressedTo: import("@prisma/client").$Enums.AddressedTo[];
        stateOfRegistry: string | null;
        stateOfDesign: string | null;
        stateOfManufacturer: string | null;
        stateOfOperator: string | null;
        occurrenceType: import("@prisma/client").$Enums.ReportOccurrenceType;
        occurrenceDate: Date | null;
        occurrenceTimeUTC: Date | null;
        occurrenceTimeLocal: Date | null;
        position: string | null;
        occurrenceDescription: string;
        damageExtent: string;
        dangerousGoodsPresent: boolean;
        dangerousGoodsDescription: string | null;
        aircraftInfo: import("@prisma/client/runtime/library").JsonValue | null;
        investigationExtent: string | null;
        investigationDelegation: string | null;
        areaCharacteristics: string | null;
        accessRequirements: string | null;
        originatingAuthority: string;
        investigatorName: string;
        investigatorMobile: string;
        investigatorEmail: string;
        comment: string | null;
        submittedById: string | null;
        approvedById: string | null;
    }>>;
    update(id: string, updateReportDto: UpdateReportDto): Promise<import("../utils/@types").ApiResponse<{
        id: string;
        status: import("@prisma/client").$Enums.ReportStatus | null;
        createdAt: Date;
        updatedAt: Date;
        groundPeoplePerished: number | null;
        groundPeopleInjured: number | null;
        latitude: string | null;
        longitude: string | null;
        referenceNumber: string;
        addressedTo: import("@prisma/client").$Enums.AddressedTo[];
        stateOfRegistry: string | null;
        stateOfDesign: string | null;
        stateOfManufacturer: string | null;
        stateOfOperator: string | null;
        occurrenceType: import("@prisma/client").$Enums.ReportOccurrenceType;
        occurrenceDate: Date | null;
        occurrenceTimeUTC: Date | null;
        occurrenceTimeLocal: Date | null;
        position: string | null;
        occurrenceDescription: string;
        damageExtent: string;
        dangerousGoodsPresent: boolean;
        dangerousGoodsDescription: string | null;
        aircraftInfo: import("@prisma/client/runtime/library").JsonValue | null;
        investigationExtent: string | null;
        investigationDelegation: string | null;
        areaCharacteristics: string | null;
        accessRequirements: string | null;
        originatingAuthority: string;
        investigatorName: string;
        investigatorMobile: string;
        investigatorEmail: string;
        comment: string | null;
        submittedById: string | null;
        approvedById: string | null;
    }>>;
    remove(id: string): Promise<import("../utils/@types").ApiResponse<{
        id: string;
        status: import("@prisma/client").$Enums.ReportStatus | null;
        createdAt: Date;
        updatedAt: Date;
        groundPeoplePerished: number | null;
        groundPeopleInjured: number | null;
        latitude: string | null;
        longitude: string | null;
        referenceNumber: string;
        addressedTo: import("@prisma/client").$Enums.AddressedTo[];
        stateOfRegistry: string | null;
        stateOfDesign: string | null;
        stateOfManufacturer: string | null;
        stateOfOperator: string | null;
        occurrenceType: import("@prisma/client").$Enums.ReportOccurrenceType;
        occurrenceDate: Date | null;
        occurrenceTimeUTC: Date | null;
        occurrenceTimeLocal: Date | null;
        position: string | null;
        occurrenceDescription: string;
        damageExtent: string;
        dangerousGoodsPresent: boolean;
        dangerousGoodsDescription: string | null;
        aircraftInfo: import("@prisma/client/runtime/library").JsonValue | null;
        investigationExtent: string | null;
        investigationDelegation: string | null;
        areaCharacteristics: string | null;
        accessRequirements: string | null;
        originatingAuthority: string;
        investigatorName: string;
        investigatorMobile: string;
        investigatorEmail: string;
        comment: string | null;
        submittedById: string | null;
        approvedById: string | null;
    }>>;
}
