"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const auth_module_1 = require("./auth/auth.module");
const config_1 = require("@nestjs/config");
const occurrence_module_1 = require("./occurrence/occurrence.module");
const users_module_1 = require("./users/users.module");
const settings_module_1 = require("./settings/settings.module");
const prisma_service_1 = require("./prisma/prisma.service");
const mail_module_1 = require("./utils/mail/mail.module");
const sms_module_1 = require("./utils/sms/sms.module");
const documents_module_1 = require("./documents/documents.module");
const reports_module_1 = require("./reports/reports.module");
const dashboard_module_1 = require("./dashboard/dashboard.module");
const logs_middleware_1 = require("./logs/logs.middleware");
const logs_module_1 = require("./logs/logs.module");
let AppModule = class AppModule {
    configure(consumer) {
        consumer
            .apply(logs_middleware_1.LoggerMiddleware)
            .forRoutes('*');
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
            }),
            auth_module_1.AuthenticationModule,
            occurrence_module_1.OccurrenceModule,
            users_module_1.UsersModule,
            settings_module_1.SettingsModule,
            mail_module_1.MailModule,
            sms_module_1.SmsModule,
            documents_module_1.DocumentsModule,
            reports_module_1.ReportsModule,
            dashboard_module_1.DashboardModule,
            logs_module_1.LogsModule
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService, prisma_service_1.PrismaService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map