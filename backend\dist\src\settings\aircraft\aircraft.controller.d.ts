import { AircraftService } from './aircraft.service';
import { CreateAircraftDto, UpdateAircraftDto } from './dto';
import { ApiResponse } from 'src/utils/@types';
import { SearchDto } from 'src/users/dto';
export declare class AircraftController {
    private readonly aircraftService;
    constructor(aircraftService: AircraftService);
    create(createAircraftDto: CreateAircraftDto): Promise<ApiResponse<any>>;
    getAll(page: string, limit: string): Promise<ApiResponse<any>>;
    Ï: any;
    findOne(id: string): Promise<ApiResponse<any>>;
    update(id: string, updateAircraftDto: UpdateAircraftDto): Promise<ApiResponse<any>>;
    remove(id: string): Promise<ApiResponse<any>>;
    search(searchDto: SearchDto): Promise<ApiResponse<any>>;
    searchManufacturers(query: string): Promise<{
        data: string[];
    }>;
    searchModels(query: string, manufacturer: string): Promise<{
        data: string[];
    }>;
}
