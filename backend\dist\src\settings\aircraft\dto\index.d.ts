export declare class CreateAircraftDto {
    modelFullName: string;
    description: string;
    wtc?: string;
    wtg?: string;
    designator: string;
    manufacturerCode: string;
    aircraftDescription: string;
    engineCount?: number;
    engineType?: string;
}
declare const UpdateAircraftDto_base: import("@nestjs/common").Type<Partial<CreateAircraftDto>>;
export declare class UpdateAircraftDto extends UpdateAircraftDto_base {
}
export declare class SearchAircraftDto {
    modelFullName?: string;
    designator?: string;
    manufacturerCode?: string;
}
export {};
