import { Injectable } from "@angular/core";
import { Contact, OccurrenceCategory, PaginatedResponse } from "../../../util/@types";
import { AxiosService } from "../../../util/axios/axios.service";

@Injectable({
    providedIn: 'root'
})
export class CategoriesService{
    constructor(private axiosService: AxiosService) {}

    async getCategories(page: number = 1, limit: number = 10): Promise<PaginatedResponse<OccurrenceCategory>> {
      try {
        const response = await this.axiosService.axios.get('/settings/category', {
          params: { page, limit }
        });
        return response.data as PaginatedResponse<OccurrenceCategory>;
      } catch (error) {
        throw error;
      }
    }
  
    async getCategory(id: string): Promise<OccurrenceCategory> {
      try {
        const response = await this.axiosService.axios.get(`/settings/category/${id}`);
        return response.data.data as OccurrenceCategory;
      } catch (error) {
        throw error;
      }
    }
  
    async createCategory(category: string, description: string, explanation?: string): Promise<OccurrenceCategory>{
      try {
        const response = await this.axiosService.axios.post('/settings/category', {
          category, 
          description,
          explanation
        })
        return response.data.data as OccurrenceCategory
      } catch (error) {
        throw error
      }
    }
  
    async updateCategory(id: string, category: string, description: string, explanation?: string): Promise<OccurrenceCategory>{
      try {
        const response = await this.axiosService.axios.patch(`/settings/category/${id}`, {
          category,
          description, 
          explanation
        })
        return response.data.data as OccurrenceCategory
      } catch (error) {
        throw error
      }
    }
  
    async deleteCategory(id: string): Promise<OccurrenceCategory> {
      try {
        const response = await this.axiosService.axios.delete(`/settings/category/${id}`)
        return response.data.data as OccurrenceCategory
      } catch (error) {
        throw error
      }
    }
}