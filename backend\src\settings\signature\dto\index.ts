import { ApiProperty, PartialType } from "@nestjs/swagger";
import { IsNotEmpty, IsString } from "class-validator";

export class CreateSignatureDto {
    @ApiProperty({ description: 'The unique name of the signature' })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: 'The URL of the signature image' })
    @IsString()
    @IsNotEmpty()
    imageUrl: string;
}

export class UpdateSignatureDto extends PartialType(CreateSignatureDto) { }
