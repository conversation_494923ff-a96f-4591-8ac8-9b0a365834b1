import { BadRequestException, Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateDocumentDto, UpdateDocumentDto } from './dto';
import { ApiResponse } from 'src/utils/@types';

@Injectable()
export class DocumentsService {
    constructor(
        private prisma: PrismaService
    ) { }

    async createDocument(data: CreateDocumentDto) {
        const occurence = await this.prisma.occurrence.findUnique({
            where: {
                id: data.occurrenceId
            }
        })

        if (!occurence) { throw new BadRequestException('Occurrence does not exist') }

        try {
            const newDoc = await this.prisma.document.create({
                data
            })

            return new ApiResponse(true, 'Document created', newDoc)
        } catch (error) {
            console.log(error)
            throw new InternalServerErrorException()
        }
    }

    async getDocuments() {
        const docs = await this.prisma.document.findMany({
            orderBy: {
                createdAt: 'desc'
            },
            include: {
                occurrence: {
                    select: {
                        referenceNumber: true,
                        type: true,
                        occurrenceCategory: true
                    }
                }
            }
        })
        return new ApiResponse(true, '', docs)
    }

    async getDocument(id: string) {
        const doc = await this.prisma.document.findUnique({
            where: { id },
            include: {
                occurrence: true
            }
        })

        if (!doc) {
            throw new NotFoundException('document not found')
        }

        return new ApiResponse(true, '', doc)
    }

    async updateDocument(id: string, data: UpdateDocumentDto) {
        const doc = await this.prisma.document.findUnique({
            where: { id }
        })

        if (!doc) {
            throw new NotFoundException('Document not found')
        }

        const updatedDoc = await this.prisma.document.update({
            where: { id },
            data
        })

        return new ApiResponse(true, 'Document Updated', updatedDoc)
    }

    async remove(id: string) {
        const doc = await this.prisma.document.findUnique({
            where: {
                id
            }
        })

        if (!doc) {
            throw new NotFoundException('Document not found')
        }

        const deletedDoc = await this.prisma.document.delete({
            where: { id }
        })

        return new ApiResponse(true, '', deletedDoc)
    }
}
