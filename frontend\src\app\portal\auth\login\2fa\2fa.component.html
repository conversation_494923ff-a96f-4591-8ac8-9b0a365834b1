<div class="flex flex-col justify-center items-center h-screen bg-gray-50">
  <div class="flex items-center flex-col gap-2 pb-12">
    <img class="w-[100px]" src="/coat-of-arm.png" alt="coat of arm" />
    <h2 class="text-2xl font-semibold text-gray-700">Enter the verification code</h2>
  </div>
  <div class="bg-white border rounded-lg p-12 w-full max-w-xl">
    <form [formGroup]="verifyForm" (ngSubmit)="onSubmit()" class="mt-6">
     
      <div class="mb-4">
        <label for="code" class="block text-sm font-medium text-gray-700">Google authenticator code</label>
        <div class="relative mt-1">
          
          <input
            type="text"
            id="code"
            formControlName="code"
            class="w-full outline-none border border-gray-300 rounded-md py-2 pl-10 pr-3 text-gray-800 focus:ring-primary focus:border-primary"
            placeholder="Enter a 6-digit code"
          />
        </div>
        <div *ngIf="verifyForm.get('code')?.touched">
          <p *ngIf="verifyForm.get('code')?.errors?.['required']" class="text-red-500 text-sm mt-1">
            A valid 6-digit code is required
          </p>
  
        </div>
      </div>

      <!-- Submit Button -->
      <button
        type="submit"
        [disabled]="verifyForm.invalid || loading"
        class="w-full bg-primary-500 text-white font-medium py-2 px-4 rounded-md mt-2 hover:bg-primary/90 focus:ring-2 focus:ring-primary focus:ring-opacity-50 disabled:opacity-60 disabled:cursor-not-allowed"
      >
        <ng-container *ngIf="!loading; else spinner">Verify</ng-container>
      </button>

      <ng-template #spinner>
        <i class="pi pi-spin pi-spinner"></i> Loading...
      </ng-template>

    </form>
  </div>
  <p-toast></p-toast>
</div>
