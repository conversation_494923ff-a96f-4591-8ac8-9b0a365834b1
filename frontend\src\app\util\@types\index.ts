export interface User {
    id: string
    name: string
    email: string
    telephone?: string
    profilePicture?: string
    role: string
    status?: string
}

export interface Signature {
    id: string;
    name: string
    imageUrl: string
}

export interface PaginatedResponse<T> {
    data: T[];
    total: number;
    page: number;
    totalPages: number;
}

export interface Contact {
    id: string
    name: string
    description?: string
    email: string
    telephone: string
    createdAt?: Date
    updatedAt?: Date
}

// Enums
export enum Role {
    ADMIN = 'ADMIN',
    INVESTIGATOR = 'INVESTIGATOR',
    DG = 'DG'
}

export enum AccountStatus {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
    PENDING = 'PENDING',
    SUSPENDED = 'SUSPENDED',
    CLOSED = 'CLOSED'
}

export enum MeteologicalCondition {
    IMC = 'IMC',
    VMC = 'VMC'
}

export enum FlightRules {
    IFR = 'IFR',
    VFR = 'VFR'
}

export enum OperationType {
    SCHEDULED = 'SCHEDULED',
    CHARTER = 'CHARTER',
    TRAINING = 'TRAINING',
    PRIVATE = 'PRIVATE',
    DEMONSTRATION = 'DEMONSTRATION',
    AEROBIC = 'AEROBIC',
    AERIAL_TOUR = 'AERIAL_TOUR',
    FIXED_WING_AIRCRAFT = 'FIXED_WING_AIRCRAFT',
    HELICOPTER = 'HELICOPTER',
    BALLOON = 'BALLOON',
    DRONE = 'DRONE',
    OTHER = 'OTHER'
}

export enum OccurrenceType {
    MAJOR_ACCIDENT = 'Major Accident',
    ACCIDENT = 'Accident',
    SERIOUS_INCIDENT = 'Serious Incident',
    INCIDENT_TO_BE_INVESTIGATED = 'Incident to be Investigated',
    INCIDENT = 'Incident',
    ABNORMAL_OCCURRENCE = 'Abnormal Occurrence'
}

export enum HTTP_Method {
    GET = 'GET',
    POST = 'POST',
    PUT = 'PUT',
    PATCH = 'PATCH',
    DELETE = 'DELETE'
}

export enum NotificationType {
    ERROR = 'ERROR',
    INFO = 'INFO',
    WARNING = 'WARNING'
}

export enum DocumentType {
    EVIDENCE = 'EVIDENCE',
    PHOTOGRAPHS = 'PHOTOGRAPHS',
    STATEMENTS = 'STATEMENTS',
    OTHER = 'OTHER'
}

export enum OccurrenceStatus {
    OPEN = 'Open',
    UNDER_INVESTIGATION = 'Under Investigation',
    CLOSED = 'Closed'
}

// Model Types
export interface Aircraft {
    id: string;
    model: string;
    manufacturer: string;
    operator?: string | null;
    registrationMark?: string | null;
    operatorNationality?: string | null;
    intendedLandingDateTime?: Date | null;
    intendedLandingPoint?: string | null;
    lastDeparturePoint?: string | null;
    crewOnBoard?: number | null;
    crewInjured?: number | null;
    crewPerished?: number | null;
    passengersOnBoard?: number | null;
    passengersInjured?: number | null;
    passengersPerished?: number | null;
    // occurrenceId: string;
    occurrence: Occurrence;
    createdAt: Date;
    updatedAt: Date;
}

export interface Occurrence {
    id: string;
    referenceNumber?: string | null;

    // General Info
    reporterName: string;
    reporterEmail: string;
    reporterPhone: string;
    pilotInCommandName?: string | null;
    pilotInCommandEmail?: string | null;
    pilotInCommandPhone?: string | null;

    // Involved Aircraft
    involvedAircraft?: Aircraft[];
    groundPeoplePerished?: number | null;
    groundPeopleInjured?: number | null;

    // Details
    generalWeatherConditions?: string | null;
    skyCoverage?: string | null;
    meteologicalCondition: MeteologicalCondition;
    flightRules: FlightRules;
    occurrenceTime: Date;
    operationType: OperationType;
    flightPhase?: string | null;

    // Coordinates
    latitude?: string | null;
    longitude?: string | null;
    occurrenceLocation: string;

    dangerousGoodCarriedOnBoard?: string | null;

    type?: OccurrenceType | null;
    occurrenceCategory?: OccurrenceCategory | null;
    occurrenceCategory_id?: string | null;

    status?: OccurrenceStatus | null

    documents?: Document[];
    safetyRecommendations?: SafetyRecommendation[];

    createdAt: Date;
    updatedAt: Date;
}

export interface OccurrenceCategory {
    id: string;
    category: string;
    description: string;
    explanation: string;
    occurrences: Occurrence[];
    createdAt: Date;
    updatedAt: Date;
}

export interface AircraftInfo {
    id: string;
    modelFullName: string;
    description: string;
    wtc?: string;
    wtg?: string;
    designator: string;
    manufacturerCode: string;
    aircraftDescription: string;
    engineCount?: number
    engineType?: string
    createdAt: Date;
    updatedAt: Date;
}


export interface Document {
    id: string;
    type: DocumentType;
    name: string;
    url: string;
    occurrenceId: string;
    occurrence: Occurrence;
    createdAt: Date;
    updatedAt: Date;
}

export interface SafetyRecommendation {
    id: string;
    title: string;
    description: string;
    occurrenceId: string;
    occurrence: Occurrence;
    createdAt: Date;
    updatedAt: Date;
}

export interface Notification {
    id: string;
    title: string;
    description?: string | null;
    userId: string;
    user: User;
    createdAt: Date;
    updatedAt: Date;
}

export interface ContactsInfo {
    id: string;
    name: string;
    description?: string | null;
    email: string;
    telephone: string;
    createdAt: Date;
    updatedAt: Date;
}

export interface DashboardData {
    total: {
        occurrences: {
            total: number;
            increase: number;
        };
        incidents: {
            total: number;
            increase: number;
        };
        seriousIncidents: {
            total: number;
            increase: number;
        };
        incidentsToBeInvestigated: {
            total: number;
            increase: number;
        };
        accidents: {
            total: number;
            increase: number;
        };
        reports: {
            total: number;
            increase: number;
        };
    };
    chart: Record<string, { incidents: number; seriousIncidents: number; incidentsToBeInvestigated: number; accidents: number }>;
    occurrences: Occurrence[]
}

export interface Report {
    id: string;
    addressedTo: AddressedTo[];
    stateOfRegistry?: string,
    stateOfDesign: string,
    stateOfManufacturer: string,
    stateOfOperator: string,

    referenceNumber: string;
    occurrenceType: 'ACCID' | 'SINCID' | 'INCID';

    occurrenceDate?: string;
    occurrenceTimeUTC?: string;
    occurrenceTimeLocal?: string;

    position?: string;
    latitude?: string;
    longitude?: string;

    occurrenceDescription: string;
    damageExtent: string;

    dangerousGoodsPresent: boolean;
    dangerousGoodsDescription?: string;
    aircraftInfo: Array<{
        manufacturer?: string;
        model?: string;
        nationality?: string;
        registrationMarks?: string;
        serialNumber?: string;
        owner?: string;
        operator?: string;
        hirer?: string;
        pilotQualification?: string;
        crewNationality?: string;
        passengerNationality?: string;
        lastDeparturePoint?: string;
        intendedLandingPoint?: string;
        casualties?: {
            crew?: {
                aboard?: number;
                killed?: number;
                injured?: number;
            };
            passengers?: {
                aboard?: number;
                killed?: number;
                injured?: number;
            };
            others?: {
                killed?: number;
                injured?: number;
            };
        };
    }>;
    groundPeopleInjured?: string;
    groundPeoplePerished?: string;
    investigationExtent?: string;
    investigationDelegation?: string;
    areaCharacteristics?: string;
    accessRequirements?: string;
    originatingAuthority?: string;
    investigatorName?: string;
    investigatorMobile?: string;
    investigatorEmail?: string;
    status?: "PENDING" | "APPROVED" | "REVERTED";
    submittedBy?: User;
    approvedBy?: User;
    comment?:   string | null
    createdAt: string;
    updatedAt: string;
}

export enum AddressedTo {
    StateOfRegistry = 'StateOfRegistry',
    StateOfDesign = 'StateOfDesign',
    StateOfManufacturer = 'StateOfManufacturer',
    StateOfOperator = 'StateOfOperator',
    ICAO = 'ICAO',
}

export interface Country {
    name: string
    flag: string
}

export interface Logs {
    id: string
    action: string
    sourceUrl: string
    sourceIpAddress: string
    sourceOS: string
    sourceBrowser: string
    url: string
    method: string
    userId?: string
    user?: User
    createdAt: Date
}