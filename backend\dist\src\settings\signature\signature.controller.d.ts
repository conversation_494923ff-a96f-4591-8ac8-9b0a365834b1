import { SignatureService } from './signature.service';
import { CreateSignatureDto, UpdateSignatureDto } from './dto';
export declare class SignatureController {
    private readonly signatureService;
    constructor(signatureService: SignatureService);
    create(createSignatureDto: CreateSignatureDto): Promise<import("../../utils/@types").ApiResponse<{
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        imageUrl: string;
    }>>;
    findAll(): Promise<import("../../utils/@types").ApiResponse<{
        data: {
            id: string;
            name: string;
            createdAt: Date;
            updatedAt: Date;
            imageUrl: string;
        }[];
        total: number;
        pageSize: number;
    }>>;
    findOne(id: string): Promise<import("../../utils/@types").ApiResponse<{
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        imageUrl: string;
    }>>;
    update(id: string, updateSignatureDto: UpdateSignatureDto): Promise<import("../../utils/@types").ApiResponse<{
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        imageUrl: string;
    }>>;
    remove(id: string): Promise<import("../../utils/@types").ApiResponse<{
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        imageUrl: string;
    }>>;
}
