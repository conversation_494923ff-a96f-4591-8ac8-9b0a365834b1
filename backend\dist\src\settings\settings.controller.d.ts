import { SettingsService } from './settings.service';
import { CreateContactInfoDto, CreateOccurrenceCategoryDto, UpdateContactInfoDto, UpdateOccurrenceCategoryDto } from './dto';
export declare class SettingsController {
    private settingsService;
    constructor(settingsService: SettingsService);
    createContactInfo(createDto: CreateContactInfoDto): Promise<import("../utils/@types").ApiResponse<any>>;
    getAllContactInfo(): Promise<import("../utils/@types").ApiResponse<any>>;
    getContactInfoById(id: string): Promise<import("../utils/@types").ApiResponse<any>>;
    updateContactInfo(id: string, updateDto: UpdateContactInfoDto): Promise<import("../utils/@types").ApiResponse<any>>;
    deleteContactInfo(id: string): Promise<import("../utils/@types").ApiResponse<any>>;
    createOccurrenceCategory(createDto: CreateOccurrenceCategoryDto): Promise<import("../utils/@types").ApiResponse<any>>;
    getAllOccurrenceCategories(): Promise<import("../utils/@types").ApiResponse<any>>;
    getOccurrenceCategoryById(id: string): Promise<import("../utils/@types").ApiResponse<any>>;
    updateOccurrenceCategory(id: string, updateDto: UpdateOccurrenceCategoryDto): Promise<import("../utils/@types").ApiResponse<any>>;
    deleteOccurrenceCategory(id: string): Promise<import("../utils/@types").ApiResponse<any>>;
}
