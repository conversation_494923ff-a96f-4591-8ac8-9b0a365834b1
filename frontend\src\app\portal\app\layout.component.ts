import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Router, RouterLink, RouterModule } from '@angular/router';
import { MenuModule } from 'primeng/menu'
import { ButtonModule } from 'primeng/button';
import { User } from '../../util/@types';
import { AuthService } from '../auth/auth.service';
import { MenuItem } from 'primeng/api';

@Component({
  selector: 'app-portal-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    RouterLink,
    MenuModule,
    ButtonModule
  ],
  templateUrl: './layout.component.html',
})
export class PortalLayoutComponent implements OnInit {

  user: User | null = null
  isSidebarOpen = false;

  constructor(
    private authService: AuthService,
    public router: Router
  ){}

  ngOnInit(): void {
    this.user = this.authService.getUserInfo()
    console.log(this.user)
  }

  userMenuItems: MenuItem[] = [
    {
      separator: true
    },
    {
      label: 'Sign Out',
      icon: 'pi pi-sign-out',
      command: () => this.signOut()
    }
  ];

  toggleSidebar() {
    this.isSidebarOpen = !this.isSidebarOpen;
  }

  navigateToProfile() {
  }

  navigateToSettings() {
  }

  signOut() {
    this.authService.logout()
  }

}
