<div class="flex flex-col justify-center items-center h-screen bg-gray-50">
    <div class="flex items-center flex-col gap-2 pb-12">
      <img class="w-[100px]" src="/coat-of-arm.png" alt="coat of arm" />
      <h2 class="text-2xl font-semibold text-gray-700">Sign into AAID Portal</h2>
    </div>
    <div class="bg-white border rounded-lg p-12 w-full max-w-xl">
      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="mt-6">
        <!-- Email Field -->
        <div class="mb-4">
          <label for="email" class="block text-sm font-medium text-gray-700">Email or phone</label>
          <div class="relative mt-1">
            <span class="absolute inset-y-0 left-0 flex items-center pl-3">
              <svg class="h-5 w-5 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
            </span>
            <input
              type="text"
              id="email"
              formControlName="identifier"
              class="w-full outline-none border border-gray-300 rounded-md py-2 pl-10 pr-3 text-gray-800 focus:ring-primary focus:border-primary"
              placeholder="Enter your email or phone"
            />
          </div>
          <div *ngIf="loginForm.get('identifier')?.touched">
            <p *ngIf="loginForm.get('identifier')?.errors?.['required']" class="text-red-500 text-sm mt-1">
              A valid email or phone is required
            </p>
            <p *ngIf="loginForm.get('identifier')?.errors?.['pattern']" class="text-red-500 text-sm mt-1">
              A valid email or phone is required
            </p>
          </div>
        </div>
  
        <!-- Password Field -->
        <div class="mb-4">
          <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
          <div class="relative mt-1">
            <span class="absolute inset-y-0 left-0 flex items-center pl-3">
              <svg class="h-5 w-5 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2" />
                <path d="M7 11V7a5 5 0 0110 0v4" />
              </svg>
            </span>
            <input
              [type]="showPassword ? 'text' : 'password'"
              id="password"
              formControlName="password"
              class="w-full outline-none border border-gray-300 rounded-md py-2 pl-10 pr-10 text-gray-800 focus:ring-primary focus:border-primary"
              placeholder="Enter your password"
            />
            <button
              type="button"
              (click)="togglePassword()"
              class="absolute inset-y-0 right-0 flex items-center pr-3"
            >
              <svg *ngIf="!showPassword" class="h-5 w-5 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
              <svg *ngIf="showPassword" class="h-5 w-5 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
                />
              </svg>
            </button>
          </div>
          <div *ngIf="loginForm.get('password')?.touched">
            <p *ngIf="loginForm.get('password')?.errors?.['required']" class="text-red-500 text-sm mt-1">
              Password is required
            </p>
            <p *ngIf="loginForm.get('password')?.errors?.['minlength']" class="text-red-500 text-sm mt-1">
              Password must be at least 6 characters
            </p>
          </div>
        </div>
  
        <!-- Submit Button -->
        <button
          type="submit"
          [disabled]="loginForm.invalid || loading"
          class="w-full bg-primary-500 text-white font-medium py-2 px-4 rounded-md mt-6 hover:bg-primary/90 focus:ring-2 focus:ring-primary focus:ring-opacity-50 disabled:opacity-60 disabled:cursor-not-allowed"
        >
          <ng-container *ngIf="!loading; else spinner">Log In</ng-container>
        </button>
  
        <ng-template #spinner>
          <i class="pi pi-spin pi-spinner"></i> Loading...
        </ng-template>
  
        <div class="flex justify-center items-center py-2">
          <a routerLink="/portal/auth/reset-password" class="text-sm text-primary-500 cursor-pointer hover:underline">
            Forgot password?
          </a>
        </div>
      </form>
    </div>
    <p-toast></p-toast>
  </div>
  