import { Injectable } from "@angular/core";
import { AxiosService } from "../../../util/axios/axios.service";
import { PaginatedResponse, Signature } from "../../../util/@types";

@Injectable({
  providedIn: 'root'
})
export class SignatureService {
  constructor(
    private axiosService: AxiosService,
  ) {}

  async getSignatures(page: number = 1, limit: number = 10): Promise<PaginatedResponse<Signature>> {
    try {
      const response = await this.axiosService.axios.get('/signatures', {
        params: { page, limit }
      });
      return response.data.data as PaginatedResponse<Signature>;
    } catch (error) {
      throw error;
    }
  }

  async getSignature(id: string): Promise<Signature> {
    try {
      const response = await this.axiosService.axios.get(`/signatures/${id}`);
      return response.data.data as Signature;
    } catch (error) {
      throw error;
    }
  }

  async createSignature(name: string, imageUrl: string): Promise<Signature>{
    try {
      const response = await this.axiosService.axios.post('/signatures', {
        name, 
        imageUrl
      })
      return response.data.data as Signature
    } catch (error) {
      throw error
    }
  }

  async updateSignature(id: string, name: string, imageUrl: string): Promise<Signature>{
    try {
      const response = await this.axiosService.axios.patch(`/signatures/${id}`, {
        name,
        imageUrl,
      })
      return response.data.data as Signature
    } catch (error) {
      throw error
    }
  }

  async deleteSignature(id: string): Promise<Signature> {
    try {
      const response = await this.axiosService.axios.delete(`/signatures/${id}`)
      return response.data.data as Signature
    } catch (error) {
      throw error
    }
  }

  async upload(data: FormData): Promise<string>{
    try {
      const resp = await this.axiosService.fileAxios.post('/attachment/upload', data, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
      return resp.data.url
    } catch (error) {
      throw error
    }
  }
}
