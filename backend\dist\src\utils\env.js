"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.env = void 0;
const dotenv_1 = require("dotenv");
const zod_1 = require("zod");
(0, dotenv_1.config)();
const envSchema = zod_1.default.object({
    PORT: zod_1.default.string(),
    DATABASE_URL: zod_1.default.string(),
    JWT_SECRET: zod_1.default.string(),
    SMTP_SERVER: zod_1.default.string(),
    SMTP_PORT: zod_1.default.string(),
    FE_URL: zod_1.default.string(),
    EMAIL_SENDER: (0, zod_1.string)(),
    EMAIL_CLIENT_URL: (0, zod_1.string)(),
    EMAIL_SENDER_NAME: (0, zod_1.string)(),
    SMS_API_URL: (0, zod_1.string)()
});
exports.env = envSchema.parse({ ...process.env });
//# sourceMappingURL=env.js.map