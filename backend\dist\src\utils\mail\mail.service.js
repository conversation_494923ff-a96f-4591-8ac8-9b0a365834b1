"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MailService = exports.MailType = void 0;
const common_1 = require("@nestjs/common");
const fs_1 = require("fs");
const axios_1 = require("@nestjs/axios");
const rxjs_1 = require("rxjs");
const env_1 = require("../env");
var MailType;
(function (MailType) {
    MailType[MailType["CREATE_ACCOUNT"] = 0] = "CREATE_ACCOUNT";
    MailType[MailType["LOGIN"] = 1] = "LOGIN";
    MailType[MailType["OCCURENCE_SUBMIT"] = 2] = "OCCURENCE_SUBMIT";
    MailType[MailType["VERIFY_ACCOUNT"] = 3] = "VERIFY_ACCOUNT";
    MailType[MailType["RESET_PASSWORD"] = 4] = "RESET_PASSWORD";
    MailType[MailType["ACCOUNT_VERIFIED"] = 5] = "ACCOUNT_VERIFIED";
    MailType[MailType["ACCOUNT_DEACTIVATED"] = 6] = "ACCOUNT_DEACTIVATED";
    MailType[MailType["PASSWORD_RESET_CONFIRMATION"] = 7] = "PASSWORD_RESET_CONFIRMATION";
    MailType[MailType["REPORT_APPROVED"] = 8] = "REPORT_APPROVED";
    MailType[MailType["REPORT_REVERTED"] = 9] = "REPORT_REVERTED";
    MailType[MailType["APPROVAL_REQUEST"] = 10] = "APPROVAL_REQUEST";
})(MailType || (exports.MailType = MailType = {}));
let MailService = class MailService {
    constructor(httpService) {
        this.httpService = httpService;
    }
    sendMail(mailType, props) {
        (async () => {
            try {
                const { template } = this.getMailTemplateAndFromEmail(mailType);
                const body = await this.constructMailBody(template, props.values);
                const requestData = {
                    sender_name: env_1.env.EMAIL_SENDER_NAME,
                    sender_email: env_1.env.EMAIL_SENDER,
                    receiver_name: props.name,
                    receiver_email: props.to,
                    subject: props.subject,
                    message: body,
                };
                await (0, rxjs_1.firstValueFrom)(this.httpService.post(env_1.env.EMAIL_CLIENT_URL, requestData));
                common_1.Logger.log(`Email sent to ${props.to}`, 'MailService');
            }
            catch (error) {
                common_1.Logger.error(`Failed to send email: ${error.message}`, 'MailService');
            }
        })();
    }
    async constructMailBody(template, props) {
        return new Promise((resolve) => {
            (0, fs_1.readFile)('./src/utils/mail/templates/' + template + '.template.html', 'utf-8', (err, data) => {
                let body = data;
                if (err) {
                    common_1.Logger.error(err.message, 'MailingService', 'constructMailBody');
                }
                for (const [key, value] of Object.entries(props)) {
                    body = body.replace(`{{${key}}}`, value);
                }
                return resolve(body);
            });
        });
    }
    getMailTemplateAndFromEmail(mailType) {
        switch (mailType) {
            case MailType.CREATE_ACCOUNT:
                return { template: 'create-account-email' };
            case MailType.LOGIN:
                return { template: 'login-notif-email' };
            case MailType.OCCURENCE_SUBMIT:
                return { template: 'occurrence-notif' };
            case MailType.VERIFY_ACCOUNT:
                return { template: 'verify-account' };
            case MailType.ACCOUNT_VERIFIED:
                return { template: 'account-verified' };
            case MailType.ACCOUNT_DEACTIVATED:
                return { template: 'account-deactivated' };
            case MailType.RESET_PASSWORD:
                return { template: 'reset-password' };
            case MailType.PASSWORD_RESET_CONFIRMATION:
                return { template: 'password-reset-confirm' };
            case MailType.REPORT_APPROVED:
                return { template: 'approve' };
            case MailType.REPORT_REVERTED:
                return { template: 'revert' };
            case MailType.APPROVAL_REQUEST:
                return { template: 'approval' };
            default:
                return { template: 'Null' };
        }
    }
};
exports.MailService = MailService;
exports.MailService = MailService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [axios_1.HttpService])
], MailService);
//# sourceMappingURL=mail.service.js.map