import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray, FormsModule, ReactiveFormsModule, AbstractControl, ValidationErrors } from '@angular/forms';
import { OccurrencesService } from '../../occurrences/occurrences.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Occurrence, Aircraft, AddressedTo, Country, User } from '../../../../util/@types';
import { MessageService } from 'primeng/api';
import { ReportsService } from '../reports.service';
import { ToastModule } from 'primeng/toast';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { DropdownModule } from 'primeng/dropdown';

@Component({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ToastModule,
    AutoCompleteModule,
    DropdownModule
  ],
  providers: [MessageService],
  standalone: true,
  selector: 'app-notification-form',
  templateUrl: './new.component.html',
})
export class NewReportComponent implements OnInit {
  notificationForm!: FormGroup;
  loading = false
  occurrence!: Occurrence
  involvedAircraft!: Aircraft[]
  addressedToOptions = Object.values(AddressedTo);

  countries: Country[] = [];
  filteredCountries: Country[] = [];

  investigators: User[] = []
  filteredInvestigators: User[] = []

  constructor(
    private fb: FormBuilder,
    private occurrenceService: OccurrencesService,
    private route: ActivatedRoute,
    private router: Router,
    private messageService: MessageService,
    private reportsService: ReportsService,
    private cdr: ChangeDetectorRef

  ) { }

  ngOnInit(): void {
    this.initializeForms();
    this.loadOccurrence()
    this.loadCountries()
    this.loadInvestigators()
  }

  get addressedToControls(): FormArray {
    return this.notificationForm.get('addressedTo') as FormArray;
  }

  addressedToStates: {[key: string]: boolean} = {
    stateOfRegistry: false,
    stateOfDesign: false,
    stateOfManufacturer: false,
    stateOfOperator: false,
    ICAO: false
  };

  initializeForms() {
    this.notificationForm = this.fb.group({
      referenceNumber: ['', Validators.required],

      addressedToRegistry: [false],
      addressedToDesign: [false],
      addressedToManufacturer: [false],
      addressedToOperator: [false],
      addressedToICAO: [false],

      stateOfRegistry: [''],
      stateOfDesignCountry: [''],
      stateOfManufacturerCountry: [''],
      stateOfOperatorCountry: [''],

      // a) Occurrence Type
      occurrenceType: ['', [Validators.required]],

      // Aircraft-related sections grouped together
      aircraftInfo: this.fb.array([this.createAircraftInfo()]),

      groundPeopleInjured: [0],
      groundPeoplePerished: [0],

      // d) Date and Time
      occurrenceDate: ['',],
      occurrenceTimeUTC: ['',],
      occurrenceTimeLocal: ['',],

      // f) Aircraft Location
      position: ['',],
      latitude: ['',],
      longitude: ['',],

      // h) Description
      occurrenceDescription: ['', Validators.required],
      damageExtent: ['', Validators.required],

      // i) Investigation Details
      investigationExtent: ['',],
      investigationDelegation: ['',],

      // j) Physical Characteristics
      areaCharacteristics: ['',],
      accessRequirements: ['',],

      // k) Investigation Authority
      originatingAuthority: ['', Validators.required],
      investigatorInCharge: this.fb.group({
        name: ['', Validators.required],
        mobile: ['', [Validators.required, Validators.pattern('^[0-9+()-\s]*$')]],
        email: ['', [Validators.required, Validators.email]]
      }),

      dangerousGoodsPresent: [false],
      dangerousGoodsDescription: [''],

    },
    {
      validators: this.atLeastOneSelectedValidator()
    });

    Object.keys(this.addressedToStates).forEach(key => {
      const controlName = 'addressedTo' + key.charAt(0).toUpperCase() + key.slice(1);
      this.notificationForm.get(controlName)?.valueChanges.subscribe(checked => {
        this.addressedToStates[key as keyof typeof this.addressedToStates] = checked;
        if (!checked) {
          const countryControl = this.notificationForm.get(key + 'Country');
          if (countryControl) {
            countryControl.setValue('');
          }
        }
      });
    });
  }

  loadCountries() {
    this.reportsService.getCountries().subscribe({
      next: (data) => {
        this.countries = data.sort((a, b)=> a.name.localeCompare(b.name));
        this.filteredCountries = [];
        this.cdr.detectChanges();
      },
      error: (error) => {
        this.handleError('Failed to load countries', error)
      }
    });
  }

  loadInvestigators(){
    this.reportsService.getInvestigators().subscribe({
      next: (data) => {
        this.investigators = data
        this.filteredInvestigators = [];
        this.cdr.detectChanges();
      },
      error: (error) => {
        this.handleError('Failed to load countries', error)
      }
    })
  }

  filterCountries(event: any) {
    const query = event.query.toLowerCase();
    this.filteredCountries = query ? 
      this.countries.filter(country => country.name.toLowerCase().includes(query)) :
      this.countries;
  }

  filterInvestigators(event: any) {
    const query = event.query.toLowerCase();
    this.filteredInvestigators = query ? 
      this.investigators.filter(i => i.name.toLowerCase().includes(query)) :
      this.investigators;
  }

  onClear() {
    this.filteredCountries = [];
    this.cdr.detectChanges();
  }

  async loadOccurrence() {
    try {
      this.loading = true;
      const id = this.route.snapshot.paramMap.get('occurrence') || '';

      if (!id) {
        throw new Error('No occurrence ID provided');
      }

      this.occurrence = await this.occurrenceService.getOccurrence(id);
      this.involvedAircraft = this.occurrence.involvedAircraft || [];

      while (this.aircraftInfo.length !== 0) {
        this.aircraftInfo.removeAt(0);
      }

      this.involvedAircraft.forEach(aircraft => {
        this.aircraftInfo.push(this.createAircraftInfo());
      });

      this.notificationForm.patchValue({
        referenceNumber: this.occurrence.referenceNumber,
        occurrenceType: this.mapOccurrenceType(this.occurrence.type ?? ''),
        occurrenceDate: this.formatDateForInput(this.occurrence.occurrenceTime??undefined),
        occurrenceTimeUTC: this.formatTimeForInput(this.occurrence.occurrenceTime??undefined),
        occurrenceTimeLocal: this.formatTimeForInput(this.occurrence.occurrenceTime??undefined),
        position: this.occurrence.occurrenceLocation,
        latitude: this.formatCoordinate(this.occurrence.latitude ?? '', 'N', 'S'),
        longitude: this.formatCoordinate(this.occurrence.longitude ?? '', 'E', 'W'),
        dangerousGoodsPresent: !!this.occurrence.dangerousGoodCarriedOnBoard,
        dangerousGoodsDescription: this.occurrence.dangerousGoodCarriedOnBoard,
        groundPeopleInjured: this.occurrence.groundPeopleInjured,
        groundPeoplePerished: this.occurrence.groundPeoplePerished,
        originatingAuthority: 'AAID-Rwanda'
      });

      if (this.involvedAircraft.length > 1) {
        const casualties = {
          crew: {
            aboard: 0,
            killed: 0,
            injured: 0
          },
          passengers: {
            aboard: 0,
            killed: 0,
            injured: 0
          },
          others: {
            killed: this.occurrence.groundPeoplePerished || 0,
            injured: this.occurrence.groundPeopleInjured || 0
          }
        };

        this.aircraftInfo.at(0).patchValue({
          aircraftDetails: {
            manufacturer: '',
            model: '',
            nationality: '',
            registrationMarks: '',
            operator: ''
          },
          flightDetails: {
            lastDeparturePoint: '',
            intendedLandingPoint: ''
          },
          casualties: casualties
        });
        this.involvedAircraft.push()
      }

      this.involvedAircraft.forEach((aircraft, index) => {
        const casualties = {
          crew: {
            aboard: aircraft.crewOnBoard || 0,
            killed: aircraft.crewPerished || 0,
            injured: aircraft.crewInjured || 0
          },
          passengers: {
            aboard: aircraft.passengersOnBoard || 0,
            killed: aircraft.passengersPerished || 0,
            injured: aircraft.passengersInjured || 0
          },
          others: {
            killed: this.occurrence.groundPeoplePerished || 0,
            injured: this.occurrence.groundPeopleInjured || 0
          }
        };

        this.aircraftInfo.at(index).patchValue({
          aircraftDetails: {
            manufacturer: aircraft.manufacturer,
            model: aircraft.model,
            nationality: aircraft.operatorNationality,
            registrationMarks: aircraft.registrationMark,
            operator: aircraft.operator
          },
          flightDetails: {
            lastDeparturePoint: aircraft.lastDeparturePoint,
            intendedLandingPoint: aircraft.intendedLandingPoint
          },
          casualties: casualties
        });
      });

      this.loading = false;
      this.cdr.detectChanges()

    } catch (error) {
      this.handleError('Failed to load occurrence details', error);
      this.loading = false;
    }
    this.cdr.detectChanges()
  }

  private atLeastOneSelectedValidator(): ValidationErrors | null {
    return (formGroup: AbstractControl): ValidationErrors | null => {
      const addressedToControls = [
        formGroup.get('addressedToRegistry')?.value,
        formGroup.get('addressedToDesign')?.value,
        formGroup.get('addressedToManufacturer')?.value,
        formGroup.get('addressedToOperator')?.value,
        formGroup.get('addressedToICAO')?.value
      ];
  
      return addressedToControls.some(value => value === true) 
        ? null 
        : { addressedTo: true };
    };
  }

  get addressedTo(): FormArray {
    return this.notificationForm.get('addressedTo') as FormArray;
  }

  get addressedToError(): boolean {
    return this.addressedTo.errors?.['atLeastOne'] && this.addressedTo.touched;
  }

  formatDateForInput(date: Date | string | undefined): string {
    if (!date) return '';
    const d = new Date(date);
    return new Date(d.getTime() - d.getTimezoneOffset() * 60000).toISOString().slice(0, 10);
  }
  
  formatTimeForInput(date: Date | string | undefined): string {
    if (!date) return '';
    const d = new Date(date);
    return d.toTimeString().slice(0, 5);
  }

  createAircraftInfo(): FormGroup {
    return this.fb.group({
      // b) Aircraft Details
      aircraftDetails: this.fb.group({
        manufacturer: ['', Validators.required],
        model: ['', ],
        nationality: ['', ],
        registrationMarks: ['', ],
        serialNumber: ['', ],
        owner: ['', ],
        operator: ['', ],
        hirer: ['']
      }),

      // c) Crew and Passenger Details
      crewDetails: this.fb.group({
        pilotQualification: ['', ],
        crewNationality: ['', ],
        passengerNationality: ['', ]
      }),

      // e) Flight Path
      flightDetails: this.fb.group({
        lastDeparturePoint: ['', ],
        intendedLandingPoint: ['', ]
      }),

      // g) Casualties
      casualties: this.fb.group({
        crew: this.fb.group({
          aboard: [0,],
          killed: [0,],
          injured: [0,]
        }),
        passengers: this.fb.group({
          aboard: [0,],
          killed: [0,],
          injured: [0,]
        }),
      })
    });
  }

  get aircraftInfo(): FormArray {
    return this.notificationForm.get('aircraftInfo') as FormArray;
  }

  addAircraft(): void {
    this.aircraftInfo.push(this.createAircraftInfo());
  }

  removeAircraft(index: number): void {
    if (this.aircraftInfo.length > 1) {
      this.aircraftInfo.removeAt(index);
    }
  }


  async onSubmit(): Promise<void> {

    if (this.notificationForm.valid) {
      this.loading = true;
      try {
        const formValue = this.notificationForm.value;
        
        await this.reportsService.createReport(formValue);
  
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Report created successfully'
        });
  
        this.router.navigate(['/portal/reports']);
      } catch (error: any) {
        console.error('Error submitting report:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: error.response?.data?.message || 'Failed to create report'
        });
      } finally {
        this.loading = false;
      }
    } else {
      this.markFormGroupTouched(this.notificationForm);
      this.messageService.add({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Please fill in all required fields correctly'
      });
    }
  }
  private markFormGroupTouched(formGroup: FormGroup | FormArray): void {
    Object.values(formGroup.controls).forEach(control => {
      if (control instanceof FormGroup || control instanceof FormArray) {
        this.markFormGroupTouched(control);
      } else {
        control.markAsTouched();
      }
    });
  }

  private handleError(message: string, error: any) {
    console.error(error);
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: error.response?.data?.message || error.message || message
    });
  }

  private formatDate(dateString: string | undefined): string {
    if (!dateString) return '';
    return new Date(dateString).toISOString().split('T')[0];
  }

  private formatTime(dateString: string | undefined): string {
    if (!dateString) return '';
    return new Date(dateString).toISOString().split('T')[1].substr(0, 5);
  }

  private formatCoordinate(coordinate: string, positiveDirection: string, negativeDirection: string): string {
    if (!coordinate || coordinate.trim() === '') return '';

    const num = parseFloat(coordinate);
    if (isNaN(num)) return '';

    const direction = num >= 0 ? positiveDirection : negativeDirection;
    const abs = Math.abs(num);
    const degrees = Math.floor(abs);
    const minutes = Math.floor((abs - degrees) * 60);
    const seconds = Math.round(((abs - degrees) * 60 - minutes) * 60);

    return `${degrees}°${minutes}'${seconds}"${direction}`;
  }

  private mapOccurrenceType(type: string): string {
    const typeMap: { [key: string]: string } = {
      'ACCIDENT': 'ACCID',
      'SERIOUS_INCIDENT': 'SINCID',
      'INCIDENT': 'INCID'
    };
    return typeMap[type] || '';
  }


  onInvestigatorChange(event: any): void {
    const selectedInvestigator = event.value;
    if (selectedInvestigator) {
      const investigatorGroup = this.notificationForm.get('investigatorInCharge');
      console.log("selected investigator: ", selectedInvestigator)
      investigatorGroup?.patchValue({
        email: selectedInvestigator.email || '',
        mobile: selectedInvestigator.telephone || '',
      });
    }
  }

  onUTCTimeChange(event: any): void {
    const utcTime = event.target.value;
    if (utcTime) {
      const date = this.notificationForm.get('occurrenceDate')?.value;
      if (date) {
        const [hours, minutes] = utcTime.split(':');
        const utcDate = new Date(date);
        utcDate.setUTCHours(parseInt(hours), parseInt(minutes));
        
        const localTime = this.formatTimeForInput(utcDate);
        this.notificationForm.patchValue({
          occurrenceTimeLocal: localTime
        });
      }
    }
  }

  onLocalTimeChange(event: any): void {
    const localTime = event.target.value;
    if (localTime) {
      const date = this.notificationForm.get('occurrenceDate')?.value;
      if (date) {
        const [hours, minutes] = localTime.split(':');
        const localDate = new Date(date);
        localDate.setHours(parseInt(hours), parseInt(minutes));
        
        const utcTime = this.formatTimeForInput(new Date(localDate.getTime() + localDate.getTimezoneOffset() * 60000));
        this.notificationForm.patchValue({
          occurrenceTimeUTC: utcTime
        });
      }
    }
  }
}