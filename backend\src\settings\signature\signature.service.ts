import { ConflictException, HttpException, Injectable, InternalServerErrorException, NotFoundException } from "@nestjs/common";
import { PrismaService } from "src/prisma/prisma.service";
import { ApiResponse } from "src/utils/@types";
import { CreateSignatureDto, UpdateSignatureDto } from "./dto";

@Injectable()
export class SignatureService {
  constructor(private readonly prisma: PrismaService) {}

  async create(createSignatureDto: CreateSignatureDto) {

    const signatures = await this.prisma.signature.findMany()
    if(signatures.length >= 1){
      throw new ConflictException('There can only be one DG signature at time')
    }

    const data = await this.prisma.signature.create({ data: createSignatureDto });
    return new ApiResponse(true, 'Signature created successfully', data);
  }

  async findAll(
    page: number = 1,
    pageSize: number = 10
  ) {
    const data = await this.prisma.signature.findMany();
    const total = await this.prisma.signature.count();

    return new ApiResponse(true, 'Users retrieved successfully', {
      data: data,
      total,
      pageSize
    });
  }

  async findOne(id: string) {
    const data = await this.prisma.signature.findUnique({ where: { id } });
    if (!data) {
      throw new NotFoundException(`Signature with ID ${id} not found.`);
    }
    return new ApiResponse(true, 'Signature fetched successfully', data);
  }

  async update(id: string, updateSignatureDto: UpdateSignatureDto) {
    const existingSignature = await this.prisma.signature.findUnique({ where: { id } });
    if (!existingSignature) {
      throw new NotFoundException(`Signature with ID ${id} not found.`);
    }
    const data = await this.prisma.signature.update({ where: { id }, data: updateSignatureDto });
    return new ApiResponse(true, 'Signature updated successfully', data);
  }

  async remove(id: string) {
    const existingSignature = await this.prisma.signature.findUnique({ where: { id } });
    if (!existingSignature) {
      throw new NotFoundException(`Signature with ID ${id} not found.`);
    }
    const data = await this.prisma.signature.delete({ where: { id } });
    return new ApiResponse(true, 'Signature deleted successfully', data);
  }
}