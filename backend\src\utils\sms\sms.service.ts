import { HttpService } from "@nestjs/axios";
import { Injectable, Logger } from "@nestjs/common";
import { firstValueFrom } from "rxjs";
import { env } from "../env";

@Injectable()
export class SmsService{

    constructor(
        private httpService: HttpService
    ){}

    async sendSMS(to: string, message: string){
        (async () => {
            try {

                await firstValueFrom(
                    this.httpService.post(env.SMS_API_URL, {
                        msisdn: to,
                        message
                    })
                )

                Logger.log(`SMS sent to ${to}`, 'SmsService');
            } catch (error) {
                Logger.error(`Failed to send SMS: ${error.message}`, 'SmsService');
            }
        })();
    }
}