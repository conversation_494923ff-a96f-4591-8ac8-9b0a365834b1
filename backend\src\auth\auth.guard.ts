import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { PrismaService } from 'src/prisma/prisma.service';
import { env } from 'src/utils/env';

@Injectable()
export class AuthGuard implements CanActivate {
  private jwtService: JwtService;
  private prismaService: PrismaService;

  constructor() {
    this.jwtService = new JwtService();
    this.prismaService = new PrismaService();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    try {
      if (request.url.includes('auth')) return true;
      if (request.url.includes('2fa')) return true;
      if (request.url.includes('users/activate')) return true;
      if (request.url.includes('users/reset-password')) return true;
      if (request.url.includes('users/confirm-reset-password')) return true;
      if (request.url.includes('occurrence/public/submit')) return true;
      if (request.url.includes('countries')) return true;

      const token = this.extractTokenFromHeader(request);
      if (!token) {
        throw new UnauthorizedException();
      }

      const payload = await this.jwtService.verifyAsync(token, {
        secret: env.JWT_SECRET,
      });

      const user = await this.prismaService.user.findUnique({
        where: {
          email: payload.email,
        },
      });
      if (!user) return false;
      request['user'] = user;

      return true;
    } catch (error) {
      if (error.message == 'jwt malformed') {
        return false;
      }
    }
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
