import { Modu<PERSON> } from '@nestjs/common';
import { AuthenticationController } from './auth.controller';
import { AuthenticationService } from './auth.service';
import { PrismaService } from 'src/prisma/prisma.service';
import { JwtModule } from '@nestjs/jwt';
import { MailService } from 'src/utils/mail/mail.service';
import { env } from 'src/utils/env';
import { HttpModule, HttpService } from '@nestjs/axios';

@Module({
  imports: [
    JwtModule.register({
      global: true,
      secret: env.JWT_SECRET,
      signOptions: {
        expiresIn: '1d',
      },
    }),
  ],
  controllers: [AuthenticationController],
  providers: [AuthenticationService, PrismaService,],
})
export class AuthenticationModule {}
