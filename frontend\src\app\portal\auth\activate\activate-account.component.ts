import { Component } from '@angular/core';
import { FormGroup, FormBuilder, Validators, AbstractControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { MessageService } from 'primeng/api';
import { AuthService } from '../auth.service';
import { CommonModule } from '@angular/common';
import { ToastModule } from 'primeng/toast';
import { DialogModule } from 'primeng/dialog';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ToastModule,
    RouterModule,
    DialogModule
  ],
  providers: [MessageService],
  templateUrl: './activate-account.component.html',
})
export class ActivateAccountComponent {
  resetForm: FormGroup;
  showPassword = false;
  loading = false;
  token: string | null = null
  visible = false

  twoFa: {qrCode: string, secret: string} | null = null

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private messageService: MessageService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.resetForm = this.fb.group({
      newPassword: ['', [
        Validators.required,
        Validators.minLength(8),
        Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/)
      ]],
      confirmPassword: ['', [Validators.required]]
    }, {
      validators: this.passwordMatchValidator
    });
  }

  passwordMatchValidator(form: AbstractControl) {
    const newPassword = form.get('newPassword');
    const confirmPassword = form.get('confirmPassword');

    // Only validate if both fields have values
    if (newPassword?.value && confirmPassword?.value) {
      const passwordsMatch = newPassword.value === confirmPassword.value;

      if (!passwordsMatch) {
        confirmPassword.setErrors({ passwordMismatch: true });
      } else {
        confirmPassword.setErrors(null);
      }
    }

    return null;
  }

  ngOnInit(): void {
    this.token = this.route.snapshot.queryParamMap.get('token')
    if (!this.token) {
      this.router.navigate(['/portal/auth/login'])
    }
  }

  togglePassword(): void {
    this.showPassword = !this.showPassword;
  }

  async onSubmit(): Promise<void> {
    if (this.resetForm.valid && this.token) {
      this.loading = true;
      const { newPassword } = this.resetForm.value;
      try {
        const data = await this.authService.activateAccount(this.token, newPassword);

        this.messageService.add({
          severity: "success",
          summary: "Account activated",
          detail: "Your account has been activated successfully, Scan the QR Code to continue"
        });

        this.twoFa = data
        this.visible = true
        
      } catch (error: any) {
        const errorMessage = error.response?.data?.message || error.message || 'An unexpected error occurred';

        this.messageService.add({
          severity: 'error',
          summary: 'Failed to activate account',
          detail: errorMessage,
        });
      } finally {
        this.loading = false;
      }
    } else {
      Object.keys(this.resetForm.controls).forEach((key) => {
        const control = this.resetForm.get(key);
        if (control?.invalid) {
          control.markAsTouched();
        }
      });

      this.messageService.add({
        severity: 'warn',
        summary: 'Form Incomplete',
        detail: 'Please fill in all required fields.',
      });
    }
  }

  finish(){
    this.router.navigate(['/portal/auth/login'])
  }
}