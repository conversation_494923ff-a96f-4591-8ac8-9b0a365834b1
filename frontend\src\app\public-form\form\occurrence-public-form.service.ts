import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { OccurrenceReport } from '../models/enums';
import { Observable } from 'rxjs';
import { AxiosService } from '../../util/axios/axios.service';

@Injectable({
  providedIn: 'root'
})
export class OccurrencePublicFormService {

  constructor(private axiosService: AxiosService) {}

  async submitOccurrenceReport(report: any): Promise<any> {
    try {
      const response = await this.axiosService.axios.post('/occurrence/public/submit', report);
      return response.data;
    } catch (error) {
      console.error('Error submitting report:', error);
      throw error;
    }
  }
}
