<div class="mt-8 pb-8">
  <form (ngSubmit)="onSubmit()" class="">
    <div class="py-4">
      <h2 class="text-2xl font-medium text-text-black">Occurrence notification form</h2>
    </div>

    <!-- Step Indicator -->
    <div class="flex items-center justify-between gap-4 w-full">
      <!-- Step 1: General Information -->
      <div class="flex items-center gap-4 w-full">
        <div
          class="min-w-[50px] min-h-[50px] border-2 rounded-xl flex items-center justify-center text-2xl font-semibold transition-colors"
          [ngClass]="{
            'border-primary-400 bg-primary-400 text-text-white':
              currentStep >= 1,
            'border-[#C2C5C6] text-text-black': currentStep < 1
          }">
          1
        </div>
        <div>
          <h4 class="text-text-black font-medium">General Information</h4>
        </div>
        <div class="h-[4px] rounded-full w-full transition-colors" [ngClass]="{
            'bg-primary-200': currentStep > 1,
            'bg-[#35393B] bg-opacity-35': currentStep <= 1
          }"></div>
      </div>

      <!-- Step 2: Occurrence -->
      <div class="flex items-center gap-4 w-full">
        <div
          class="min-w-[50px] min-h-[50px] border-2 rounded-xl flex items-center justify-center text-2xl font-semibold transition-colors"
          [ngClass]="{
            'border-primary-400 bg-primary-400 text-text-white':
              currentStep >= 2,
            'border-[#C2C5C6] text-text-black': currentStep < 2
          }">
          2
        </div>
        <div>
          <h4 class="text-text-black font-medium">Occurrence</h4>
        </div>
        <div class="h-[4px] rounded-full w-full transition-colors" [ngClass]="{
            'bg-primary-200': currentStep > 2,
            'bg-[#35393B] bg-opacity-35': currentStep <= 2
          }"></div>
      </div>

      <!-- Step 3: Details -->
      <div class="flex items-center gap-4">
        <div
          class="min-w-[50px] min-h-[50px] border-2 rounded-xl flex items-center justify-center text-2xl font-semibold transition-colors"
          [ngClass]="{
            'border-primary-400 bg-primary-400 text-text-white':
              currentStep >= 3,
            'border-[#C2C5C6] text-text-black': currentStep < 3
          }">
          3
        </div>
        <div>
          <h4 class="text-text-black font-medium">Details</h4>
        </div>
      </div>
    </div>

    <div class="px-12 py-6 rounded-xl border shadow-md border-[#CCD2D5] mt-12">
      <!-- Step 1: Reporter Information -->
      <div *ngIf="currentStep === 1" class="space-y-6">
        <div [formGroup]="reporterForm">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2">Reporter Name <span
                  class="text-red-500">*</span></label>
              <input formControlName="reporterName" placeholder="Full name"
                class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"
                [ngClass]="{
                  'border-red-500':
                    reporterForm.get('reporterName')?.invalid &&
                    reporterForm.get('reporterName')?.touched
                }" />
              <p *ngIf="
                  reporterForm.get('reporterName')?.invalid &&
                  reporterForm.get('reporterName')?.touched
                " class="text-red-500 text-xs italic">
                Name is required
              </p>
            </div>

            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2">Reporter Email <span
                  class="text-red-500">*</span></label>
              <input formControlName="reporterEmail" placeholder="Email address"
                class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"
                [ngClass]="{
                  'border-red-500':
                    reporterForm.get('reporterEmail')?.invalid &&
                    reporterForm.get('reporterEmail')?.touched
                }" />
              <p *ngIf="
                  reporterForm.get('reporterEmail')?.invalid &&
                  reporterForm.get('reporterEmail')?.touched
                " class="text-red-500 text-xs italic">
                Valid email is requried
              </p>
            </div>

            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2">Reporter Phone <span
                  class="text-red-500">*</span></label>
              <input type="tel" formControlName="reporterPhone" placeholder="Telephone number"
                class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"
                [ngClass]="{
                  'border-red-500':
                    reporterForm.get('reporterPhone')?.invalid &&
                    reporterForm.get('reporterPhone')?.touched
                }" />
              <p *ngIf="
                  reporterForm.get('reporterPhone')?.invalid &&
                  reporterForm.get('reporterPhone')?.touched
                " class="text-red-500 text-xs italic">
                Valid telephone is required
              </p>
            </div>
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2">Name of the pilot in command
              </label>
              <input type="text" formControlName="pilotInCommandName" placeholder="Full name"
                class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"
                [ngClass]="{
                  'border-red-500':
                    reporterForm.get('pilotInCommandName')?.invalid &&
                    reporterForm.get('pilotInCommandName')?.touched
                }" />
            </div>

            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2">Email of the pilot in command
              </label>
              <input type="email" formControlName="pilotInCommandEmail" placeholder="Email address"
                class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"
                [ngClass]="{
                  'border-red-500':
                    reporterForm.get('pilotInCommandEmail')?.invalid &&
                    reporterForm.get('pilotInCommandEmail')?.touched
                }" />
              <p *ngIf="
                  reporterForm.get('pilotInCommandEmail')?.invalid &&
                  reporterForm.get('pilotInCommandEmail')?.touched
                " class="text-red-500 text-xs italic">
                Invalid email
              </p>
            </div>

            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2">Pilot in command phone
              </label>
              <input type="tel" formControlName="pilotInCommandPhone" placeholder="Telephone number"
                class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"
                [ngClass]="{
                  'border-red-500':
                    reporterForm.get('pilotInCommandPhone')?.invalid &&
                    reporterForm.get('pilotInCommandPhone')?.touched
                }" />
              <p *ngIf="
                  reporterForm.get('pilotInCommandPhone')?.invalid &&
                  reporterForm.get('pilotInCommandPhone')?.touched
                " class="text-red-500 text-xs italic">
                Invalid phone
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 2: Aircraft Information -->
      <div *ngIf="currentStep === 2" class="space-y-6">
        <div class="flex items-center gap-4">
          <h4 class="font-medium text-lg text-text-black">Involved aircraft</h4>
          <button (click)="openNew()"
            class="bg-primary-500 text-white text-sm px-5 py-1 h-[34px] rounded-lg active:bg-primary-600 flex items-center gap-1">
            <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M16.3333 2.5H4.66667C3.74619 2.5 3 3.24619 3 4.16667V15.8333C3 16.7538 3.74619 17.5 4.66667 17.5H16.3333C17.2538 17.5 18 16.7538 18 15.8333V4.16667C18 3.24619 17.2538 2.5 16.3333 2.5Z"
                stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              <path d="M7.16669 10H13.8334" stroke="white" stroke-width="2" stroke-linecap="round"
                stroke-linejoin="round" />
              <path d="M10.5 6.66666V13.3333" stroke="white" stroke-width="2" stroke-linecap="round"
                stroke-linejoin="round" />
            </svg>
            Add aircraft
          </button>
        </div>
        <div [formGroup]="aircraftForm">
          <div formArrayName="involvedAircraft">
            @if(involvedAircraftControls.length >= 1){
            <div *ngFor="let aircraft of involvedAircraftControls; let i = index" class="px-4">
              <!-- Display Aircraft Summary -->
              <div class="flex gap-8 items-center mb-4">
                <div class="flex items-center gap-2">
                  <p class="text-text-black">
                    <span class="pr-2">{{ i + 1 }}.</span>{{ aircraft.value.manufacturer }}
                    {{ aircraft.value.registrationMark }}
                  </p>
                </div>
                <div class="flex items-center gap-2">
                  <button type="button" (click)="editAircraft(aircraft, i)"
                    class="border border-primary-500 text-primary-500 px-4 py-2 rounded-lg text-xs flex items-center gap-2 scale-95 active:scale-90">
                    <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M13.4583 2.87498C13.6662 2.66706 13.9131 2.50212 14.1848 2.38959C14.4564 2.27706 14.7476 2.21915 15.0416 2.21915C15.3357 2.21915 15.6269 2.27706 15.8985 2.38959C16.1702 2.50212 16.4171 2.66706 16.625 2.87498C16.8329 3.08291 16.9978 3.32976 17.1104 3.60142C17.2229 3.87309 17.2808 4.16427 17.2808 4.45832C17.2808 4.75237 17.2229 5.04354 17.1104 5.31521C16.9978 5.58688 16.8329 5.83372 16.625 6.04165L5.93748 16.7291L1.58331 17.9166L2.77081 13.5625L13.4583 2.87498Z"
                        stroke="#2185B4" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    Edit
                  </button>
                  <button type="button" (click)="removeAircraft(i)"
                    class="border border-red-400 text-red-400 px-4 py-2 rounded-lg text-xs flex items-center gap-2 scale-95 active:scale-90">
                    <svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M2.375 5.25H16.625" stroke="#FF8C8C" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round" />
                      <path
                        d="M15.0416 5.25V16.3333C15.0416 17.125 14.25 17.9167 13.4583 17.9167H5.54165C4.74998 17.9167 3.95831 17.125 3.95831 16.3333V5.25"
                        stroke="#FF8C8C" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                      <path
                        d="M6.33331 5.25001V3.66668C6.33331 2.87501 7.12498 2.08334 7.91665 2.08334H11.0833C11.875 2.08334 12.6666 2.87501 12.6666 3.66668V5.25001"
                        stroke="#FF8C8C" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                      <path d="M7.91669 9.20834V13.9583" stroke="#FF8C8C" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round" />
                      <path d="M11.0833 9.20834V13.9583" stroke="#FF8C8C" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round" />
                    </svg>
                    Remove
                  </button>
                </div>
              </div>
            </div>
            } @else{
            <p class="text-xs font-light text-gray-700">
              Added aircrafts will appear here
            </p>
            }
          </div>
        </div>

        <!-- Add/Edit Aircraft Dialog -->
        <p-dialog [(visible)]="aircraftDialog" [style]="{width: '80vw'}" header="Aircraft Details" [modal]="true"
          styleClass="p-fluid">
          <ng-template pTemplate="content">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
              <!-- Manufacturer -->
              <div class="field">
                <label for="manufacturer">Manufacturer <span class="text-red-300">*</span></label>
                <!-- <input id="manufacturer" [(ngModel)]="currentAircraft.manufacturer" name="manufacturer" required
                  class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"
                  [ngClass]="{
                  'border-red-500': submitted && !currentAircraft.manufacturer,
                  'ng-invalid ng-dirty': submitted && !currentAircraft.manufacturer
                }" /> -->
                <p-autoComplete
                    id="manufacturer"
                    name="manufacturer"
                    [suggestions]="filteredManufacturers"
                    (completeMethod)="searchManufacturer($event)"
                    (onSelect)="onManufacturerSelect($event)"
                    [minLength]="2"
                    [(ngModel)]="currentAircraft.manufacturer"
                    [style]="{
                        'border': '1px solid #3333',
                        'border-radius': '0.8rem',
                        'padding': '0.5rem',
                        'width': '100%'
                    }"
                    class="w-full">
                </p-autoComplete>
                <p *ngIf="submitted && !currentAircraft.manufacturer" class="text-red-500 text-xs italic mt-1">
                  Manufacturer is required
                </p>
              </div>

              <!-- Aircraft Model -->
              <div class="field">
                <label for="model">Aircraft Model</label>
                <!-- <input id="model" [(ngModel)]="currentAircraft.model" name="model" required
                  class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"
                  /> -->
                  <p-dropdown 
                      id="model" 
                      [(ngModel)]="currentAircraft.model" 
                      name="model"
                      [options]="filteredModels"
                      [disabled]="!currentAircraft.manufacturer"
                      placeholder="Select Model"
                      optionLabel="label"
                      optionValue="value"
                      (onChange)="searchModel()"
                      [filter]="true"
                      [style]="{
                          'border': '1px solid #3333',
                          'border-radius': '10px',
                          'width': '100%'
                      }"
                      class="w-full">
                  </p-dropdown>
                <!-- <p *ngIf="submitted && !currentAircraft.model" class="text-red-500 text-xs italic mt-1">
                  Aircraft Model is required
                </p> -->
              </div>

              <!-- Operator (Optional) -->
              <div class="field">
                <label for="operator">Operator</label>
                <input id="operator" [(ngModel)]="currentAircraft.operator" name="operator"
                  class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors" />
              </div>

              <!-- Registration Mark (Optional) -->
              <div class="field">
                <label for="registrationMark">Registration Mark</label>
                <input id="registrationMark" [(ngModel)]="currentAircraft.registrationMark" name="registrationMark"
                  class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors" />
              </div>

              <!-- Operator Nationality (Optional) -->
              <div class="field">
                <label for="operatorNationality">Operator Nationality</label>
                <input id="operatorNationality" [(ngModel)]="currentAircraft.operatorNationality"
                  name="operatorNationality"
                  class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors" />
              </div>

              <!-- Intended Landing Date/Time (Optional) -->
              <div class="field">
                <label for="intendedLandingDateTime">Intended Landing Date/Time</label>
                <input type="datetime-local" id="intendedLandingDateTime"
                  [(ngModel)]="currentAircraft.intendedLandingDateTime" name="intendedLandingDateTime"
                  class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors" />
              </div>

              <!-- Intended Landing Point (Optional) -->
              <div class="field">
                <label for="intendedLandingPoint">Intended Landing Point</label>
                <input id="intendedLandingPoint" [(ngModel)]="currentAircraft.intendedLandingPoint"
                  name="intendedLandingPoint"
                  class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors" />
              </div>

              <!-- Last Departure Point (Optional) -->
              <div class="field">
                <label for="lastDeparturePoint">Last Departure Point</label>
                <input id="lastDeparturePoint" [(ngModel)]="currentAircraft.lastDeparturePoint"
                  name="lastDeparturePoint"
                  class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors" />
              </div>

              <!-- People on board section -->
              <div class="field col-span-2">
                <h4 class="font-semibold mb-2">People on Board</h4>
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label for="crewOnBoard">Crew on Board</label>
                    <input type="number" id="crewOnBoard" [(ngModel)]="currentAircraft.crewOnBoard" name="crewOnBoard"
                      min="0"
                      class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors" />

                  </div>
                  <div>
                    <label for="passengersOnBoard">Passengers on Board</label>
                    <input type="number" id="passengersOnBoard" [(ngModel)]="currentAircraft.passengersOnBoard"
                      name="passengersOnBoard" min="0"
                      class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors" />

                  </div>
                </div>
              </div>

              <!-- Casualties section -->
              <div class="field col-span-2">
                <h4 class="font-semibold mb-2">Casualties</h4>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <label for="crewInjured">Crew Injured</label>
                    <input type="number" id="crewInjured" [(ngModel)]="currentAircraft.crewInjured" name="crewInjured"
                      min="0"
                      class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors" />
                  </div>
                  <div>
                    <label for="crewPerished">Crew Perished</label>
                    <input type="number" id="crewPerished" [(ngModel)]="currentAircraft.crewPerished"
                      name="crewPerished" min="0"
                      class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors" />

                  </div>
                  <div>
                    <label for="passengersInjured">Passengers Injured</label>
                    <input type="number" id="passengersInjured" [(ngModel)]="currentAircraft.passengersInjured"
                      name="passengersInjured" min="0"
                      class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors" />
                  </div>
                  <div>
                    <label for="passengersPerished">Passengers Perished</label>
                    <input type="number" id="passengersPerished" [(ngModel)]="currentAircraft.passengersPerished"
                      name="passengersPerished" min="0"
                      class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors" />
                  </div>
                </div>
              </div>
            </div>
          </ng-template>

          <ng-template pTemplate="footer">
            <button class="text-text-white font-normal rounded-lg py-2 px-6 bg-gray-500 active:bg-gray-600"
              (click)="hideDialog()">
              Cancel
            </button>
            <button class="text-text-white font-normal ml-4 rounded-lg py-2 px-6 bg-primary-500 active:bg-primary-600"
              (click)="saveAircraft()">
              Save
            </button>
          </ng-template>
        </p-dialog>
        <!-- Confirmation Dialog -->
        <p-confirmDialog [style]="{ width: '450px' }"></p-confirmDialog>

      </div>

      <!-- Step 3: Occurrence Details -->
      <div *ngIf="currentStep === 3" class="space-y-6">
        <div [formGroup]="occurrenceDetailsForm">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- General Weather Conditions -->
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2">
                Describe the general weather conditions
              </label>
              <textarea formControlName="generalWeatherConditions"
                class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"></textarea>
            </div>

            <!-- Sky Coverage -->
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2">
                Indicate coverage of the sky and type of clouds
              </label>
              <textarea formControlName="skyCoverage"
                class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"></textarea>
            </div>

            <!-- Meteorological Condition -->
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2">
                Meteorological Condition
              </label>
              <select formControlName="meteologicalCondition"
                class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"
                [ngClass]="{
            'border-red-500':
              occurrenceDetailsForm.get('meteologicalCondition')?.invalid &&
              occurrenceDetailsForm.get('meteologicalCondition')?.touched
          }">
                <option value="">Select Meteorological Condition</option>
                <option *ngFor="let condition of meteologicalConditionOptions" [value]="condition">
                  {{ condition }}
                </option>
              </select>

            </div>

            <!-- Flight Rules -->
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2">
                Flight Rules
              </label>
              <select formControlName="flightRules"
                class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"
                [ngClass]="{
            'border-red-500':
              occurrenceDetailsForm.get('flightRules')?.invalid &&
              occurrenceDetailsForm.get('flightRules')?.touched
          }">
                <option value="">Select Flight Rules</option>
                <option *ngFor="let rule of flightRulesOptions" [value]="rule">
                  {{ rule }}
                </option>
              </select>

            </div>

            <!-- Occurrence Time -->
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2">
                Occurrence Time
              </label>
              <input type="datetime-local" formControlName="occurrenceTime"
                class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"
                [ngClass]="{
            'border-red-500':
              occurrenceDetailsForm.get('occurrenceTime')?.invalid &&
              occurrenceDetailsForm.get('occurrenceTime')?.touched
          }" />
              <p *ngIf="
            occurrenceDetailsForm.get('occurrenceTime')?.invalid &&
            occurrenceDetailsForm.get('occurrenceTime')?.touched
          " class="text-red-500 text-xs italic mt-1">
                Please provide a valid occurrence time
              </p>
            </div>

            <!-- Flight Phase -->
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2">
                Flight Phase
              </label>
              <select formControlName="flightPhase"
                class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors" >
              
              <option value="">Select Flight Phase</option>
              <option value="" *ngFor=" let phase of flightPhaseOptions" [value]="phase.key">
                {{phase.value}}
              </option>
              </select>
            </div>

            <!-- Operation Type -->
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2">
                Operation Type
              </label>
              <select formControlName="operationType"
                class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"
                [ngClass]="{
            'border-red-500':
              occurrenceDetailsForm.get('operationType')?.invalid &&
              occurrenceDetailsForm.get('operationType')?.touched
          }">
                <option value="">Select Operation Type</option>
                <option *ngFor="let type of operationTypeOptions" [value]="type.key">
                  {{ type.value }}
                </option>
              </select>
            </div>

            <!-- Latitude -->
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2">
                Latitude
              </label>
              <input type="text" formControlName="latitude"
                class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"
                [ngClass]="{
            'border-red-500':
              occurrenceDetailsForm.get('latitude')?.invalid &&
              occurrenceDetailsForm.get('latitude')?.touched
          }" />
              
            </div>

            <!-- Longitude -->
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2">
                Longitude 
              </label>
              <input type="text" formControlName="longitude"
                class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"
                [ngClass]="{
            'border-red-500':
              occurrenceDetailsForm.get('longitude')?.invalid &&
              occurrenceDetailsForm.get('longitude')?.touched
          }" />
              
            </div>

            <!-- Occurrence Location -->
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2">
                Occurrence Location 
              </label>
              <input type="text" formControlName="occurrenceLocation"
                class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"
                [ngClass]="{
            'border-red-500':
              occurrenceDetailsForm.get('occurrenceLocation')?.invalid &&
              occurrenceDetailsForm.get('occurrenceLocation')?.touched
          }" />
              
            </div>

            <!-- Dangerous Goods Carried On Board -->
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2">
                Dangerous Goods Carried On Board
              </label>
              <textarea formControlName="dangerousGoodCarriedOnBoard"
                class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"></textarea>
            </div>

            <!-- Ground People Injured -->
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2">
                Ground People Injured
              </label>
              <input type="number" formControlName="groundPeopleInjured"
                class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"
                min="0" />
            </div>

            <!-- Ground People Perished -->
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2">
                Ground People Perished
              </label>
              <input type="number" formControlName="groundPeoplePerished"
                class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"
                min="0" />
            </div>
          </div>
        </div>
      </div>

      <!-- Step 4: Summary and Confirmation -->
      <div *ngIf="currentStep === 4" class="space-y-6">
        <h2 class="text-xl text-text-black font-semibold mb-4">
          Review and Submit
        </h2>
        <div [formGroup]="summaryForm">
          <div class="flex items-center">
            <input type="checkbox" formControlName="termsAccepted" class="p-4 mr-2 w-4 h-4" id="accept" />
            <label for="accept" class="text-sm">I confirm that the information provided is accurate to the best of my
              knowledge</label>
          </div>
        </div>
      </div>

      <!-- Navigation Buttons -->
      <div class="flex justify-between mt-6">
        <button *ngIf="currentStep > 1" type="button" (click)="prevStep()"
          class="bg-gray-500 hover:bg-gray-600 text-white px-5 py-2 rounded-lg transition-all">
          Back
        </button>

        <button *ngIf="currentStep < totalSteps" type="button" (click)="nextStep()"
          class="bg-primary-500 hover:bg-primary-600 text-white px-5 py-2 rounded-lg ml-auto transition-all"
          [disabled]="!validateCurrentStep()">
          Continue
        </button>

        <button *ngIf="currentStep === totalSteps" type="submit"
          class="bg-primary-500 hover:bg-primary-600 text-white px-5 py-2 rounded-lg ml-auto transition-all"
          [disabled]="!validateAllForms()">
          Submit Report
        </button>
      </div>
    </div>
  </form>
  <p-toast></p-toast>
</div>