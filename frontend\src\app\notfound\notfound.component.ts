import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { Router, RouterModule } from '@angular/router';

@Component({
  selector: 'app-notfound',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule
  ],
  templateUrl: './notfound.component.html',
})
export class NotfoundComponent {
  constructor(
    private router: Router
  ) {}

  goBack(): void {

  }

  goHome(): void {
    this.router.navigate(['/']);
  }
}
