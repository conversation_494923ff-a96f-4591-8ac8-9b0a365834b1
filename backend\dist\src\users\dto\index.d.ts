import { Role } from '@prisma/client';
export declare class CreateUserDto {
    name: string;
    email: string;
    telephone: string;
    role: Role;
    profilePicture?: string;
}
export declare class UpdateUserDto {
    name?: string;
    email?: string;
    telephone?: string;
    role?: Role;
    profilePicture?: string;
}
export declare class ActivateAccountDto {
    token: string;
    password: string;
}
export declare class ResetPasswordRequestDto {
    email: string;
}
export declare class ResetPasswordConfirmDto {
    token: string;
    newPassword: string;
}
export declare class SearchDto {
    query: string;
    page: number;
    limit: number;
}
