import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { MessageService, ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { FileUploadModule } from 'primeng/fileupload';
import { InputTextModule } from 'primeng/inputtext';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { ReportsService } from './reports.service';
import { Report } from '../../../util/@types';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-reports',
  standalone: true,
  imports: [
    CommonModule,
    HttpClientModule,
    FormsModule,
    ButtonModule,
    TableModule,
    DialogModule,
    ToastModule,
    ConfirmDialogModule,
    InputTextModule,
    DropdownModule,
    FileUploadModule,
    PdfViewerModule,
    RouterLink
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './reports.component.html',
})
export class ReportsComponent implements OnInit {
  reports: Report[] = [];
  filteredReports: Report[] = [];
  searchQuery = '';
  selectedType = '';

  constructor(
    private reportsService: ReportsService,
    private messageService: MessageService,
  ) { }

  ngOnInit(): void {
    this.loadReports();
  }

  filterReports() {
    // Start with all reports
    this.filteredReports = [...this.reports];

    // Apply search query filter
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase().trim();
      this.filteredReports = this.filteredReports.filter(report => 
        report.referenceNumber?.toLowerCase().includes(query) ||
        report.occurrenceType?.toLowerCase().includes(query) ||
        report.position?.toLowerCase().includes(query) ||
        report.aircraftInfo?.[0]?.registrationMarks?.toLowerCase().includes(query)
      );
    }

    // Apply type filter
    if (this.selectedType) {
      this.filteredReports = this.filteredReports.filter(report => 
        report.occurrenceType === this.selectedType
      );
    }
  }

  async loadReports() {
    try {
      const data = await this.reportsService.getAllReports();
      this.reports = data;
      this.filteredReports = data; // Initialize filtered reports with all reports
    } catch (error) {
      console.error(error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Error loading reports'
      });
    }
  }

  onSearch(event: Event) {
    this.searchQuery = (event.target as HTMLInputElement).value;
    this.filterReports();
  }

  onTypeChange(event: Event) {
    this.selectedType = (event.target as HTMLSelectElement).value;
    this.filterReports();
  }

  formatDateTime(date: string): string {
    return new Date(date).toLocaleString();
  }

  formatDate(date: string): string {
    return new Date(date).toLocaleDateString('en-US');
  }
}