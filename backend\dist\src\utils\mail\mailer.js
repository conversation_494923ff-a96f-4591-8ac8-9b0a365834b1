"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const common_1 = require("@nestjs/common");
const nodemailer = require("nodemailer");
const env_1 = require("../env");
const mailer = nodemailer.createTransport({
    host: env_1.env.SMTP_SERVER,
    port: env_1.env.SMTP_PORT,
});
mailer.verify((error, success) => {
    if (error) {
        common_1.Logger.error('SMTP Connection failed', 'NodeMailerConn');
    }
    else if (success) {
        common_1.Logger.log('Smtp service connected', 'NodeMailerConn');
    }
});
exports.default = mailer;
//# sourceMappingURL=mailer.js.map