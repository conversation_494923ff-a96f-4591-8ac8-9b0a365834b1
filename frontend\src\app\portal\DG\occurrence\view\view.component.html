<div class="w-full h-full p-8 flex flex-col gap-4 overflow-scroll">
    <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold text-gray-700">Occurrence Information</h2>
    </div>

    <div class="pt-4 flex flex-col gap-4">
        <!-- AAID Information Section -->
        <div class="p-4 rounded-lg bg-white">
            <h4 class="text-gray-700 font-semibold">AAID Information</h4>
            <div class="pt-2">
                <p class="text-gray-600"><strong>Reference Number:</strong> {{occurrence.referenceNumber ?? "N/A"}}</p>
                <p class="text-gray-600"><strong>Type:</strong> {{occurrence.type ?? "N/A"}}</p>
                <p class="text-gray-600"><strong>Category:</strong> {{occurrence.occurrenceCategory?.category ?? "N/A"}}</p>
            </div>
        </div>

        <!-- General Information Section -->
        <div class="p-4 rounded-lg bg-white">
            <h4 class="text-gray-700 font-semibold">General Information</h4>
            <div class="pt-2 grid grid-cols-2 gap-4">
                <div>
                    <h5 class="font-semibold text-gray-700 mb-2">Reporter Information</h5>
                    <p class="text-gray-600"><strong>Name:</strong> {{occurrence.reporterName}}</p>
                    <p class="text-gray-600"><strong>Email:</strong> {{occurrence.reporterEmail}}</p>
                    <p class="text-gray-600"><strong>Phone:</strong> {{occurrence.reporterPhone}}</p>
                </div>
                <div>
                    <h5 class="font-semibold text-gray-700 mb-2">Pilot In Command Information</h5>
                    <p class="text-gray-600"><strong>Name:</strong> {{occurrence.pilotInCommandName ?? "N/A"}}</p>
                    <p class="text-gray-600"><strong>Email:</strong> {{occurrence.pilotInCommandEmail ?? "N/A"}}</p>
                    <p class="text-gray-600"><strong>Phone:</strong> {{occurrence.pilotInCommandPhone ?? "N/A"}}</p>
                </div>
            </div>
        </div>

        <!-- Involved Aircraft Section -->
        <div class="p-4 rounded-lg bg-white">
            <h4 class="text-gray-700 font-semibold">Involved Aircraft</h4>
            <div class="pt-2">
                <p-table [value]="involvedAircraft" [tableStyle]="{'min-width': '50rem'}">
                    <ng-template pTemplate="header">
                        <tr>
                            <th>Manufacturer</th>
                            <th>Model</th>
                            <th>Registration Mark</th>
                            <th>Actions</th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-aircraft>
                        <tr>
                            <td>{{aircraft.manufacturer}}</td>
                            <td>{{aircraft.model}}</td>
                            <td>{{aircraft.registrationMark ?? 'N/A'}}</td>
                            <td>
                                <button class="py-2 px-3 bg-primary-500 rounded-lg text-white text-sm" (click)="showAircraftDetails(aircraft)">View details</button>
                            </td>
                        </tr>
                    </ng-template>
                </p-table>
            </div>
        </div>

        <div class="p-4 rounded-lg bg-white">
            <h4 class="text-gray-700 font-semibold">Ground People</h4>
            <div class="pt-2">
                <p class="text-gray-600"><strong>Injured:</strong> {{occurrence.groundPeopleInjured}}</p>
                <p class="text-gray-600"><strong>Perished:</strong> {{occurrence.groundPeoplePerished}}</p>
            </div>
        </div>

        <!-- Occurrence Details Section -->
        <div class="p-4 rounded-lg bg-white">
            <h4 class="text-gray-700 font-semibold">Occurrence Details</h4>
            <div class="pt-2 grid grid-cols-2 gap-4">
                <div>
                    <p class="text-gray-600"><strong>Occurrence Time:</strong> {{formatDate(occurrence.occurrenceTime)}}</p>
                    <p class="text-gray-600"><strong>Operation Type:</strong> {{occurrence.operationType}}</p>
                    <p class="text-gray-600"><strong>Flight Rules:</strong> {{occurrence.flightRules}}</p>
                    <p class="text-gray-600"><strong>Meteorological Condition:</strong> {{occurrence.meteologicalCondition}}</p>
                </div>
                <div>
                    <p class="text-gray-600"><strong>Occurrence Location:</strong> {{occurrence.occurrenceLocation}}</p>
                    <p class="text-gray-600"><strong>Latitude:</strong> {{occurrence.latitude ?? "N/A"}}</p>
                    <p class="text-gray-600"><strong>Longitude:</strong> {{occurrence.longitude ?? "N/A"}}</p>
                    <p class="text-gray-600"><strong>Flight Phase:</strong> {{occurrence.flightPhase ?? "N/A"}}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Aircraft Details Dialog -->
    <p-dialog [(visible)]="displayAircraftDialog" [modal]="true" [style]="{width: '50vw'}" header="Aircraft Details">
        <div *ngIf="selectedAircraft" class="grid grid-cols-2 gap-4">
            <div>
                <h5 class="font-semibold text-gray-700">Basic Information</h5>
                <p class="text-gray-600"><strong>Manufacturer:</strong> {{selectedAircraft.manufacturer}}</p>
                <p class="text-gray-600"><strong>Model:</strong> {{selectedAircraft.model}}</p>
                <p class="text-gray-600"><strong>Registration Mark:</strong> {{selectedAircraft.registrationMark ?? 'N/A'}}</p>
                <p class="text-gray-600"><strong>Operator:</strong> {{selectedAircraft.operator ?? 'N/A'}}</p>
                <p class="text-gray-600"><strong>Operator Nationality:</strong> {{selectedAircraft.operatorNationality ?? 'N/A'}}</p>
            </div>
            <div>
                <h5 class="font-semibold text-gray-700">Operational Details</h5>
                <p class="text-gray-600"><strong>Last Departure Point:</strong> {{selectedAircraft.lastDeparturePoint ?? 'N/A'}}</p>
                <p class="text-gray-600"><strong>Intended Landing Point:</strong> {{selectedAircraft.intendedLandingPoint ?? 'N/A'}}</p>
                <p class="text-gray-600"><strong>Intended Landing Date/Time:</strong>
                    @if(selectedAircraft.intendedLandingDateTime){
                    {{formatDate(selectedAircraft.intendedLandingDateTime)}}
                    }@else{
                    {{"N/A"}}
                    }
                </p>
            </div>
            <div class="col-span-2">
                <h5 class="font-semibold text-gray-700">Crew and Passenger Information</h5>
                <div class="grid grid-cols-2">
                    <div>
                        <p class="text-gray-600"><strong>Crew On Board:</strong> {{selectedAircraft.crewOnBoard ?? 'N/A'}}</p>
                        <p class="text-gray-600"><strong>Crew Injured:</strong> {{selectedAircraft.crewInjured ?? 'N/A'}}</p>
                        <p class="text-gray-600"><strong>Crew Perished:</strong> {{selectedAircraft.crewPerished ?? 'N/A'}}</p>
                    </div>
                    <div>
                        <p class="text-gray-600"><strong>Passengers On Board:</strong> {{selectedAircraft.passengersOnBoard ?? 'N/A'}}</p>
                        <p class="text-gray-600"><strong>Passengers Injured:</strong> {{selectedAircraft.passengersInjured ?? 'N/A'}}</p>
                        <p class="text-gray-600"><strong>Passengers Perished:</strong> {{selectedAircraft.passengersPerished ?? 'N/A'}}</p>
                    </div>
                </div>
            </div>
        </div>
    </p-dialog>

    <!-- Toast for error messages -->
    <p-toast></p-toast>
</div>