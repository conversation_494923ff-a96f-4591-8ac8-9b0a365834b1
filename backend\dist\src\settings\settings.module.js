"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsModule = void 0;
const common_1 = require("@nestjs/common");
const settings_service_1 = require("./settings.service");
const settings_controller_1 = require("./settings.controller");
const prisma_service_1 = require("../prisma/prisma.service");
const aircraft_controller_1 = require("./aircraft/aircraft.controller");
const aircraft_service_1 = require("./aircraft/aircraft.service");
const signature_controller_1 = require("./signature/signature.controller");
const signature_service_1 = require("./signature/signature.service");
const countries_service_1 = require("./countries/countries.service");
const countries_controller_1 = require("./countries/countries.controller");
let SettingsModule = class SettingsModule {
};
exports.SettingsModule = SettingsModule;
exports.SettingsModule = SettingsModule = __decorate([
    (0, common_1.Module)({
        providers: [settings_service_1.SettingsService, aircraft_service_1.AircraftService, signature_service_1.SignatureService, countries_service_1.CountriesService, prisma_service_1.PrismaService],
        controllers: [settings_controller_1.SettingsController, aircraft_controller_1.AircraftController, signature_controller_1.SignatureController, countries_controller_1.CountriesController],
    })
], SettingsModule);
//# sourceMappingURL=settings.module.js.map