import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MessageService, ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { ActivatedRoute } from '@angular/router';
import { Occurrence, Aircraft, Document } from '../../../../util/@types';
import { OccurrencesService } from '../../../app/occurrences/occurrences.service';

@Component({
  selector: 'app-dg-view-occurrence',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    TableModule,
    DialogModule,
    ToastModule,
    ConfirmDialogModule
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './view.component.html',
})
export class DgViewOccurrencesComponent implements OnInit {
  occurrence!: Occurrence;
  loading: boolean = true;
  
  // Popup related properties
  displayAircraftDialog: boolean = false;
  selectedAircraft: Aircraft | null = null;

  // Additional view-related properties
  involvedAircraft: Aircraft[] = [];
  documents: Document[] = [];

  constructor(
    private occurrenceService: OccurrencesService,
    private messageService: MessageService,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.loadOccurrence();
  }

  async loadOccurrence() {
    try {
      this.loading = true;
      const id = this.route.snapshot.paramMap.get('id') || '';
      
      if (!id) {
        throw new Error('No occurrence ID provided');
      }

      this.occurrence = await this.occurrenceService.getOccurrence(id);
      
      this.involvedAircraft = this.occurrence.involvedAircraft || [];
      
      this.loading = false;
    } catch (error) {
      this.handleError('Failed to load occurrence details', error);
      this.loading = false;
    }
  }

  showAircraftDetails(aircraft: Aircraft) {
    this.selectedAircraft = aircraft;
    this.displayAircraftDialog = true;
  }

  formatDate(date: Date | string | undefined): string {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getAircraftDetails(aircraft: Aircraft): string {
    return `${aircraft.manufacturer} ${aircraft.model} (${aircraft.registrationMark || 'N/A'})`;
  }

  private handleError(message: string, error: any) {
    console.error(error);
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: error.response?.data?.message || error.message || message
    });
  }
}