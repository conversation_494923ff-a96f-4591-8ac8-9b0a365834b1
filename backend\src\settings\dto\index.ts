import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsEmail, IsOptional, IsString } from 'class-validator';

export class CreateContactInfoDto {
  @ApiProperty({ description: 'Name of the contact' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Description of the contact', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Email of the contact' })
  @IsEmail()
  email: string;

  @ApiProperty({ description: 'Telephone of the contact' })
  @IsString()
  telephone: string;
}

export class UpdateContactInfoDto extends PartialType(CreateContactInfoDto) {}

export class CreateOccurrenceCategoryDto {
  @ApiProperty({ description: 'Category name' })
  @IsString()
  category: string;

  @ApiProperty({ description: 'Category description' })
  @IsString()
  description: string;
}

export class UpdateOccurrenceCategoryDto extends PartialType(CreateOccurrenceCategoryDto) {}
