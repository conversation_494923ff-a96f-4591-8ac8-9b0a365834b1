"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OccurrenceController = void 0;
const common_1 = require("@nestjs/common");
const dto_1 = require("./dto");
const occurrence_service_1 = require("./occurrence.service");
const swagger_1 = require("@nestjs/swagger");
const dto_2 = require("../users/dto");
let OccurrenceController = class OccurrenceController {
    constructor(occurrenceService) {
        this.occurrenceService = occurrenceService;
    }
    submitOccurrence(createOccurrenceDto) {
        return this.occurrenceService.createOccurrence(createOccurrenceDto);
    }
    search(searchDto) {
        const { query, page, limit } = searchDto;
        return this.occurrenceService.searchOccurrence(query);
    }
    async getAllOccurrences() {
        return this.occurrenceService.getOccurrences();
    }
    async getOccurrencebyId(id) {
        return this.occurrenceService.getOccurrencesById(id);
    }
    async updateOccurrence(id, updateOccurrenceDto) {
        return this.occurrenceService.updateOccurrence(id, updateOccurrenceDto);
    }
    async deleteOccurrence(id) {
        return this.occurrenceService.deleteOccurrence(id);
    }
    async generateRefNumber(id) {
        return this.occurrenceService.generateReferenceNumber(id);
    }
};
exports.OccurrenceController = OccurrenceController;
__decorate([
    (0, common_1.Post)('public/submit'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateOccurrenceDto]),
    __metadata("design:returntype", void 0)
], OccurrenceController.prototype, "submitOccurrence", null);
__decorate([
    (0, common_1.Get)('search'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_2.SearchDto]),
    __metadata("design:returntype", void 0)
], OccurrenceController.prototype, "search", null);
__decorate([
    (0, common_1.Get)(''),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OccurrenceController.prototype, "getAllOccurrences", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], OccurrenceController.prototype, "getOccurrencebyId", null);
__decorate([
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateOccurrenceDto]),
    __metadata("design:returntype", Promise)
], OccurrenceController.prototype, "updateOccurrence", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], OccurrenceController.prototype, "deleteOccurrence", null);
__decorate([
    (0, common_1.Post)('refnumber/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], OccurrenceController.prototype, "generateRefNumber", null);
exports.OccurrenceController = OccurrenceController = __decorate([
    (0, swagger_1.ApiTags)("Occurence"),
    (0, common_1.Controller)('occurrence'),
    __metadata("design:paramtypes", [occurrence_service_1.OccurrenceService])
], OccurrenceController);
//# sourceMappingURL=occurrence.controller.js.map