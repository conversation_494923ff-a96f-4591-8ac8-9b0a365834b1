import { Injectable } from '@angular/core';
import { AxiosService } from '../../../util/axios/axios.service';
import { Country, Report, User } from '../../../util/@types/index';
import { from, map, Observable } from 'rxjs';
import axios from 'axios';

@Injectable({
  providedIn: 'root'
})
export class ReportsService {
  constructor(private axiosService: AxiosService) { }

  async getAllReports(): Promise<Report[]> {
    try {
      const response = await this.axiosService.axios.get('/reports');
      return response.data.data;
    } catch (error) {
      throw error;
    }
  }

  async getReportById(id: string): Promise<Report> {
    try {
      const response = await this.axiosService.axios.get(`/reports/${id}`);
      return response.data.data;
    } catch (error) {
      throw error;
    }
  }

  async createReport(data: any): Promise<Report> {
    try {
      const selectedAddressedTo = [];
      if (data.addressedToRegistry) selectedAddressedTo.push('StateOfRegistry');
      if (data.addressedToDesign) selectedAddressedTo.push('StateOfDesign');
      if (data.addressedToManufacturer) selectedAddressedTo.push('StateOfManufacturer');
      if (data.addressedToOperator) selectedAddressedTo.push('StateOfOperator');
      if (data.addressedToICAO) selectedAddressedTo.push('ICAO');
  
      const reportData = {
        addressedTo: selectedAddressedTo,
        referenceNumber: data.referenceNumber,
        stateOfRegistry: data.stateOfRegistry.name,
        stateOfDesign: data.stateOfDesignCountry.name,
        stateOfManufacturer: data.stateOfManufacturerCountry.name,
        stateOfOperator: data.stateOfOperatorCountry.name,
        occurrenceType: data.occurrenceType,
        occurrenceDate: data.occurrenceDate,
        occurrenceTimeUTC: this.combineDateTime(data.occurrenceDate, data.occurrenceTimeUTC),
        occurrenceTimeLocal: this.combineDateTime(data.occurrenceDate, data.occurrenceTimeLocal),
        position: data.position,
        latitude: data.latitude,
        longitude: data.longitude,
        occurrenceDescription: data.occurrenceDescription,
        damageExtent: data.damageExtent,
        dangerousGoodsPresent: data.dangerousGoodsPresent,
        dangerousGoodsDescription: data.dangerousGoodsDescription,
        aircraftInfo: data.aircraftInfo.map((aircraft: any) => ({
          manufacturer: aircraft.aircraftDetails.manufacturer,
          model: aircraft.aircraftDetails.model,
          nationality: aircraft.aircraftDetails.nationality,
          registrationMarks: aircraft.aircraftDetails.registrationMarks,
          serialNumber: aircraft.aircraftDetails.serialNumber,
          owner: aircraft.aircraftDetails.owner,
          operator: aircraft.aircraftDetails.operator,
          hirer: aircraft.aircraftDetails.hirer,
          pilotQualification: aircraft.crewDetails.pilotQualification,
          crewNationality: aircraft.crewDetails.crewNationality,
          passengerNationality: aircraft.crewDetails.passengerNationality,
          lastDeparturePoint: aircraft.flightDetails.lastDeparturePoint,
          intendedLandingPoint: aircraft.flightDetails.intendedLandingPoint,
          casualties: {
            crew: aircraft.casualties.crew,
            passengers: aircraft.casualties.passengers,
          }
        })),
        groundPeopleInjured: data.groundPeopleInjured,
        groundPeoplePerished: data.groundPeoplePerished,
        investigationExtent: data.investigationExtent,
        investigationDelegation: data.investigationDelegation,
        areaCharacteristics: data.areaCharacteristics,
        accessRequirements: data.accessRequirements,
        originatingAuthority: data.originatingAuthority,
        investigatorName: data.investigatorInCharge.name.name,
        investigatorMobile: data.investigatorInCharge.mobile,
        investigatorEmail: data.investigatorInCharge.email
      };
  
      const response = await this.axiosService.axios.post('/reports', reportData);
      return response.data.data;
    } catch (error) {
      console.error('Error creating report:', error);
      throw error;
    }
  }

  async updateReport(id: string, data: any): Promise<Report> {
    try {
      const selectedAddressedTo = [];
      if (data.addressedToRegistry) selectedAddressedTo.push('StateOfRegistry');
      if (data.addressedToDesign) selectedAddressedTo.push('StateOfDesign');
      if (data.addressedToManufacturer) selectedAddressedTo.push('StateOfManufacturer');
      if (data.addressedToOperator) selectedAddressedTo.push('StateOfOperator');
      if (data.addressedToICAO) selectedAddressedTo.push('ICAO');
  
      const updateData: { [key in keyof typeof data]?: any } = {
        addressedTo: selectedAddressedTo,
        referenceNumber: data.referenceNumber,
        stateOfRegistry: data.stateOfRegistry?.name,
        stateOfDesign: data.stateOfDesignCountry?.name,
        stateOfManufacturer: data.stateOfManufacturerCountry?.name,
        stateOfOperator: data.stateOfOperatorCountry?.name,
        occurrenceType: data.occurrenceType,
        occurrenceDate: data.occurrenceDate,
        occurrenceTimeUTC: data.occurrenceTimeUTC ? 
          this.combineDateTime(data.occurrenceDate, data.occurrenceTimeUTC) : undefined,
        occurrenceTimeLocal: data.occurrenceTimeLocal ? 
          this.combineDateTime(data.occurrenceDate, data.occurrenceTimeLocal) : undefined,
        position: data.position,
        latitude: data.latitude,
        longitude: data.longitude,
        occurrenceDescription: data.occurrenceDescription,
        damageExtent: data.damageExtent,
        dangerousGoodsPresent: data.dangerousGoodsPresent,
        dangerousGoodsDescription: data.dangerousGoodsDescription,
        aircraftInfo: data.aircraftInfo?.map((aircraft: any) => ({
          manufacturer: aircraft.aircraftDetails?.manufacturer,
          model: aircraft.aircraftDetails?.model,
          nationality: aircraft.aircraftDetails?.nationality,
          registrationMarks: aircraft.aircraftDetails?.registrationMarks,
          serialNumber: aircraft.aircraftDetails?.serialNumber,
          owner: aircraft.aircraftDetails?.owner,
          operator: aircraft.aircraftDetails?.operator,
          hirer: aircraft.aircraftDetails?.hirer,
          pilotQualification: aircraft.crewDetails?.pilotQualification,
          crewNationality: aircraft.crewDetails?.crewNationality,
          passengerNationality: aircraft.crewDetails?.passengerNationality,
          lastDeparturePoint: aircraft.flightDetails?.lastDeparturePoint,
          intendedLandingPoint: aircraft.flightDetails?.intendedLandingPoint,
          casualties: {
            crew: aircraft.casualties?.crew,
            passengers: aircraft.casualties?.passengers,
          }
        })),
        groundPeopleInjured: data.groundPeopleInjured,
        groundPeoplePerished: data.groundPeoplePerished,
        investigationExtent: data.investigationExtent,
        investigationDelegation: data.investigationDelegation,
        areaCharacteristics: data.areaCharacteristics,
        accessRequirements: data.accessRequirements,
        originatingAuthority: data.originatingAuthority,
        investigatorName: data.investigatorInCharge?.name?.name,
        investigatorMobile: data.investigatorInCharge?.mobile,
        investigatorEmail: data.investigatorInCharge?.email
      };
  
      // Remove undefined values
      (Object.keys(updateData) as (keyof typeof updateData)[]).forEach(key => {
        if (updateData[key as string] === undefined) {
          delete updateData[key as string];
        }
      });
  
      const response = await this.axiosService.axios.patch(`/reports/${id}`, updateData);
      return response.data.data;
    } catch (error) {
      console.error('Error updating report:', error);
      throw error;
    }
  }

  async deleteReport(id: string): Promise<void> {
    try {
      await this.axiosService.axios.delete(`/reports/${id}`);
    } catch (error) {
      throw error;
    }
  }

  async submitForApproval(id: string): Promise<void>{
    try {
      await this.axiosService.axios.patch(`/reports/submit/${id}`)
    } catch (error) {
     throw error; 
    }
  }

  async approve(id: string, comment?: string){
    try {
      await this.axiosService.axios.patch(`/reports/approve/${id}`, {
        comment
      })
    } catch (error) {
      throw error
    }
  }

  async revert(id: string, comment?: string){
    try {
      await this.axiosService.axios.patch(`/reports/revert/${id}`, {
        comment
      })
    } catch (error) {
      throw error
    }
  }

  private combineDateTime(date: string, time: string): string {
    const [hours, minutes] = time.split(':');
    const dateObj = new Date(date);
    dateObj.setHours(Number(hours), Number(minutes));
    return dateObj.toISOString();
  }

  getCountries(): Observable<Country[]> {
    return from(this.axiosService.axios.get('/countries')).pipe(
      map(response => response.data.map((country: any) => ({
        name: country.name,
        flag: country.flag
      })))
    );
  }

  getInvestigators(): Observable<User[]>{
    return from(this.axiosService.axios.get('/users/investigators')).pipe(
      map(reponse => reponse.data.data)
    )
  }
}