import { JwtService } from '@nestjs/jwt';
import { PrismaService } from 'src/prisma/prisma.service';
import { ApiResponse } from 'src/utils/@types';
import { User } from '@prisma/client';
import { MailService } from 'src/utils/mail/mail.service';
import { SmsService } from 'src/utils/sms/sms.service';
export declare class AuthenticationService {
    private readonly prisma;
    private jwtService;
    private mailService;
    private smsService;
    constructor(prisma: PrismaService, jwtService: JwtService, mailService: MailService, smsService: SmsService);
    private generateVerificationCode;
    sendVerificationCode(user: User, type: 'ACTIVATION' | 'PASSWORD_RESET'): Promise<ApiResponse<any>>;
    login(identifier: string, password: string, req: Request): Promise<ApiResponse<any>>;
    verifyMultiFactorCode(userId: string, code: string): Promise<ApiResponse<{
        token: string;
        user: User;
    }>>;
    generateTwoFactorSecret(): {
        secret: string;
        otpAuthUrl: string;
    };
    generateQRCode(otpAuthUrl: string): Promise<string>;
    private generateLoginToken;
    verifyTwoFactorToken(secret: string, token: string): boolean;
    private logLoginAttempt;
}
