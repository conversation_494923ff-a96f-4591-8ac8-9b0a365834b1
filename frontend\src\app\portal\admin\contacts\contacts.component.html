<div class="w-full h-full p-8 flex flex-col gap-4 overflow-scroll">
    <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold text-gray-700">Contact information</h2>
        <button pRipple (click)="openContactsModal('create')"
            class="flex items-center gap-2 py-2 px-3 rounded-lg text-sm font-medium text-text-white bg-primary active:scale-95">
            <i class="pi pi-plus"></i>
            Add contact
        </button>
    </div>
    <div class="pt-12">
        <div class="flex items-center gap-4">
            <div class="relative w-full">
                <i class="pi pi-search text-lg absolute top-2 left-3 text-gray-700"></i>
                <input
                    class="outline-none bg-transparent py-2 px-3 pl-10 border border-gray-300 rounded-lg  text-gray-700 w-full focus:ring-1 focus:ring-primary-500"
                    type="search" placeholder="Search contacts...">
            </div>
            <!-- <button
                class="border border-gray-300 rounded-lg text-gray-700 flex items-center gap-1 p-2 px-3 active:ring ring-primary-500">
                <i class="pi pi-filter"></i>
                Filter
            </button> -->
        </div>

        <!-- table -->
        <div class="mt-8">
            <p-table [value]="contacts" [paginator]="true" [rows]="10" [showCurrentPageReport]="true"
                [totalRecords]="totalContacts" (onPage)="onPageChange($event)"
                currentPageReportTemplate="Showing {first} to {last} of {totalRecords} contact(s)"
                [rowsPerPageOptions]="[10, 25, 50]">
                <ng-template pTemplate="header">
                    <tr>
                        <th class="font-medium text-gray-700">Name</th>
                        <th class="font-medium text-gray-700">Email</th>
                        <th class="font-medium text-gray-700">Phone</th>
                        <th class="font-medium text-gray-700">Description</th>
                        <th class="font-medium text-gray-700">Actions</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-contact>
                    <tr class="">
                        <td class="font-normal border-b">{{contact.name}}</td>
                        <td class="font-normal border-b">{{contact.email}}</td>
                        <td class="font-normal border-b">{{contact.telephone}}</td>
                        <td class="font-normal border-b">{{contact.description}}</td>
                        
                        <td class="font-normal border-b " class="flex items-center gap-2">
                            <button (click)="openContactsModal('edit', contact)"
                                class="bg-primary-50 text-primary-500 py-2 px-3 rounded-lg text-lg active:scale-95 active:ring ring-primary-200"
                                title="edit user">
                                <i class="pi pi-user-edit"></i>
                            </button>
                            <button (click)="confirmDelete(contact)"
                                class="bg-red-100 text-red-400 py-2 px-3 rounded-lg text-lg active:scale-95 active:ring ring-red-400"
                                title="delete user">
                                <i class="pi pi-trash"></i>
                            </button>
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="5">No contacts</td>
                    </tr>
                </ng-template>
            </p-table>
            <p-dialog [(visible)]="contactsModalVisible" [modal]="true" [header]="isEditMode ? 'Edit Contact' : 'Add New Contact'"
                [style]="{width: '450px'}">
                <ng-template pTemplate="content">
                    <div class="flex flex-col gap-4">
                        <div class="flex flex-col gap-2">
                            <label for="name">Name *</label>
                            <input 
                            placeholder="Contact name"
                            class="w-full outline-none border border-gray-300 rounded-md py-2 px-3 text-gray-800 focus:ring-1 focus:ring-primary "
                            id="name" [(ngModel)]="currentContact.name" type="text" />
                        </div>

                        <div class="flex flex-col gap-2">
                            <label for="description">Description</label>
                            <input 
                            placeholder="Description (optional)"
                            class="w-full outline-none border border-gray-300 rounded-md py-2 px-3 text-gray-800 focus:ring-1 focus:ring-primary "
                            id="description" [(ngModel)]="currentContact.description" type="text" />
                        </div>

                        <div class="flex flex-col gap-2">
                            <label for="email">Email *</label>
                            <input 
                            placeholder="Email address"
                            class="w-full outline-none border border-gray-300 rounded-md py-2 px-3 text-gray-800 focus:ring-1 focus:ring-primary "
                            id="email" [(ngModel)]="currentContact.email" type="email" />
                        </div>


                        <div class="flex flex-col gap-2">
                            <label for="telephone">Telephone *</label>
                            <input 
                            placeholder="Telephone number"
                            class="w-full outline-none border border-gray-300 rounded-md py-2 px-3 text-gray-800 focus:ring-1 focus:ring-primary "
                            id="telephone" [(ngModel)]="currentContact.telephone" type="text" />
                        </div>
                        
                    </div>
                </ng-template>
                <ng-template pTemplate="footer">
                    <button class="p-2 px-5 rounded-lg text-text-white bg-gray-600 active:scale-95" (click)="contactsModalVisible = false">
                        Cancel
                    </button>
                    <button class="p-2 px-4 rounded-lg text-text-white bg-primary-500 ml-4 active:scale-95"
                        (click)="saveContact()">
                        {{isEditMode ? 'Update' : 'Create'}}
                    </button>
                </ng-template>
            </p-dialog>
            <p-confirmDialog></p-confirmDialog>
            <p-toast position="top-right"></p-toast>
        </div>
    </div>
</div>