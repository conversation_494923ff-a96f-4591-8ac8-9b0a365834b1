import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MessageService, ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { User } from '../../auth/auth.service';
import { ContactsService } from './contacts.service';
import { Contact } from '../../../util/@types';

@Component({
  selector: 'app-contacts',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    TableModule,
    DialogModule,
    ToastModule,
    ConfirmDialogModule
  ],
  providers: [MessageService, ConfirmationService], 
  templateUrl: './contacts.component.html',
})
export class ContactsComponent {
  contacts: Contact[] = [];
  totalContacts: number = 0;
  currentPage: number = 1;
  pageSize: number = 10;

  contactsModalVisible: boolean = false;
  isEditMode: boolean = false;
  
  currentContact: Partial<Contact> = {};

  constructor(
    private contactsService: ContactsService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService
  ) {}

  ngOnInit(): void {
    this.loadContacts();
  }

  async loadContacts(page: number = 1) {
    try {
      const response = await this.contactsService.getContacts(page, this.pageSize);
      this.contacts = response.data;
      this.totalContacts = response.total;
    } catch (error) {
      this.handleError('Failed to load contacts', error);
    }
  }

  onPageChange(event: any) {
    this.currentPage = event.page + 1;
    this.pageSize = event.rows;
    this.loadContacts(this.currentPage);
  }

  openContactsModal(mode: 'create' | 'edit', contact?: Contact) {
    this.isEditMode = mode === 'edit';
    this.currentContact = this.isEditMode 
      ? { ...contact } 
      : { name: '', description: '', email: '' , telephone: ''};
    this.contactsModalVisible = true;
  }

  async saveContact() {
    try {
      if (this.isEditMode) {
        await this.contactsService.updateContact(
          this.currentContact.id!,
          this.currentContact.name!, 
          this.currentContact.description!, 
          this.currentContact.email!,
          this.currentContact.telephone!
        );
        this.messageService.add({
          severity: 'success', 
          summary: 'Contact Updated', 
          detail: 'Contact has been successfully updated.'
        });
      } else {
        await this.contactsService.createContact(
          this.currentContact.name!, 
          this.currentContact.description!, 
          this.currentContact.email!,
          this.currentContact.telephone!
        );
        this.messageService.add({
          severity: 'success', 
          summary: 'Contact Created', 
          detail: `New contact has been successfully created.`,
          life: 1000
        });
      }
      
      this.contactsModalVisible = false;
      this.loadContacts(this.currentPage);
    } catch (error) {
      this.handleError('Failed to save contact', error);
    }
  }

  confirmDelete(contact: Contact) {
    this.confirmationService.confirm({
      message: `Are you sure you want to delete contact ${contact.name}?`,
      header: 'Confirm Deletion',
      icon: 'pi pi-info-circle',
      acceptIcon:"none",
      rejectIcon:"none",
      acceptButtonStyleClass:"p-button-danger p-button-text",
      rejectButtonStyleClass:"p-button-text p-button-text mr-4", 
      accept: () => this.deleteContact(contact.id!)
    });
  }

  async deleteContact(contactId: string) {
    try {
      await this.contactsService.deleteContact(contactId);
      this.messageService.add({
        severity: 'success', 
        summary: 'Contact Deleted', 
        detail: 'Contact has been successfully deleted.'
      });
      this.loadContacts(this.currentPage);
    } catch (error) {
      this.handleError('Failed to delete contact', error);
    }
  }

  private handleError(message: string, error: any) {
    console.error(error);
    this.messageService.add({
      severity: 'error', 
      summary: 'Error', 
      detail: error.response.data.message || error.response.data.message[0] || error.message
    });
  }
}
