import { CreateOccurrenceDto, UpdateOccurrenceDto } from './dto';
import { OccurrenceService } from './occurrence.service';
import { SearchDto } from 'src/users/dto';
export declare class OccurrenceController {
    private occurrenceService;
    constructor(occurrenceService: OccurrenceService);
    submitOccurrence(createOccurrenceDto: CreateOccurrenceDto): Promise<import("../utils/@types").ApiResponse<any>>;
    search(searchDto: SearchDto): Promise<import("../utils/@types").ApiResponse<any>>;
    getAllOccurrences(): Promise<import("../utils/@types").ApiResponse<any>>;
    getOccurrencebyId(id: string): Promise<import("../utils/@types").ApiResponse<any>>;
    updateOccurrence(id: string, updateOccurrenceDto: UpdateOccurrenceDto): Promise<{
        id: string;
        status: import("@prisma/client").$Enums.OccurrenceStatus | null;
        createdAt: Date;
        updatedAt: Date;
        type: import("@prisma/client").$Enums.OccurrenceType | null;
        reporterName: string;
        reporterEmail: string;
        reporterPhone: string;
        pilotInCommandName: string | null;
        pilotInCommandEmail: string | null;
        pilotInCommandPhone: string | null;
        groundPeoplePerished: number | null;
        groundPeopleInjured: number | null;
        generalWeatherConditions: string | null;
        skyCoverage: string | null;
        meteologicalCondition: import("@prisma/client").$Enums.MeteologicalCondition | null;
        flightRules: import("@prisma/client").$Enums.FlightRules | null;
        occurrenceTime: Date | null;
        operationType: import("@prisma/client").$Enums.OperationType | null;
        flightPhase: string | null;
        latitude: string | null;
        longitude: string | null;
        occurrenceLocation: string | null;
        dangerousGoodCarriedOnBoard: string | null;
        occurrenceCategory_id: string | null;
        referenceNumber: string | null;
    }>;
    deleteOccurrence(id: string): Promise<import("../utils/@types").ApiResponse<any>>;
    generateRefNumber(id: string): Promise<import("../utils/@types").ApiResponse<{
        id: string;
        status: import("@prisma/client").$Enums.OccurrenceStatus | null;
        createdAt: Date;
        updatedAt: Date;
        type: import("@prisma/client").$Enums.OccurrenceType | null;
        reporterName: string;
        reporterEmail: string;
        reporterPhone: string;
        pilotInCommandName: string | null;
        pilotInCommandEmail: string | null;
        pilotInCommandPhone: string | null;
        groundPeoplePerished: number | null;
        groundPeopleInjured: number | null;
        generalWeatherConditions: string | null;
        skyCoverage: string | null;
        meteologicalCondition: import("@prisma/client").$Enums.MeteologicalCondition | null;
        flightRules: import("@prisma/client").$Enums.FlightRules | null;
        occurrenceTime: Date | null;
        operationType: import("@prisma/client").$Enums.OperationType | null;
        flightPhase: string | null;
        latitude: string | null;
        longitude: string | null;
        occurrenceLocation: string | null;
        dangerousGoodCarriedOnBoard: string | null;
        occurrenceCategory_id: string | null;
        referenceNumber: string | null;
    }>>;
}
