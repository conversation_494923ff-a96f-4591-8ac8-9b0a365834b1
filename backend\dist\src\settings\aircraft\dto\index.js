"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SearchAircraftDto = exports.UpdateAircraftDto = exports.CreateAircraftDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateAircraftDto {
}
exports.CreateAircraftDto = CreateAircraftDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Full model name of the aircraft' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAircraftDto.prototype, "modelFullName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Aircraft description' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAircraftDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Wake turbulence category' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAircraftDto.prototype, "wtc", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Weight group' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAircraftDto.prototype, "wtg", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Aircraft designator' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAircraftDto.prototype, "designator", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Manufacturer code' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAircraftDto.prototype, "manufacturerCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Description of aircraft type' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAircraftDto.prototype, "aircraftDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of engines' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateAircraftDto.prototype, "engineCount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Type of engine' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAircraftDto.prototype, "engineType", void 0);
class UpdateAircraftDto extends (0, swagger_1.PartialType)(CreateAircraftDto) {
}
exports.UpdateAircraftDto = UpdateAircraftDto;
class SearchAircraftDto {
}
exports.SearchAircraftDto = SearchAircraftDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Search by full model name of the aircraft' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SearchAircraftDto.prototype, "modelFullName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Search by aircraft designator' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SearchAircraftDto.prototype, "designator", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Search by manufacturer code' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SearchAircraftDto.prototype, "manufacturerCode", void 0);
//# sourceMappingURL=index.js.map