import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common';
import { CreateOccurrenceDto, UpdateOccurrenceDto } from './dto';
import { OccurrenceService } from './occurrence.service';
import { ApiTags } from '@nestjs/swagger';
import { SearchDto } from 'src/users/dto';

@ApiTags("Occurence")
@Controller('occurrence')
export class OccurrenceController {

    constructor(private occurrenceService: OccurrenceService) { }

    @Post('public/submit')
    submitOccurrence(@Body() createOccurrenceDto: CreateOccurrenceDto) {
        return this.occurrenceService.createOccurrence(createOccurrenceDto);
    }

    @Get('search')
    search(@Query() searchDto: SearchDto) {
        const { query, page, limit } = searchDto;
        return this.occurrenceService.searchOccurrence(query);
    }

    @Get('')
    async getAllOccurrences() {
        return this.occurrenceService.getOccurrences()
    }

    @Get(':id')
    async getOccurrencebyId(@Param('id') id: string) {
        return this.occurrenceService.getOccurrencesById(id)
    }

    @Put(':id')
    async updateOccurrence(
        @Param('id') id: string,
        @Body() updateOccurrenceDto: UpdateOccurrenceDto
    ) {
        return this.occurrenceService.updateOccurrence(id, updateOccurrenceDto);
    }

    @Delete(':id')
    async deleteOccurrence(@Param('id') id: string) {
        return this.occurrenceService.deleteOccurrence(id)
    }

    @Post('refnumber/:id')
    async generateRefNumber(@Param('id') id: string) {
        return this.occurrenceService.generateReferenceNumber(id)
    }
}
