import { FlightRules, OperationType, OccurrenceType, MeteologicalCondition, OccurrenceStatus } from '@prisma/client';
export declare class AircraftDto {
    model: string;
    manufacturer?: string;
    operator?: string;
    registrationMark?: string;
    operatorNationality?: string;
    intendedLandingDateTime?: Date;
    intendedLandingPoint?: string;
    lastDeparturePoint?: string;
    crewOnBoard?: number;
    crewInjured?: number;
    crewPerished?: number;
    passengersOnBoard?: number;
    passengersInjured?: number;
    passengersPerished?: number;
}
export declare class CreateOccurrenceDto {
    reporterName: string;
    reporterEmail: string;
    reporterPhone: string;
    pilotInCommandName?: string;
    pilotInCommandEmail?: string;
    pilotInCommandPhone?: string;
    involvedAircraft: AircraftDto[];
    groundPeoplePerished?: number;
    groundPeopleInjured?: number;
    generalWeatherConditions?: string;
    skyCoverage?: string;
    meteologicalCondition?: MeteologicalCondition;
    flightRules?: FlightRules;
    occurrenceTime?: Date;
    operationType?: OperationType;
    flightPhase?: string;
    latitude?: string;
    longitude?: string;
    occurrenceLocation?: string;
    dangerousGoodCarriedOnBoard?: string;
}
export declare class UpdateAircraftDto {
    id: string;
    manufacturer: string;
    model: string;
    registrationMark: string;
    operator: string;
    operatorNationality: string;
    lastDeparturePoint: string;
    intendedLandingPoint: string;
    intendedLandingDateTime: Date;
    crewOnBoard: number;
    crewInjured: number;
    crewPerished: number;
    passengersOnBoard: number;
    passengersInjured: number;
    passengersPerished: number;
}
export declare class UpdateOccurrenceDto {
    type?: OccurrenceType;
    occurrenceCategory_id?: string;
    reporterName?: string;
    reporterEmail?: string;
    reporterPhone?: string;
    pilotInCommandName?: string;
    pilotInCommandEmail?: string;
    pilotInCommandPhone?: string;
    occurrenceLocation?: string;
    latitude?: string;
    longitude?: string;
    occurrenceTime?: Date;
    operationType?: OperationType;
    flightRules?: FlightRules;
    meteologicalCondition?: MeteologicalCondition;
    flightPhase?: string;
    involvedAircraft?: UpdateAircraftDto[];
    status?: OccurrenceStatus;
}
