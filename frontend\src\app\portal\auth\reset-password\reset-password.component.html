@if(linkSent){
    <div class="flex flex-col justify-center items-center h-screen bg-gray-50 text-center">
        <div class="bg-white p-10 rounded-xl border max-w-md w-full">
          <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto mb-6 text-primary-500" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
          <h3 class="text-2xl font-bold text-gray7800 mb-4">Link Sent</h3>
          <p class="text-gray-600 leading-relaxed">
            A password reset link has been sent to your email. 
            Please check your inbox and follow the instructions to reset your password.
          </p>
        </div>
    </div>
}@else {
    <div class="flex flex-col justify-center items-center h-screen bg-gray-50">
        <div class="flex items-center flex-col gap-2 pb-12">
          <img class="w-[100px]" src="/coat-of-arm.png" alt="coat of arm" />
          <h2 class="text-2xl font-semibold text-gray-700">Reset Password</h2>
        </div>
        <div class="bg-white border rounded-lg p-12 w-full max-w-xl">
          <form [formGroup]="resetForm" (ngSubmit)="onSubmit()" class="mt-6">
            <!-- Email Field -->
            <div class="mb-4">
              <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
              <div class="relative mt-1">
                <span class="absolute inset-y-0 left-0 flex items-center pl-3">
                  <svg class="h-5 w-5 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                </span>
                <input
                  type="email"
                  id="email"
                  formControlName="email"
                  class="w-full outline-none border border-gray-300 rounded-md py-2 pl-10 pr-3 text-gray-800 focus:ring-primary focus:border-primary"
                  placeholder="Enter your email"
                />
              </div>
              <div *ngIf="resetForm.get('email')?.touched">
                <p *ngIf="resetForm.get('email')?.errors?.['required']" class="text-red-500 text-sm mt-1">
                  Email is required
                </p>
                <p *ngIf="resetForm.get('email')?.errors?.['email']" class="text-red-500 text-sm mt-1">
                  Please enter a valid email
                </p>
              </div>
            </div>
      
            <!-- Submit Button -->
            <button
              type="submit"
              [disabled]="resetForm.invalid || loading"
              class="w-full bg-primary-500 text-white font-medium py-2 px-4 rounded-md mt-6 hover:bg-primary/90 focus:ring-2 focus:ring-primary focus:ring-opacity-50 disabled:opacity-60 disabled:cursor-not-allowed"
            >
              <ng-container *ngIf="!loading; else spinner">Send reset link</ng-container>
            </button>
      
            <ng-template #spinner>
              <i class="pi pi-spin pi-spinner"></i> Please wait...
            </ng-template>
      
            <div class="flex justify-center items-center gap-2 py-2">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 12H4M4 12L10 6M4 12L10 18" stroke="#268BAF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>                
              <a routerLink="/portal/auth/login" class="text-sm text-primary-500 cursor-pointer hover:underline">
                Back to Login
              </a>
            </div>
          </form>
        </div>
        <p-toast></p-toast>
    </div>
      
}