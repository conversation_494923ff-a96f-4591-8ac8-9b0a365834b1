import { BadRequestException, Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { ApiResponse } from 'src/utils/@types';
import { CreateContactInfoDto, CreateOccurrenceCategoryDto, UpdateContactInfoDto, UpdateOccurrenceCategoryDto } from './dto';

@Injectable()
export class SettingsService {

    constructor(private prisma: PrismaService) { }

    async createContactInfo(createDto: CreateContactInfoDto): Promise<ApiResponse<any>> {
        try {
            const emailExists = await this.prisma.contactsInfo.findUnique({
                where: { email: createDto.email }
            })

            if (emailExists) {
                throw new BadRequestException("Email already exists")
            }
            const contact = await this.prisma.contactsInfo.create({ data: createDto });
            return new ApiResponse(true, 'Contact info created successfully', contact);
        } catch (error) {
            throw new InternalServerErrorException(error.message)
        }
    }

    async getAllContactInfo(): Promise<ApiResponse<any>> {
        try {
            const contacts = await this.prisma.contactsInfo.findMany({
                orderBy: {
                    id: "desc"
                }
            });
            return new ApiResponse(true, 'Contact info retrieved successfully', contacts);
        } catch (error) {
            throw new InternalServerErrorException(error.message)
        }
    }

    async getContactInfoById(id: string): Promise<ApiResponse<any>> {
        try {
            const contact = await this.prisma.contactsInfo.findUnique({ where: { id } });
            if (!contact) {
                throw new NotFoundException("Contact not found")
            }
            return new ApiResponse(true, 'Contact info retrieved successfully', contact);
        } catch (error) {
            throw new InternalServerErrorException(error.message)
        }
    }

    async updateContactInfo(id: string, updateDto: UpdateContactInfoDto): Promise<ApiResponse<any>> {
        try {
            const contact = await this.prisma.contactsInfo.update({
                where: { id },
                data: updateDto,
            });
            return new ApiResponse(true, 'Contact info updated successfully', contact);
        } catch (error) {
            throw new InternalServerErrorException(error.message)
        }
    }

    async deleteContactInfo(id: string): Promise<ApiResponse<any>> {
        try {
            const contact = await this.prisma.contactsInfo.findUnique({ where: { id } });
            if (!contact) {
                throw new NotFoundException("Contact not found")
            }
            await this.prisma.contactsInfo.delete({ where: { id } });
            return new ApiResponse(true, 'Contact info deleted successfully', null);
        } catch (error) {
            throw new InternalServerErrorException(error.message)
        }
    }

    async createOccurrenceCategory(
        createDto: CreateOccurrenceCategoryDto,
    ): Promise<ApiResponse<any>> {
        try {
            const exits = await this.prisma.occurrenceCategory.findUnique({
                where: {category: createDto.category}
            })

            if(exits) throw new BadRequestException("Category with the same name exists")
        
            const category = await this.prisma.occurrenceCategory.create({ data: createDto });
            return new ApiResponse(true, 'Occurrence category created successfully', category);
        } catch (error) {
            throw new InternalServerErrorException('Failed to create occurrence category: ' + error.message);
        }
    }

    async getAllOccurrenceCategories(): Promise<ApiResponse<any>> {
        try {
            const categories = await this.prisma.occurrenceCategory.findMany({
                orderBy: {
                    id: "desc"
                }
            });
            return new ApiResponse(true, 'Occurrence categories retrieved successfully', categories);
        } catch (error) {
            throw new InternalServerErrorException('Failed to retrieve occurrence categories: ' + error.message);
        }
    }

    async getOccurrenceCategoryById(id: string): Promise<ApiResponse<any>> {
        try {
            const category = await this.prisma.occurrenceCategory.findUnique({ where: { id } });
            if (!category) {
                throw new NotFoundException('Occurrence category not found');
            }
            return new ApiResponse(true, 'Occurrence category retrieved successfully', category);
        } catch (error) {
            throw new InternalServerErrorException('Failed to retrieve occurrence category: ' + error.message);
        }
    }

    async updateOccurrenceCategory(
        id: string,
        updateDto: UpdateOccurrenceCategoryDto,
    ): Promise<ApiResponse<any>> {
        try {
            const category = await this.prisma.occurrenceCategory.update({
                where: { id },
                data: updateDto,
            });
            return new ApiResponse(true, 'Occurrence category updated successfully', category);
        } catch (error) {
            throw new InternalServerErrorException('Failed to update occurrence category: ' + error.message);
        }
    }

    async deleteOccurrenceCategory(id: string): Promise<ApiResponse<any>> {
        try {
            const category = await this.prisma.occurrenceCategory.findUnique({ where: { id } });
            if (!category) {
                throw new NotFoundException('Occurrence category not found');
            }
            await this.prisma.occurrenceCategory.delete({ where: { id } });
            return new ApiResponse(true, 'Occurrence category deleted successfully', null);
        } catch (error) {
            throw new InternalServerErrorException('Failed to delete occurrence category: ' + error.message);
        }
    }
}
