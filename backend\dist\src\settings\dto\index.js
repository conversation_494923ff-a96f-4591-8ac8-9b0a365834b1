"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateOccurrenceCategoryDto = exports.CreateOccurrenceCategoryDto = exports.UpdateContactInfoDto = exports.CreateContactInfoDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateContactInfoDto {
}
exports.CreateContactInfoDto = CreateContactInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Name of the contact' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateContactInfoDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Description of the contact', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateContactInfoDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Email of the contact' }),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], CreateContactInfoDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Telephone of the contact' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateContactInfoDto.prototype, "telephone", void 0);
class UpdateContactInfoDto extends (0, swagger_1.PartialType)(CreateContactInfoDto) {
}
exports.UpdateContactInfoDto = UpdateContactInfoDto;
class CreateOccurrenceCategoryDto {
}
exports.CreateOccurrenceCategoryDto = CreateOccurrenceCategoryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Category name' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOccurrenceCategoryDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Category description' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOccurrenceCategoryDto.prototype, "description", void 0);
class UpdateOccurrenceCategoryDto extends (0, swagger_1.PartialType)(CreateOccurrenceCategoryDto) {
}
exports.UpdateOccurrenceCategoryDto = UpdateOccurrenceCategoryDto;
//# sourceMappingURL=index.js.map