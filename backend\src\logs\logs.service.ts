import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Logs, Prisma } from '@prisma/client';
import { ApiResponse } from 'src/utils/@types';

@Injectable()
export class LogsService {
    constructor(private prisma: PrismaService) { }

    async getLogs(
        filters?: Partial<Logs>,
        sortBy?: keyof Logs,
        page: number = 1,
        pageSize: number = 10
    ): Promise<ApiResponse<any>> {
        const skip = (page - 1) * pageSize;

        const where: Prisma.LogsWhereInput = {};

        if (filters) {
            if (filters.action) {
                where.action = { contains: filters.action };
            }
            if (filters.sourceUrl) {
                where.sourceUrl = { contains: filters.sourceUrl };
            }
            if (filters.sourceIpAddress) {
                where.sourceIpAddress = { contains: filters.sourceIpAddress };
            }
            if (filters.sourceOS) {
                where.sourceOS = { contains: filters.sourceOS };
            }
            if (filters.sourceBrowser) {
                where.sourceBrowser = { contains: filters.sourceBrowser };
            }
            if (filters.url) {
                where.url = { contains: filters.url };
            }
            if (filters.method) {
                where.method = filters.method;
            }
            if (filters.userId) {
                where.userId = filters.userId;
            }
        }

        const logs = await this.prisma.logs.findMany({
            where,
            orderBy: sortBy ? { [sortBy]: 'desc' } : { createdAt: 'desc' },
            skip,
            take: pageSize,
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true
                    }
                }
            }
        });

        const totalLogs = await this.prisma.logs.count({ where });

        return new ApiResponse(true, 'Logs retrieved successfully', {
            data: logs,
            total: totalLogs,
            page,
            pageSize,
            totalPages: Math.ceil(totalLogs / pageSize)
        });
    }

    async search(query: string, page: number = 1, pageSize: number = 10) {
        const results = await this.prisma.logs.findMany({
            where: {
                OR: [
                    { action: { contains: query, mode: 'insensitive' } },
                    { sourceUrl: { contains: query, mode: 'insensitive' } },
                    { sourceIpAddress: { contains: query, mode: 'insensitive' } },
                    { sourceOS: { contains: query, mode: 'insensitive' } },
                    { sourceBrowser: { contains: query, mode: 'insensitive' } },
                    { url: { contains: query, mode: 'insensitive' } },
                    {
                        user: {
                            name: { contains: query, mode: 'insensitive' },
                            email: { contains: query, mode: 'insensitive' },
                            telephone: { contains: query, mode: 'insensitive' }
                        }
                    }
                ]
            },
            include: {
                user: {
                    select: {
                        email: true,
                        name: true,
                        telephone: true
                    }
                }
            }
        });
        return new ApiResponse(true, 'Logs retrieved successfully', {
            data: results,
            total: results.length,
            page,
            pageSize,
            totalPages: Math.ceil(results.length / pageSize)
        });
    }

    async getUserLogs(
        userId: string,
        sortBy?: keyof Logs,
        page: number = 1,
        pageSize: number = 10
    ): Promise<ApiResponse<any>> {
        return this.getLogs({ userId }, sortBy, page, pageSize);
    }
}