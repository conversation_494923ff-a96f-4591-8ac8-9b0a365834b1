<div class="w-full h-full p-8 flex flex-col gap-4 overflow-scroll">
    <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold text-gray-700">User management</h2>
        <button pRipple (click)="openUserModal('create')"
            class="flex items-center gap-2 py-2 px-3 rounded-lg text-sm font-medium text-text-white bg-primary active:scale-95">
            <i class="pi pi-plus"></i>
            Add user
        </button>
    </div>
    <div class="pt-12">
        <div class="flex items-center gap-4">
            <div class="relative w-full">
                <i class="pi pi-search text-lg absolute top-2 left-3 text-gray-700"></i>
                <input
                    [(ngModel)]="searchQuery"
                    (input)="search()"
                    class="outline-none bg-transparent py-2 px-3 pl-10 border border-gray-300 rounded-lg  text-gray-700 w-full focus:ring-1 focus:ring-primary-500"
                    type="search" placeholder="Search Users...">
            </div>
            <!-- <button
                class="border border-gray-300 rounded-lg text-gray-700 flex items-center gap-1 p-2 px-3 active:ring ring-primary-500">
                <i class="pi pi-filter"></i>
                Sort
            </button> -->
        </div>

        <!-- table -->
        <div class="mt-8">
            <p-table [value]="users" [paginator]="true" [rows]="10" [showCurrentPageReport]="true"
                [totalRecords]="totalUsers" (onPage)="onPageChange($event)"
                currentPageReportTemplate="Showing {first} to {last} of {totalRecords} users"
                [rowsPerPageOptions]="[10, 25, 50]">
                <ng-template pTemplate="header">
                    <tr>
                        <th class="font-medium text-gray-700">Photo</th>
                        <th class="font-medium text-gray-700">Name</th>
                        <th class="font-medium text-gray-700">Email</th>
                        <th class="font-medium text-gray-700">Telephone</th>
                        <th class="font-medium text-gray-700">Role</th>
                        <th class="font-medium text-gray-700">Status</th>
                        <th class="font-medium text-gray-700">Actions</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-user>
                    <tr class="">
                        <td class="font-normal border-b">
                            <div class="w-[50px] h-[50px] rounded-lg border">
                                @if(user.profilePicture){
                                    <img class="w-full h-full object-cover" src="{{user.profilePicture}}" alt="profile picture">
                                }
                            </div>
                        </td>
                        <td class="font-normal border-b">{{ user.name }}</td>
                        <td class="font-normal border-b">{{ user.email }}</td>
                        <td class="font-normal border-b">{{ user.telephone }}</td>
                        <td class="font-normal border-b">{{ user.role }}</td>
                        <td>
                            <span class="rounded-full px-2 py-1 text-sm text-center" [ngClass]="{
                                'text-yellow-600 bg-yellow-200': user.status === 'PENDING',
                                'text-green-600 bg-green-200': user.status === 'ACTIVE',
                                'text-red-600 bg-red-200': user.status === 'INACTIVE'
                              }">
                                {{ user.status }}
                            </span>
                        </td>
                        <td class="font-normal border-b " class="flex items-center gap-2">
                            <button (click)="openUserModal('edit', user)"
                                class="bg-primary-50 text-primary-500 py-2 px-3 rounded-lg text-lg active:scale-95 active:ring ring-primary-200"
                                title="edit user">
                                <i class="pi pi-user-edit"></i>
                            </button>
                            @if(user.status === 'DEACTIVATED' && user.id !== me?.id){
                            <button (click)="confirmActivate(user)"
                                class="bg-primary-50 text-primary-500 py-2 px-3 rounded-lg text-lg active:scale-95 active:ring ring-primary-200"
                                title="edit user">
                                Activate
                            </button>
                            }@else if(user.status !== 'PENDING' && user.id !== me?.id){
                            <button (click)="confirmDeactivate(user)"
                                class="bg-red-100 text-red-400 py-2 px-3 rounded-lg text-lg active:scale-95 active:ring ring-red-400"
                                title="Deactivate user">
                                Deactivate
                            </button>
                            }

                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="5">No users found</td>
                    </tr>
                </ng-template>
            </p-table>
            <p-dialog [(visible)]="userModalVisible" [modal]="true" [header]="isEditMode ? 'Edit User' : 'Add New User'"
                [style]="{width: '450px'}">
                <ng-template pTemplate="content">
                    <div class="pb-4">
                        <p>Profile photo</p>
                        <div class="flex items-center gap-4">
                            <div class="w-[150px] h-[150px] mt-4 rounded-lg border flex items-center justify-center">
                                <input (change)="onFileSelected($event)" id="profilePicture" type="file" accept="image/*" hidden>
                                @if(profilePicturePreview){
                                    <img class="w-full h-full object-cover"  [src]="profilePicturePreview" alt="">
                                }@else if (currentUser.profilePicture){
                                    <img class="w-full h-full object-cover"  [src]="currentUser.profilePicture" alt="">
                                }
                            </div>
                            <label for="profilePicture" class="cursor-pointer p-2 bg-primary-400 text-sm rounded-lg text-white">
                                Upload
                            </label>
                        </div>
                    </div>
                    <div class="flex flex-col gap-4">
                        <div class="flex flex-col gap-2">
                            <label for="name">Name</label>
                            <input placeholder="Full name"
                                class="w-full outline-none border border-gray-300 rounded-md py-2 px-3 text-gray-800 focus:ring-1 focus:ring-primary "
                                id="name" [(ngModel)]="currentUser.name" type="text" />
                        </div>
                        <div class="flex flex-col gap-2">
                            <label for="email">Email</label>
                            <input placeholder="Email address"
                                class="w-full outline-none border border-gray-300 rounded-md py-2 px-3 text-gray-800 focus:ring-1 focus:ring-primary "
                                id="email" [(ngModel)]="currentUser.email" type="email" />
                        </div>
                        <div class="flex flex-col gap-2">
                            <label for="telephone">Telephone</label>
                            <input placeholder="Eg. +250788..."
                                class="w-full outline-none border border-gray-300 rounded-md py-2 px-3 text-gray-800 focus:ring-1 focus:ring-primary "
                                id="telephone" [(ngModel)]="currentUser.telephone" type="tel" />
                        </div>
                        <div class="flex flex-col gap-2">
                            <label for="role">Role</label>
                            <select name="role" id="role" [(ngModel)]="currentUser.role"
                                class="w-full outline-none border border-gray-300 rounded-md py-2 px-3 text-gray-800 focus:ring-1 focus:ring-primary ">
                                <option *ngFor="let role of roleOptions" [value]="role.value">
                                    {{ role.label }}
                                </option>
                            </select>
                        </div>
                    </div>
                </ng-template>
                <ng-template pTemplate="footer">
                    <button class="p-2 px-5 rounded-lg text-text-white bg-gray-600 active:scale-95"
                        (click)="userModalVisible = false">
                        Cancel
                    </button>
                    <button class="p-2 px-4 rounded-lg text-text-white bg-primary-500 ml-4 active:scale-95"
                        (click)="saveUser()">
                        {{isEditMode ? 'Update' : 'Create'}}
                    </button>
                </ng-template>
            </p-dialog>
            <p-confirmDialog></p-confirmDialog>
            <p-toast position="top-right"></p-toast>
        </div>
    </div>
</div>