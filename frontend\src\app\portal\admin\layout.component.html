<main class="flex flex-col md:flex-row w-full h-screen bg-gray-100">
    <div class="w-full md:max-w-64 md:w-64 bg-white flex flex-col fixed md:static z-50 transform md:transform-none transition-transform duration-300 ease-in-out"
        [class.translate-x-full]="!isSidebarOpen" [class.-translate-x-0]="isSidebarOpen">
        <a routerLink="/portal" class="flex items-center gap-2 py-4 pb-8 px-8 border-b">
            <img class="w-[50px] object-cover" src="/coat-of-arm.png" alt="coat of arm">
            <h2 class="font-bold text-gray-700">AAID Notification System</h2>
        </a>

        <!-- sidebar -->
        <nav class="flex flex-col h-full justify-between">
            <div class="flex flex-col gap-2 px-8 pt-4">
                <a routerLink="/admin/users" 
                   [ngClass]="{'bg-primary-50 text-primary-500 font-medium': router.isActive('/admin/users', false)}"
                   class="py-3 px-4 rounded-lg flex items-center gap-3 
                          text-gray-700 hover:bg-primary-50 hover:text-primary-500 
                          transition-colors">
                    <i class="pi pi-users text-xl"></i>
                    Users
                </a>
                <a routerLink="/admin/signature" 
                   [ngClass]="{'bg-primary-50 text-primary-500 font-medium': router.isActive('/admin/signature', false)}"
                   class="py-3 px-4 rounded-lg flex items-center gap-3 
                          text-gray-700 hover:bg-primary-50 hover:text-primary-500 
                          transition-colors">
                    <i class="pi pi-pencil text-xl"></i>
                    Signature
                </a>
                <a routerLink="/admin/contacts" 
                   [ngClass]="{'bg-primary-50 text-primary-500 font-medium': router.isActive('/admin/contacts', false)}"
                   class="py-3 px-4 rounded-lg flex items-center gap-3 
                          text-gray-700 hover:bg-primary-50 hover:text-primary-500 
                          transition-colors">
                    <i class="pi pi-globe text-xl"></i>
                    Contacts
                </a>
                <a routerLink="/admin/categories" 
                   [ngClass]="{'bg-primary-50 text-primary-500 font-medium': router.isActive('/admin/categories', false)}"
                   class="py-3 px-4 rounded-lg flex items-center gap-3 
                          text-gray-700 hover:bg-primary-50 hover:text-primary-500 
                          transition-colors">
                    <i class="pi pi-cog text-xl"></i>
                    Categories
                </a>
                <a routerLink="/admin/aircrafts" 
                   [ngClass]="{'bg-primary-50 text-primary-500 font-medium': router.isActive('/admin/aircrafts', false)}"
                   class="py-3 px-4 rounded-lg flex items-center gap-3 
                          text-gray-700 hover:bg-primary-50 hover:text-primary-500 
                          transition-colors">
                    <i class="pi pi-objects-column text-xl"></i>
                    Aircrafts
                </a>
                <a routerLink="/admin/logs" 
                   [ngClass]="{'bg-primary-50 text-primary-500 font-medium': router.isActive('/admin/logs', false)}"
                   class="py-3 px-4 rounded-lg flex items-center gap-3 
                          text-gray-700 hover:bg-primary-50 hover:text-primary-500 
                          transition-colors">
                    <i class="pi pi-file text-xl"></i>
                    Logs
                </a>
            </div>
            <div class="flex items-center gap-2 px-8 border-t py-6">
                <div
                    class="min-w-[50px] min-h-[50px] rounded-full bg-primary-500 text-white flex items-center justify-center font-medium text-lg">
                    @if(user?.profilePicture){
                        <img class="w-[50px] h-[50px] object-cover rounded-full" src="{{user?.profilePicture}}" alt="photo">
                    }@else {
                        {{user?.name?.charAt(0)}}
                    }
                </div>
                <div class="flex-grow overflow-hidden">
                    <h4 [title]="user?.name" class="text-gray-700 truncate whitespace-nowrap font-medium">{{user?.name}}
                    </h4>
                    <p class="text-sm text-gray-600 capitalize">{{user?.role?.toLowerCase()}}</p>
                </div>
                <div>
                    <p-menu #menu [model]="userMenuItems" [popup]="true"></p-menu>
                    <p-button (onClick)="menu.toggle($event)" icon="pi pi-ellipsis-v" class="p-button-text"></p-button>
                </div>
            </div>
        </nav>
    </div>

    <!-- Mobile Hamburger Button -->
    <button class="md:hidden fixed top-4 right-4 z-50 bg-primary-500 text-white p-2 rounded-md"
        (click)="toggleSidebar()">
        <i class="pi pi-bars text-xl"></i>
    </button>

    <!-- Main Content Area -->
    <div class="md:w-[80%] w-full transition-all duration-300 ease-in-out">
        <p-toast></p-toast>
        <router-outlet></router-outlet>
    </div>

    <!-- Overlay for mobile sidebar -->
    <div *ngIf="isSidebarOpen" class="md:hidden fixed inset-0 bg-black/50 z-40" (click)="toggleSidebar()">
    </div>
</main>