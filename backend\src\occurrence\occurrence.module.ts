import { Module } from '@nestjs/common';
import { OccurrenceService } from './occurrence.service';
import { OccurrenceController } from './occurrence.controller';
import { PrismaService } from 'src/prisma/prisma.service';
import { MailService } from 'src/utils/mail/mail.service';

@Module({
  providers: [OccurrenceService, PrismaService],
  controllers: [OccurrenceController]
})
export class OccurrenceModule {}
