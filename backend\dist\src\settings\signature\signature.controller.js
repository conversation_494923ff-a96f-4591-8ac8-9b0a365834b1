"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SignatureController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const signature_service_1 = require("./signature.service");
const dto_1 = require("./dto");
let SignatureController = class SignatureController {
    constructor(signatureService) {
        this.signatureService = signatureService;
    }
    async create(createSignatureDto) {
        return this.signatureService.create(createSignatureDto);
    }
    async findAll() {
        return this.signatureService.findAll();
    }
    async findOne(id) {
        return this.signatureService.findOne(id);
    }
    async update(id, updateSignatureDto) {
        return this.signatureService.update(id, updateSignatureDto);
    }
    async remove(id) {
        return this.signatureService.remove(id);
    }
};
exports.SignatureController = SignatureController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new signature' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Signature created successfully.' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateSignatureDto]),
    __metadata("design:returntype", Promise)
], SignatureController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all signatures' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of all signatures.' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SignatureController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a signature by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Signature fetched successfully.' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SignatureController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a signature by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Signature updated successfully.' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateSignatureDto]),
    __metadata("design:returntype", Promise)
], SignatureController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a signature by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Signature deleted successfully.' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SignatureController.prototype, "remove", null);
exports.SignatureController = SignatureController = __decorate([
    (0, swagger_1.ApiTags)('Signatures'),
    (0, common_1.Controller)('signatures'),
    __metadata("design:paramtypes", [signature_service_1.SignatureService])
], SignatureController);
//# sourceMappingURL=signature.controller.js.map