<div class="w-full h-full p-8 flex flex-col gap-4 overflow-scroll">
    <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold text-gray-700">Aircrafts Information</h2>
        <button pRipple (click)="openCategoriesModal('create')"
            class="flex items-center gap-2 py-2 px-3 rounded-lg text-sm font-medium text-text-white bg-primary active:scale-95">
            <i class="pi pi-plus"></i>
            Add aircraft
        </button>
    </div>
    <div class="pt-12">
        <div class="flex items-center gap-4">
            <div class="relative w-full">
                <i class="pi pi-search text-lg absolute top-2 left-3 text-gray-700"></i>
                <input [(ngModel)]="searchQuery"
                    class="outline-none bg-transparent py-2 px-3 pl-10 border border-gray-300 rounded-lg  text-gray-700 w-full focus:ring-1 focus:ring-primary-500"
                    type="search" (input)="search()" placeholder="Search aircrafts..">
            </div>
            <button
                class="border border-gray-300 rounded-lg text-gray-700 flex items-center gap-1 p-2 px-3 active:ring ring-primary-500">
                <i class="pi pi-filter"></i>
                Filter
            </button>
        </div>

        <!-- table -->
        <div class="mt-8 overflow-x-auto">
            <p-table [value]="aircrafts" [lazy]="true" [paginator]="true" [rows]="pageSize" [showCurrentPageReport]="true"
                [totalRecords]="totalAircrafts" [first]="first" (onPage)="onPageChange($event)"
                currentPageReportTemplate="Showing {first} to {last} of {totalRecords} aircrafts"
                [rowsPerPageOptions]="[10, 25, 50, 100]">
                <ng-template pTemplate="header">
                    <tr>
                        <th class="font-medium text-gray-700">Manufacturer</th>
                        <th class="font-medium text-gray-700">Model name</th>
                        <th class="font-medium text-gray-700">Description</th>
                        <th class="font-medium text-gray-700">Designator</th>
                        <th class="font-medium text-gray-700">Engine Type</th>
                        <th class="font-medium text-gray-700">Engine Count</th>
                        <th class="font-medium text-gray-700">WTC</th>
                        <th class="font-medium text-gray-700">WTG</th>
                        <th class="font-medium text-gray-700">Actions</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-aircraft>
                    <tr class="">
                        <td class="font-normal border-b">{{aircraft.manufacturerCode}}</td>
                        <td class="font-normal border-b">{{aircraft.modelFullName}}</td>
                        <td class="font-normal border-b">{{aircraft.description}}</td>
                        <td class="font-normal border-b">{{aircraft.designator}}</td>
                        <td class="font-normal border-b">{{aircraft.engineType}}</td>
                        <td class="font-normal border-b">{{aircraft.engineCount}}</td>
                        <td class="font-normal border-b">{{aircraft.wtc}}</td>
                        <td class="font-normal border-b">{{aircraft.wtg}}</td>

                        <td class="font-normal border-b " class="flex items-center gap-2">
                            <button (click)="openCategoriesModal('edit', aircraft)"
                                class="bg-primary-50 text-primary-500 py-2 px-3 rounded-lg text-lg active:scale-95 active:ring ring-primary-200"
                                title="edit user">
                                <i class="pi pi-user-edit"></i>
                            </button>
                            <button (click)="confirmDelete(aircraft)"
                                class="bg-red-100 text-red-400 py-2 px-3 rounded-lg text-lg active:scale-95 active:ring ring-red-400"
                                title="delete user">
                                <i class="pi pi-trash"></i>
                            </button>
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="5">No aircrafts</td>
                    </tr>
                </ng-template>
            </p-table>
            <p-dialog [(visible)]="aircraftsModalVisible" [modal]="true"
                [header]="isEditMode ? 'Edit Categiry' : 'Add New Category'" [style]="{width: '450px'}">
                <ng-template pTemplate="content">
                    <div class="flex flex-col gap-4">
                        <div class="flex flex-col gap-2">
                            <label for="model">Model Name *</label>
                            <input placeholder="Model Full Name"
                                class="w-full outline-none border border-gray-300 rounded-md py-2 px-3 text-gray-800 focus:ring-1 focus:ring-primary "
                                id="model" [(ngModel)]="currentAircraft.modelFullName" type="text" />
                        </div>

                        <div class="flex flex-col gap-2">
                            <label for="designator">Designator *</label>
                            <input placeholder="Designator"
                                class="w-full outline-none border border-gray-300 rounded-md py-2 px-3 text-gray-800 focus:ring-1 focus:ring-primary "
                                id="designator" [(ngModel)]="currentAircraft.designator" type="text" />
                        </div>

                        <div class="flex flex-col gap-2">
                            <label for="manufacturer">Manufacturer *</label>
                            <input placeholder="Aircraft manufacturer"
                                class="w-full outline-none border border-gray-300 rounded-md py-2 px-3 text-gray-800 focus:ring-1 focus:ring-primary "
                                id="manufacturer" [(ngModel)]="currentAircraft.manufacturerCode" type="text" />
                        </div>

                        <div class="flex flex-col gap-2">
                            <label for="description">Description *</label>
                            <input placeholder="Aircraft description"
                                class="w-full outline-none border border-gray-300 rounded-md py-2 px-3 text-gray-800 focus:ring-1 focus:ring-primary "
                                id="description" [(ngModel)]="currentAircraft.aircraftDescription" type="text" />
                        </div>

                        <div class="flex flex-col gap-2">
                            <label for="count">Engine count</label>
                            <input placeholder="Engine count (optional)"
                                class="w-full outline-none border border-gray-300 rounded-md py-2 px-3 text-gray-800 focus:ring-1 focus:ring-primary "
                                id="count" [(ngModel)]="currentAircraft.engineCount" type="number" />
                        </div>

                        <div class="flex flex-col gap-2">
                            <label for="type">Engine type</label>
                            <input placeholder="Engine type (optional)"
                                class="w-full outline-none border border-gray-300 rounded-md py-2 px-3 text-gray-800 focus:ring-1 focus:ring-primary "
                                id="type" [(ngModel)]="currentAircraft.engineType" type="text" />
                        </div>

                        <div class="flex flex-col gap-2">
                            <label for="wtc">WTC</label>
                            <input placeholder="WTC (optional)"
                                class="w-full outline-none border border-gray-300 rounded-md py-2 px-3 text-gray-800 focus:ring-1 focus:ring-primary "
                                id="wtc" [(ngModel)]="currentAircraft.wtc" type="text" />
                        </div>

                        <div class="flex flex-col gap-2">
                            <label for="wtg">WTG</label>
                            <input placeholder="WTG (optional)"
                                class="w-full outline-none border border-gray-300 rounded-md py-2 px-3 text-gray-800 focus:ring-1 focus:ring-primary "
                                id="wtg" [(ngModel)]="currentAircraft.wtg" type="text" />
                        </div>

                    </div>
                </ng-template>
                <ng-template pTemplate="footer">
                    <button class="p-2 px-5 rounded-lg text-text-white bg-gray-600 active:scale-95"
                        (click)="aircraftsModalVisible = false">
                        Cancel
                    </button>
                    <button class="p-2 px-4 rounded-lg text-text-white bg-primary-500 ml-4 active:scale-95"
                        (click)="saveAicraft()">
                        {{isEditMode ? 'Update' : 'Create'}}
                    </button>
                </ng-template>
            </p-dialog>
            <p-confirmDialog></p-confirmDialog>
            <p-toast position="top-right"></p-toast>
        </div>
    </div>
</div>