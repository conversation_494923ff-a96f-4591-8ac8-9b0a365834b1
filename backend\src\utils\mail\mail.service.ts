import { Injectable, Logger } from '@nestjs/common';
import { readFile } from 'fs';
import mailer from './mailer';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { env } from '../env';

export enum MailType {
    CREATE_ACCOUNT,
    LOGIN,
    OCCURENCE_SUBMIT,
    VERIFY_ACCOUNT,
    RESET_PASSWORD,
    ACCOUNT_VERIFIED,
    ACCOUNT_DEACTIVATED,
    PASSWORD_RESET_CONFIRMATION,
    REPORT_APPROVED,
    REPORT_REVERTED,
    APPROVAL_REQUEST
}
export interface ISendMailProps {
    name: string
    to: string
    subject: string
    values: Record<string, string>
    attachments?: {
        filename: string
        base64EncodedContent: string
    }[]
}

@Injectable()
export class MailService {
    /* LEGACY: USING NODEMAILER
    async sendMail(mailType: MailType, props: ISendMailProps) {
        try {
            const { template, from } = this.getMailTemplateAndFromEmail(mailType);
            const body = await this.constructMailBody(template, props.values);
            const res = await mailer.sendMail({
                from,
                to: props.to,
                subject: props.subject,
                html: body,
                attachments: props.attachments?.map((a) => ({
                    content: a.base64EncodedContent,
                    filename: a.filename,
                    contentType: 'base64',
                })),
            });
            if (res.accepted)
                Logger.log(
                    `Email sent to ${props.to} ${JSON.stringify(props)}`,
                    'MailingService',
                );
        } catch (error) {
            Logger.error(error, 'MailingService');
        }
    }
    */

    constructor(
        private httpService: HttpService
    ) { }

    sendMail(mailType: MailType, props: ISendMailProps): void {
        (async () => {
            try {
                const { template } = this.getMailTemplateAndFromEmail(mailType);
                const body = await this.constructMailBody(template, props.values);

                const requestData = {
                    sender_name: env.EMAIL_SENDER_NAME,
                    sender_email: env.EMAIL_SENDER,
                    receiver_name: props.name,
                    receiver_email: props.to,
                    subject: props.subject,
                    message: body,
                };

                await firstValueFrom(
                    this.httpService.post(env.EMAIL_CLIENT_URL, requestData),
                );

                Logger.log(`Email sent to ${props.to}`, 'MailService');
            } catch (error) {
                Logger.error(`Failed to send email: ${error.message}`, 'MailService');
            }
        })();
    }
    async constructMailBody(template: string, props: Record<string, string>) {
        return new Promise<string>((resolve) => {
            readFile(
                './src/utils/mail/templates/' + template + '.template.html',
                'utf-8',
                (err, data) => {
                    let body = data;
                    if (err) {
                        Logger.error(err.message, 'MailingService', 'constructMailBody');
                    }
                    for (const [key, value] of Object.entries(props)) {
                        body = body.replace(`{{${key}}}`, value);
                    }
                    return resolve(body);
                },
            );
        });
    }

    getMailTemplateAndFromEmail(mailType: MailType) {
        switch (mailType) {
            case MailType.CREATE_ACCOUNT:
                return { template: 'create-account-email' };
            case MailType.LOGIN:
                return { template: 'login-notif-email' };
            case MailType.OCCURENCE_SUBMIT:
                return { template: 'occurrence-notif' }
            case MailType.VERIFY_ACCOUNT:
                return { template: 'verify-account' }
            case MailType.ACCOUNT_VERIFIED:
                return { template: 'account-verified' }
            case MailType.ACCOUNT_DEACTIVATED:
                return { template: 'account-deactivated' }
            case MailType.RESET_PASSWORD:
                return { template: 'reset-password' }
            case MailType.PASSWORD_RESET_CONFIRMATION:
                return { template: 'password-reset-confirm' }
            case MailType.REPORT_APPROVED:
                return { template: 'approve' }
            case MailType.REPORT_REVERTED:
                return { template: 'revert' }
            case MailType.APPROVAL_REQUEST:
            return { template: 'approval' }
            default:
                return { template: 'Null' };
        }
    }
}
