import { config } from 'dotenv';
import z, { string } from 'zod';
config();

const envSchema = z.object({
  PORT: z.string(),
  DATABASE_URL: z.string(),
  JWT_SECRET: z.string(),

  SMTP_SERVER: z.string(),
  SMTP_PORT: z.string(),

  FE_URL: z.string(),
  EMAIL_SENDER: string(),
  EMAIL_CLIENT_URL: string(),
  EMAIL_SENDER_NAME: string(),
  SMS_API_URL: string() 
});

export const env = envSchema.parse({ ...process.env });
