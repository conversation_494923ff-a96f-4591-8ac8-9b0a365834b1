import { Injectable } from "@angular/core";
import { AxiosService } from "../../../util/axios/axios.service";
import { Occurrence, PaginatedResponse } from "../../../util/@types";

@Injectable({
    providedIn: 'root'
})
export class OccurrencesService {

    constructor(
        private axiosService: AxiosService
    ) { }

    async generateRefNumber(id: string){
        try {
            const response = await this.axiosService.axios.post(`/occurrence/refnumber/${id}`);
            return response.data as Occurrence
        } catch (error) {
            throw error;
        }
    }

    async getOccurrences(page: number = 1, limit: number = 10): Promise<PaginatedResponse<Occurrence>> {
        try {
            const response = await this.axiosService.axios.get('/occurrence', {
                params: { page, limit }
            });
            return response.data as PaginatedResponse<Occurrence>;
        } catch (error) {
            throw error;
        }
    }

    async getOccurrence(id: string): Promise<Occurrence> {
        try {
            const response = await this.axiosService.axios.get(`/occurrence/${id}`);
            return response.data.data as Occurrence;
        } catch (error) {
            throw error;
        }
    }

    async deleteOccurrence(id: string): Promise<Occurrence> {
        try {
            const response = await this.axiosService.axios.delete(`/occurrence/${id}`)
            return response.data.data as Occurrence
        } catch (error) {
            throw error
        }
    }

    async updateOccurrence(id: string, occurrenceData: Partial<Occurrence>): Promise<Occurrence> {
        try {
            const response = await this.axiosService.axios.put(`/occurrence/${id}`, occurrenceData);
            return response.data.data as Occurrence;
        } catch (error) {
            throw error;
        }
    }

    async search(query: string, page = 1, limit = 10  ):  Promise<PaginatedResponse<Occurrence>>{
        try {
            const response = await this.axiosService.axios.get('/occurrence/search', {
                params: { query, page, limit  }
            });
            return response.data.data as PaginatedResponse<Occurrence>;
        } catch (error) {
            throw error;
        }
    }
}