import { Body, Controller, Delete, Get, Param, Patch, Post } from '@nestjs/common';
import { DocumentsService } from './documents.service';
import { ApiTags } from '@nestjs/swagger';
import { CreateDocumentDto, UpdateDocumentDto } from './dto';

@ApiTags('Documents')
@Controller('document')
export class DocumentsController {
    constructor(
        private documentsService: DocumentsService
    ) { }

    @Post()
    create(@Body() data: CreateDocumentDto) {
        return this.documentsService.createDocument(data)
    }

    @Get()
    getAllDocuments() {
        return this.documentsService.getDocuments()
    }

    @Get(':id')
    getDocument(@Param('id') id: string) {
        return this.documentsService.getDocument(id)
    }

    @Patch(':id')
    updateDocument(@Param('id') id: string, data: UpdateDocumentDto) {
        return this.documentsService.updateDocument(id, data)
    }

    @Delete(':id')
    deleteDocument(@Param('id') id: string){
        return this.documentsService.remove(id)
    }
}
