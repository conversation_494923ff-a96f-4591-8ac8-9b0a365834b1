"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const bcrypt = require("bcrypt");
const mail_service_1 = require("../utils/mail/mail.service");
const env_1 = require("../utils/env");
const _types_1 = require("../utils/@types");
const sms_service_1 = require("../utils/sms/sms.service");
const client_1 = require("@prisma/client");
const auth_service_1 = require("../auth/auth.service");
const speakeasy = require("speakeasy");
const qrcode = require("qrcode");
let UsersService = class UsersService {
    constructor(prisma, mailService, smsService, authService) {
        this.prisma = prisma;
        this.mailService = mailService;
        this.smsService = smsService;
        this.authService = authService;
    }
    async createUser(createUserDto) {
        const existingUser = await this.prisma.user.findFirst({
            where: {
                OR: [
                    { email: createUserDto.email },
                    { telephone: createUserDto.telephone }
                ]
            }
        });
        if (existingUser) {
            throw new common_1.BadRequestException('User with this email or phone number already exists');
        }
        const passwordExpiryDate = new Date();
        passwordExpiryDate.setDate(passwordExpiryDate.getDate() + 90);
        const user = await this.prisma.user.create({
            data: {
                ...createUserDto,
                passwordExpiryDate,
                status: client_1.AccountStatus.PENDING,
                multiFactorEnabled: false,
            }
        });
        await this.authService.sendVerificationCode(user, 'ACTIVATION');
        delete user.password;
        delete user.resetToken;
        return new _types_1.ApiResponse(true, 'User created. Activation code sent via email and SMS.', { userId: user.id, email: user.email, telephone: user.telephone });
    }
    async updateUser(userId, updateUserDto) {
        const existingUser = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (!existingUser) {
            throw new common_1.NotFoundException('User not found');
        }
        if (updateUserDto.email) {
            const emailConflict = await this.prisma.user.findFirst({
                where: {
                    email: updateUserDto.email,
                    NOT: { id: userId }
                }
            });
            if (emailConflict) {
                throw new common_1.BadRequestException('Email is already in use');
            }
        }
        if (updateUserDto.telephone) {
            const phoneConflict = await this.prisma.user.findFirst({
                where: {
                    telephone: updateUserDto.telephone,
                    NOT: { id: userId }
                }
            });
            if (phoneConflict) {
                throw new common_1.BadRequestException('Phone number is already in use');
            }
        }
        const updatedUser = await this.prisma.user.update({
            where: { id: userId },
            data: updateUserDto
        });
        delete updatedUser.password;
        delete updatedUser.resetToken;
        return new _types_1.ApiResponse(true, 'User updated successfully', updatedUser);
    }
    async activateUserAccount(userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        if (!user.password) {
            throw new common_1.NotAcceptableException('The user must set their password first');
        }
        const activatedUser = await this.prisma.user.update({
            where: { id: userId },
            data: {
                status: client_1.AccountStatus.ACTIVE,
                multiFactorEnabled: true
            }
        });
        this.mailService.sendMail(mail_service_1.MailType.ACCOUNT_VERIFIED, {
            to: user.email,
            name: user.name,
            subject: 'Account activated | AAID Notification',
            values: {
                name: user.name
            }
        });
        await this.smsService.sendSMS(user.telephone, `Hello ${user.name},\nYour account has been activated by an administrator.\nAAID Team.`);
        return new _types_1.ApiResponse(true, 'User account activated successfully', null);
    }
    async activateAccount(resetPasswordConfirmDto) {
        const user = await this.prisma.user.findFirst({
            where: {
                resetToken: resetPasswordConfirmDto.token,
                resetTokenExpiry: { gt: new Date() }
            }
        });
        if (!user) {
            throw new common_1.BadRequestException('Invalid or expired reset token');
        }
        const hashedPassword = await bcrypt.hash(resetPasswordConfirmDto.password, 10);
        await this.prisma.user.update({
            where: { id: user.id },
            data: {
                password: hashedPassword,
                resetToken: null,
                resetTokenExpiry: null,
                status: client_1.AccountStatus.ACTIVE,
                multiFactorEnabled: true,
                passwordExpiryDate: (() => {
                    const newExpiry = new Date();
                    newExpiry.setDate(newExpiry.getDate() + 90);
                    return newExpiry;
                })()
            }
        });
        return this.generateTwoFactorSecret(user.id);
    }
    async deactivateUserAccount(userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const deactivatedUser = await this.prisma.user.update({
            where: { id: userId },
            data: {
                status: client_1.AccountStatus.DEACTIVATED,
                multiFactorEnabled: false
            }
        });
        this.mailService.sendMail(mail_service_1.MailType.ACCOUNT_DEACTIVATED, {
            to: user.email,
            name: user.name,
            subject: 'Account Deactivated | AAID Notification',
            values: {
                name: user.name
            }
        });
        await this.smsService.sendSMS(user.telephone, `Hello ${user.name},\nYour account has been deactivated by an administrator.\nAAID Team.`);
        return new _types_1.ApiResponse(true, 'User account deactivated successfully', null);
    }
    async initiatePasswordReset(resetPasswordDto) {
        const user = await this.prisma.user.findUnique({
            where: { email: resetPasswordDto.email }
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        await this.authService.sendVerificationCode(user, 'PASSWORD_RESET');
        return new _types_1.ApiResponse(true, 'Password reset code sent via email and SMS', { userId: user.id });
    }
    async confirmPasswordReset(resetPasswordConfirmDto) {
        const user = await this.prisma.user.findFirst({
            where: {
                resetToken: resetPasswordConfirmDto.token,
                resetTokenExpiry: { gt: new Date() }
            }
        });
        if (!user) {
            throw new common_1.BadRequestException('Invalid or expired reset token');
        }
        const hashedPassword = await bcrypt.hash(resetPasswordConfirmDto.newPassword, 10);
        await this.prisma.user.update({
            where: { id: user.id },
            data: {
                password: hashedPassword,
                resetToken: null,
                resetTokenExpiry: null,
                passwordExpiryDate: (() => {
                    const newExpiry = new Date();
                    newExpiry.setDate(newExpiry.getDate() + 90);
                    return newExpiry;
                })()
            }
        });
        this.mailService.sendMail(mail_service_1.MailType.PASSWORD_RESET_CONFIRMATION, {
            to: user.email,
            name: user.name,
            subject: 'Password Reset | AAID Notification',
            values: {
                name: user.name
            }
        });
        await this.smsService.sendSMS(user.telephone, `Hello ${user.name},\nYour password has been successfully reset.\nAAID Team.`);
        return new _types_1.ApiResponse(true, 'Password reset successful', null);
    }
    async generateTwoFactorSecret(userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const secret = speakeasy.generateSecret({
            name: `AAID Portal: ${user.email}`
        });
        await this.prisma.user.update({
            where: { id: userId },
            data: {
                multiFactorSecret: secret.base32,
                multiFactorEnabled: true
            }
        });
        const qrCode = await qrcode.toDataURL(secret.otpauth_url);
        this.mailService.sendMail(mail_service_1.MailType.ACCOUNT_VERIFIED, {
            to: user.email,
            name: user.name,
            subject: 'Account activated | AAID Notification',
            values: {
                name: user.name,
                link: `${env_1.env.FE_URL}/portal/login`
            }
        });
        await this.smsService.sendSMS(user.telephone, `Hello ${user.name},\nYour account has been successfully activated.\nAAID Team.`);
        return new _types_1.ApiResponse(true, '2FA secret generated', {
            qrCode,
            secret: secret.base32
        });
    }
    async verifyTwoFactorSetup(userId, token) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        if (!user.multiFactorSecret) {
            throw new common_1.BadRequestException('2FA not set up');
        }
        const isValid = speakeasy.totp.verify({
            secret: user.multiFactorSecret,
            encoding: 'base32',
            token: token
        });
        if (!isValid) {
            throw new common_1.NotAcceptableException('Invalid 2FA token');
        }
        return new _types_1.ApiResponse(true, '2FA setup verified', null);
    }
    async disableTwoFactor(userId) {
        await this.prisma.user.update({
            where: { id: userId },
            data: {
                multiFactorSecret: null,
                multiFactorEnabled: false
            }
        });
        return new _types_1.ApiResponse(true, '2FA disabled', null);
    }
    async getAllUsers(filters, sortBy, page = 1, pageSize = 10) {
        const skip = (page - 1) * pageSize;
        const users = await this.prisma.user.findMany({
            where: filters,
            orderBy: sortBy ? { [sortBy]: 'desc' } : { createdAt: 'desc' },
            skip,
            take: pageSize,
            select: {
                id: true,
                name: true,
                email: true,
                telephone: true,
                role: true,
                status: true,
                profilePicture: true,
                createdAt: true
            }
        });
        const totalUsers = await this.prisma.user.count({ where: filters });
        return new _types_1.ApiResponse(true, 'Users retrieved successfully', {
            data: users,
            total: totalUsers,
            page
        });
    }
    async searchUsers(query, page, limit = 10) {
        console.log("searching");
        const results = await this.prisma.user.findMany({
            where: {
                OR: [
                    { name: { contains: query, mode: 'insensitive' } },
                    { email: { contains: query, mode: 'insensitive' } },
                    { telephone: { contains: query, mode: 'insensitive' } },
                ]
            },
        });
        const total = await this.prisma.user.count({
            where: {
                OR: [
                    { name: { contains: query, mode: 'insensitive' } },
                    { email: { contains: query, mode: 'insensitive' } },
                    { telephone: { contains: query, mode: 'insensitive' } },
                ]
            },
        });
        return new _types_1.ApiResponse(true, 'Search results retrieved successfully', {
            data: results,
            total,
            page
        });
    }
    async getUser(id) {
        const user = await this.prisma.user.findUnique({
            where: {
                id
            }
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        delete user.password;
        delete user.resetToken;
        return new _types_1.ApiResponse(true, "", user);
    }
    async getInvestigators() {
        const investigators = await this.prisma.user.findMany({
            where: {
                role: client_1.Role.INVESTIGATOR
            },
            select: {
                id: true,
                name: true,
                email: true,
                telephone: true,
                role: true,
                createdAt: true,
                updatedAt: true
            }
        });
        return new _types_1.ApiResponse(true, '', investigators);
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        mail_service_1.MailService,
        sms_service_1.SmsService,
        auth_service_1.AuthenticationService])
], UsersService);
//# sourceMappingURL=users.service.js.map