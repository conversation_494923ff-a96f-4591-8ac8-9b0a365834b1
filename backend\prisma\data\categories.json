[{"id": 4027251, "valueListId": 16578, "identifier": 24, "parent": 0, "level": 1, "description": "ADRM: Aerodrome", "detailed": "Occurrences involving aerodrome design, service, or functionality issues.", "explanation": "Usage Notes: Occurrences do not necessarily involve an aircraft. \r\n\r\nIncludes: \r\n- Deficiencies/issues associated with State-approved Aerodromes and Heliports, including: \r\no Runways and Taxiways \r\no Buildings and structures \r\no Crash/Fire/Rescue (CFR) services \r\no Obstacles on the Aerodrome property \r\no Lighting, markings, and signage \r\no Procedures, policies, and standards \r\n- Deficiencies with snow, frost, or ice removal from aerodrome surfaces \r\n- Closed runways, improperly marked runways, construction interference, lighting failures, signage limitations, etc. \r\n-Effects of Aerodrome Design (See crossovers below) \r\n- Loose foreign objects on aerodromes and heliports (See exceptions below) \r\n- Failures of glider winch launch equipment (See crossovers below) \r\n\r\nDoes NOT include: \r\n- Deficiencies or loose foreign objects at unprepared or natural landing sites, which are coded as OTHR. \r\n- Occurrences related to snow, frost, or ice removal from aircraft, which are coded as RAMP. \r\n\r\nCrossover to/from other occurrence categories: \r\n- For effects of aerodrome design, code both ADRM and the phenomenon encountered. For example, building layout and architecture leading to surface wind disruptions would be coded as both ADRM and WSTRW or TURB, as appropriate. \r\n- If a glider winch launch equipment failure causes an event meeting the criteria for the GTOW category, code both ADRM and GTOW. \r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "24", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027252, "valueListId": 16578, "identifier": 1, "parent": 0, "level": 1, "description": "AMAN: Ab<PERSON>t maneuvre", "detailed": "The intentional abrupt maneuvering of the aircraft by the flight crew.", "explanation": "Usage Notes:\r\n• This category includes the intentional maneuvering of the aircraft to avoid a collision with terrain, objects/obstacles, weather or other aircraft (Note: The effect of intentional maneuvering is the key consideration).\r\n• Abrupt maneuvering may also result in a loss of control or system/component failure or malfunction. In this case, the event is coded under both categories (e.g., AMAN and Loss of Control–Inflight (LOC–I), AMAN and System/Component Failure or Malfunction (Non- Powerplant) (SCF–NP), or AMAN and System/Component Failure or Malfunction\r\n(Powerplant) (SCF–PP)).\r\n• Abrupt maneuvering may also occur on ground; examples include hard braking maneuver, rapid change of direction to avoid collisions, etc.\r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "1", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027253, "valueListId": 16578, "identifier": 2, "parent": 0, "level": 1, "description": "ARC: Abnormal runway contact", "detailed": "Any landing or takeoff involving abnormal runway or landing surface contact.", "explanation": "ABNORMAL RUNWAY CONTACT (ARC)\r\nAny landing or takeoff involving abnormal runway or landing surface contact.\r\nUsage Notes:\r\n• Events such as hard/heavy landings, long/fast landings, off center landings, crabbed landings, nose wheel first touchdown, tail strikes, and wingtip/nacelle strikes are included in this category.\r\n• Gear-up landings are also recorded here. However, if a system/component failure or malfunction occurred, which led to the gear up landing, the event is also coded under the appropriate system/component failure or malfunction category.\r\n• Do not use this category for runway contacts after losing control, e.g., runway contact after takeoff.\r\n• Occurrences in which the gear collapses during the takeoff run or the landing roll are not included here except if a condition in the usage notes above has been met.\r\nNOTE: Throughout this document the term runway or landing area is taken in its broadest sense and includes runways, landing strips, waterways, unimproved landing areas, and landing pads (which may include offshore platforms, building roofs, roads, ships, and fields), or other landing areas.\r\nNOTE: Does not include helicopter hard/heavy landings after an off-field emergency\r\nautorotation when there was no intention to land before the autorotation was entered.\r\nNOTE: Includes (tail) rotor striking the intended landing surface during takeoff and landing. However, collisions with obstacles during takeoff and landing, such as trees or walls, should be coded under Collision With Obstacle(s) During Takeoff and Landing (CTOL).\r\nNOTE: Does not include off-field landing by gliders.\r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "2", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027254, "valueListId": 16578, "identifier": 25, "parent": 0, "level": 1, "description": "ATM: ATM/CNS", "detailed": "Occurrences involving Air traffic management (ATM) or communications, navigation, or surveillance (CNS) service issues.", "explanation": "Usage Notes:\r\n• Includes ATC facility/personnel failure/degradation, CNS service failure/degradation, procedures, policies, and standards\r\n• Examples include, NAVAID outage, NAVAID service error, controller error, Supervisor error, ATC computer failure, Radar failure, and navigation satellite failure\r\n• Occurrences do not necessarily involve an aircraft.\r\n\r\nNOTE: ATM includes all of the facilities, equipment, personnel, and procedures involved in the provision of state-approved Air Traffic Services.\r\n\r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "25", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027255, "valueListId": 16578, "identifier": 29, "parent": 0, "level": 1, "description": "BIRD: Birdstrike", "detailed": "Occurrences involving collisions / near collisions with birds", "explanation": "A collision / near collision with or ingestion of one or several birds.\r\n\r\nUsage Notes:\r\n• May occur in any phase of flight\r\n\r\nNOTE: Bird strikes were previously categorized as 'other'. Users may wish to update their historic data by replacing 'other' with 'BIRD' where the occurrence involved a bird strike.", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "29", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027256, "valueListId": 16578, "identifier": 26, "parent": 0, "level": 1, "description": "CABIN: Cabin safety events", "detailed": "Miscellaneous occurrences in the passenger cabin of transport category aircraft", "explanation": "Usage Notes: \r\n\r\nIncludes: \r\n- Events related to carry-on baggage, supplemental oxygen, or missing/non-operational cabin emergency equipment. \r\n- Inadvertent deployment of emergency equipment. \r\n- Injuries of persons while in the passenger cabin of an aircraft (see below for exceptions). \r\n\r\nDoes NOT include: \r\n- Injuries sustained as a result of— \r\no Thunderstorms and/or wind shear, which are coded as WSTRW; \r\no Turbulence (excluding turbulence caused by wind shear and/or thunderstorms), which is coded as TURB; \r\no Intentional acts (suicide, homicide, acts of violence, self-inflicted injury, or laser attacks), which are coded as SEC; \r\no Icing events, which are coded as ICE. \r\n-Illnesses or non-injury medical emergencies, which are coded as Medical (MED). \r\n\r\nCrossover to/from other occurrence categories: \r\n- Medical emergencies involving persons other than crew members or a medical evacuation patient were coded as CABIN before October 2013. All medical emergencies are now coded as MED. \r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "26", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027257, "valueListId": 16578, "identifier": 3, "parent": 0, "level": 1, "description": "CFIT: Controlled flight into or toward terrain", "detailed": "Inflight collision or near collision with terrain, water, or obstacle without indication of loss of control", "explanation": "Usage Notes:\r\n• Use only for occurrences during airborne phases of flight.\r\n• Includes collisions with those objects extending above the surface (for example, towers, trees, power lines, cable car support, transport wires, power cables, telephone lines and aerial masts).\r\n• Can occur during either Instrument Meteorological Conditions (IMC) or Visual Meteorological Conditions (VMC).\r\n• Includes instances when the cockpit crew is affected by visual illusions or degraded visual environment (e.g., black hole approaches and helicopter operations in brownout or whiteout conditions) that result in the aircraft being flown under control into terrain, water, or obstacles.\r\n• If control of the aircraft is lost (induced by crew, weather or equipment failure), do not use this category, use Loss of Control–Inflight (LOC–I) instead.\r\n• For an occurrence involving intentional low altitude operations (e.g., crop dusting, aerial work operations close to obstacles, and Search and Rescue (SAR) operations close to water or ground surface) use the Low Altitude Operations (LALT) code instead of CFIT.\r\n• Do not use this category for occurrences involving intentional flight into/toward terrain in manned aircraft or intentional ground impact of unmanned aircraft. Code all collisions with obstacles during takeoff and landing under Collision With Obstacle(s) During Takeoff and Landing (CTOL). Code all suicides under Security Related (SEC) events. Code system, equipment, or command and control failures involving unmanned aircraft under System/Component Failure or Malfunction (Non-Powerplant) (SCF–NP) or LOC–I as applicable.\r\n• Do not use this category for occurrences involving runway undershoot/overshoot, which are classified as Undershoot/Overshoot (USOS).\r\n• Includes flying into terrain during transition into forward flight.\r\n• For helicopter operations, not to be used for takeoff and landing phases, except when the occurrence involves flying into terrain without indication of loss of control during transition into forward flight.\r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "3", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027258, "valueListId": 16578, "identifier": 102, "parent": 0, "level": 1, "description": "CTOL: Collision with obstacle(s) during take-off and landing", "detailed": "Collision with obstacle(s), during take-off or landing whilst airborne.", "explanation": "Usage Notes:\r\n• For all aircraft (excluding rotorcraft), to be used only in cases where the crew was aware of the true location of the obstacle, but its clearance from the aircraft flightpath was inadequate.\r\n• Includes contact with obstacles, such as vegetation, trees and walls, snow drifts, power cables, telegraph wires and antennae, offshore platforms, maritime vessels and structures, land structures and buildings.\r\n• Includes collisions during take-off to and landing from the hover.\r\n• Includes water obstacles during take-off from water (e.g. waves, dead-heads, ships, swimmers). \r\n• Not to be used for occurrences classified under Controlled Flight Into or Toward Terrain (CFIT), Loss of Control–Inflight (LOC–I) or System/Component Failure or Malfunction (Powerplant)(SCF–PP).", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "102", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027259, "valueListId": 16578, "identifier": 4, "parent": 0, "level": 1, "description": "EVAC: Evacuation", "detailed": "Occurrence where either; (a) person(s) are injured during an evacuation; (b) an unnecessary evacuation was performed; (c) evacuation equipment failed to perform as required; or (d) the evacuation contributed to the severity of the occurrence.", "explanation": "Usage Notes:\r\n• Includes cases where an injury(ies) was(were) sustained during the evacuation through an emergency exit or main cabin door.\r\n• Includes cases where the evacuation itself is the accident (in essence, had there not been an evacuation there would not have been an accident).\r\n• An unnecessary evacuation is one that was either erroneously commanded by the crew or uncommanded.\r\n• Only used for passenger carrying operations involving transport category aircraft.\r\n• Includes evacuation following a ditching or survivable crash landing in water provided one of the conditions above are met.", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "4", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027260, "valueListId": 16578, "identifier": 101, "parent": 0, "level": 1, "description": "EXTL: External load related occurrences", "detailed": "Occurrences during or as a result of external load or external cargo operations.", "explanation": "Usage Notes:\r\n• Includes cases where external load or the load lifting equipment used (e.g. long line, cable) contacts terrain, water surface or objects.\r\n• Includes cases where the load or, in the absence of a load, the load lifting equipment strikes or becomes entangled with the main rotor, tail rotor, or the helicopter fuselage.\r\n• Includes injuries to ground crew handling external loads as result of contact with/dropping/inadvertent release of external load.\r\n• Includes ground injuries to ground crew handling external loads due to the downwash effect or falling branch, trees etc.\r\n• Includes external hoist, human external cargo, long lines.\r\n• If the preparation of the external load by ground crew played a role, also code under RAMP.\r\n• Failures or malfunctions of the onboard external load handling lifting equipment or release systems should be coded under SCF-NP, as these are considered to be aircraft systems.", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "101", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027261, "valueListId": 16578, "identifier": 5, "parent": 0, "level": 1, "description": "F-NI: Fire/smoke (non-impact)", "detailed": "Fire or smoke in or on the aircraft, in flight or on the ground, which is not the result of impact.", "explanation": "Usage Notes:\r\n• Includes fire due to a combustive explosion from an accidental ignition source.\r\n• Includes fire and smoke from system/component failures/malfunctions in the cockpit, passenger cabin, or cargo area.\r\n• Non-combustive explosions such as tire burst and pressure bulkhead failures are coded under System/Component Failure - Non-Powerplant (SCF-NP).\r\n• Fire/Smoke resulting from an accident impact is coded under Fire/Smoke (post-impact) (F-POST).\t\r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "5", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027262, "valueListId": 16578, "identifier": 6, "parent": 0, "level": 1, "description": "F-POST: Fire/smoke (post-impact)", "detailed": "Fire/Smoke resulting from impact.", "explanation": "Usage Notes:\r\n• This category is only used for occurrences where post impact fire was a factor in the outcome.\r\n• This category is only used in conjunction with another category. For example: a system/component failure that also results in a post-impact fire will be coded as SCF-PP and F-POST or SCF-NP and F-POST.\r\n\r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "6", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027263, "valueListId": 16578, "identifier": 7, "parent": 0, "level": 1, "description": "FUEL: Fuel related", "detailed": "One or more powerplants experienced reduced or no power output due to fuel exhaustion, fuel starvation/mismanagement, fuel contamination/wrong fuel, or carburetor and/or induction icing.", "explanation": "Usage Notes:\r\n• The following fuel related definitions are provided for clarity:\r\n- Exhaustion: No usable fuel remains on the aircraft.\r\n- Starvation/mismanagement: Usable fuel remains on the aircraft, but it is not available to the engines.\r\n- Contamination: Any foreign substance (for example: water, oil, ice, dirt, sand, bugs) in the correct type of fuel for the given powerplant(s).\r\n- Wrong fuel: Fuel supplied to the powerplant(s) is incorrect, for example: Jet A into a piston powerplant, 80 octane into a powerplant requiring 100 octane.\r\n\r\n• Includes cockpit crew or ground crew-induced fuel-related problems that are not the result of mechanical failures. Interruptions of the fuel supply caused by mechanical failures are coded elsewhere as non-powerplant or powerplant system/component failures (SCF-NP or SCF-PP), as appropriate.\r\n\r\n• Also used when the wrong fuel causes a powerplant failure (e.g., through detonation). In this case it should be coded as FUEL, not as a system/component failure or malfunction- powerplant (SCF-PP).\r\n\r\n• Includes cases where there was a high risk of fuel exhaustion but there was no actual loss of power.", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "7", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027264, "valueListId": 16578, "identifier": 9, "parent": 0, "level": 1, "description": "GCOL: Ground Collision", "detailed": "Collision while taxiing to or from a runway in use.", "explanation": "Usage Notes:\r\n• Includes collisions with an aircraft, person, ground vehicle, obstacle, building, structure, etc. while on a surface other than the runway used for landing or intended for takeoff.\r\n• Ground collisions resulting from events categorized under Runway Incursion (RI), Wildlife (WILD) or Ground Handling (RAMP) are excluded from this category.\r\n\r\nNOTE: Taxiing includes ground and air taxiing for rotorcraft on designated taxiways.\r\n\r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "9", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027265, "valueListId": 16578, "identifier": 104, "parent": 0, "level": 1, "description": "GTOW: Glider towing related events", "detailed": "Premature release, inadvertent release or non-release during towing, entangling with towing, cable, loss of control, or impact into towing aircraft / winch.", "explanation": "Usage Notes:\r\n• Applicable both to aircraft under tow by winch or by another aircraft or to aircraft executing towing.\r\n• To be used in events only after reaching airborne phase.\r\n• Includes loss of control because of entering the towing aircraft's wake turbulence and events where of airspeed is out of limits during tow.", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "104", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027266, "valueListId": 16578, "identifier": 10, "parent": 0, "level": 1, "description": "ICE: Icing", "detailed": "Accumulation of snow, ice, freezing rain, or frost on aircraft surfaces that adversely affects aircraft control or performance.", "explanation": "Usage Notes:\r\n• Includes accumulations that occur inflight or on the ground (i.e., deicing-related).\r\n• Carburetor and induction icing events are coded in the FUEL Related (FUEL) category.\r\n• Windscreen icing which restricts visibility is also covered here.\r\n• Includes ice accumulation on sensors, antennae, and other external surfaces.\r\n• Includes ice accumulation on external surfaces including those directly in front of the engine intakes.\r\n\r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "10", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027267, "valueListId": 16578, "identifier": 11, "parent": 0, "level": 1, "description": "LALT: Low altitude operations", "detailed": "Collision or near collision with obstacles/objects/terrain while intentionally operating near the surface (excludes takeoff or landing phases).", "explanation": "Usage Notes:\r\n• ‘Terrain’ includes: water, vegetation, rocks, and other natural elements laying, on or growing out of, the earth.\r\n• Includes ostentatious display, manoeuvring at low height, aerobatics, sight seeing, demonstration flights, aerial inspection, avalanche mining, human hoist or human cargo sling, search and rescue operations, aerial application, intentional helicopter operations close to obstacles during aerial work and scud running (ducking under low visibility conditions).\r\n• Also includes intentional manoeuvring in close proximity to cliffs, mountains, into box canyons, and similar flights where the aircraft aerodynamic capability is not sufficient to avoid impact.\r\n• If there is a loss of control during low altitude operations, both loss of control – inflight (LOC-I) and LALT are coded.\r\n• NOTE: excluding rotorcraft air taxi phase of flight.\r\n• NOTE: includes maneuvering at low height while searching for an off-aerodrome landing location.\t\r\n\r\nDo not use LALT in conjunction with CFIT.\r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "11", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027268, "valueListId": 16578, "identifier": 12, "parent": 0, "level": 1, "description": "LOC-G: Loss of control - ground", "detailed": "Loss of aircraft control while the aircraft is on the ground", "explanation": "Usage Notes:\r\n• Used only for non-airborne phases of flight, i.e., ground/surface operations.\r\n• The loss of control may result from a contaminated runway or taxiway (e.g., rain, snow, ice, slush).\r\n• The loss of control during ground operations can occur as the result of other occurrence categories as well. For example, LOC-G may result from a system/component failure or malfunction to the powerplant (SCF-PP) or non-powerplant (SCF-NP), or from evasive action taken during a Runway Incursion (RI-VAP) or Wildlife (WILD) encounter. For these occurrences, the event is coded under both categories (e.g., LOC-G and SCF-PP, LOC-G and SCF-NP, or LOC-G and RI-VAP or LOC-G and WILD).\r\n• Do not use when a mechanical failure rendered the aircraft uncontrollable.\r\n• Rotorcraft (losses of control) during sloping ground or moving helideck operations, dynamic rollover and ground resonance events are also included here.\r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "12", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027269, "valueListId": 16578, "identifier": 13, "parent": 0, "level": 1, "description": "LOC-I: Loss of control - inflight", "detailed": "Loss of aircraft control while or deviation from intended flightpath inflight .", "explanation": "Loss of control inflight is an extreme manifestation of a deviation from intended flightpath. The phrase “loss of control” may cover only some of the cases during which an unintended deviation occurred.\r\n\r\nCommunity comments:  It is  suggested that the occurrence title is reviewed and changed accordingly. For example: Deviation from Intended Flightpath- DEV.\r\n\r\nUsage Notes:\r\n• Used only for airborne phases of flight in which aircraft control was lost.\r\n• Loss of control can occur during either Instrument Meteorological Conditions (IMC) or Visual Meteorological Conditions (VMC).\r\n• The loss of control during flight may occur as a result of a deliberate maneuver (e.g., stall/spin practice).\r\n• Occurrences involving configuring the aircraft (e.g., flaps, slats, onboard systems, etc.) are included as well as rotorcraft retreating blade stall.\r\n• Stalls are considered loss of control and are included here.\r\n• Rotorcraft occurrences which involve power settling (vortex ring), or settling with power to ground contact are coded here and as Abnormal Runway Contact (ARC) if during normal landing or takeoff.\r\n• Rotorcraft External Load operations involving loss of control related to the external load should be coded as LOC–I as well as External Load Related Occurrences (EXTL).\r\n• Includes Rotorcraft “Loss of Tail Rotor Effectiveness.”\r\n• Includes loss of control during practice or emergency autorotation.\r\n• Includes pilot-induced or assisted oscillations.\r\n• For unmanned aircraft events, includes hazardous outcomes involving deviation from intended flightpath associated with anticipated or unanticipated loss of datalink. However, if loss of datalink is the direct result of a system/component failure or malfunction, code the occurrence as System/Component Failure or Malfunction (Non-Powerplant) (SCF–NP) only. \r\n• For icing-related events, which are also loss of control, code both LOC–I and Icing (ICE)).\r\n• If the loss of control is a direct result of a system/component failure or malfunction (SCF), code the occurrence as an System/Component Failure or Malfunction (Non-Powerplant) (SCF–NP), or System/Component Failure or Malfunction (Powerplant) (SCF–PP) only. However, loss of control may follow less severe system/component failures, and in this case, code both categories.\r\n• Cockpit crew vision-related events and flight in degraded visual environments (for example, obscuration, black hole approach events, brownouts, or whiteout events), in which the aircraft is flown under control into terrain, water, or obstacles, are coded under Controlled Flight Into or Toward Terrain (CFIT), not LOC–I.\r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "13", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027270, "valueListId": 16578, "identifier": 103, "parent": 0, "level": 1, "description": "LOLI: Loss of lifting conditions en-route", "detailed": "Landing en-route due to loss of lifting conditions.", "explanation": "Usage Notes:\r\n• Applicable only to aircraft that rely on static lift to maintain or increase flight altitude, namely sailplanes, gliders, hang gliders and paragliders, balloons and airships.\r\n• All static lift forms to be considered, including atmospheric lift, namely from Orographic, Thermal, Mountain Wave and Convergence Zone, and buoyancy lift namely from lighter than air gas or hot air.\r\n• Also include motorglider and paramotor aircraft if operating under static atmospheric lift conditions and the engine could not be started.\r\n• If the aircraft was flying intentionally at low height above the terrain, use LALT instead (typical cases occur with gliders in competition flying).", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "103", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027271, "valueListId": 16578, "identifier": 14, "parent": 0, "level": 1, "description": "MAC: Airprox/ACAS alert/loss of separation/(near) midair collisions", "detailed": "Airprox, ACAS alerts, loss of separation as well as near collisions or collisions between aircraft in flight.", "explanation": "Usage Notes: \n\nIncludes: \n- All collisions between aircraft while both aircraft are airborne. \n- Separation-related occurrences caused by either air traffic control or cockpit crew. \n- AIRPROX reports \n- Genuine TCAS/ACAS alerts. \n\nDoes NOT include: \n- False TCAS/ACAS alerts caused by equipment malfunctions, which are coded as SCF-NP. \n- Loss of separation with at least one aircraft on the ground, which may be coded as ATM, GCOL, NAV, and/or RI if the occurrence meets the criteria and usage notes for those categories. \n\nCrossover to/from other occurrence categories: \n- Code both MAC and NAV if the event was caused by a navigation error and the event meets the usage notes of both categories. \n- Code both MAC and ATM if the event was caused by an ATC/ATM error and the event meets the usage notes of both categories. \n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "14", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027272, "valueListId": 16578, "identifier": 105, "parent": 0, "level": 1, "description": "MED: Medical", "detailed": "Medical – occurrences involving illness of persons on board the aircraft", "explanation": "Usage notes:  \r\n\r\nIncludes: \r\n- Crewmembers unable to perform duties due to illness. \r\n- Medical emergencies due to illness involving any person on board an aircraft, including passengers and crew. \r\n\r\nDoes NOT include: \r\n- Injuries sustained during flight operations. Injuries are coded as— \r\no WSTRW for injuries sustained as a result of thunderstorms or wind shear, \r\no TURB for injuries sustained as a result of turbulence (excluding turbulence caused by wind shear and/or thunderstorms), \r\no SEC for injuries resulting from intentional acts (suicide, homicide, acts of violence, or self-inflicted injury), \r\no CABIN for any injury sustained on an aircraft not occurring as a result of any events above, such as sprains, cuts, or burns resulting from normal cabin operations (handling bags, operating galley equipment, etc.) \r\n- Injuries, temporary blindness, or other incapacitation resulting from laser attacks, which are coded as SEC. \r\n\r\n\r\nCrossover to/from other occurrence categories: \r\n- Medical emergencies involving persons other than crew members or a medical evacuation patient were coded as CABIN before October 2013. All medical emergencies are now coded as MED. \r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "105", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027273, "valueListId": 16578, "identifier": 106, "parent": 0, "level": 1, "description": "NAV: Navigation error", "detailed": "Navigation errors - Occurrences involving the incorrect navigation of aircraft on the ground or in the air.", "explanation": "Usage notes:\r\nIncludes: \r\n- Lateral navigation errors caused by navigating using the improper navaid or improper programming of aircraft navigation systems\r\n- Airspace incursions resulting from improper navigation, uncertainty of position, improper planning, or failure to follow procedures prior to entering airspace\r\n- Failure to accurately track navigation signals (lateral or vertical), \r\n- Altitude/level busts (see below for exceptions)\r\n- Deviating from ATC/ATM clearances or published procedures (SID/DP, STAR, approach procedures, charted visual procedures), \r\n- Failure to follow clearances or restrictions while operating on the surface of an aerodrome, including— \r\no Taxiing or towing an aircraft on an unassigned taxiway or runway (see crossover section below), \r\no Taxiing or otherwise operating an aircraft on a restricted portion of an aerodrome (cargo ramp, air carrier ramp, general aviation ramp, military ramp, wingspan- or weight-restricted taxiways or runways, etc.) \r\no Take-offs, aborted take-offs, or landings on a taxiway, unassigned runway, or closed runway (see below for exceptions), \r\no Approaches or landings to/on unassigned runways or to/at the wrong aerodrome. \r\n- Taxiway excursions (except following a loss of control on the ground or intentionally steering an aircraft off a taxiway to avoid a collision). \r\n\r\n \r\nDoes NOT include: \r\n- Intentional deviations resulting from a PIC exercising emergency authority. \r\n- Deviations from assigned altitude or course to avoid other aircraft as a result of visual detection or complying with a TCAS RA, which are coded as MAC. \r\n- Deviations from assigned altitude or electronic navigation path as a result of wind shear or turbulence, which are coded as WSTRW or TURB. \r\n- Lateral or vertical deviations resulting from extreme manifestations of loss of aircraft control in flight, which is coded as LOC-I. \r\n- Taxiway excursions due to a loss of control on the ground, which is coded as LOC-G. \r\n- Taxiway excursions to avoid a ground collision, which are coded as AMAN. \r\n- Takeoffs, aborted takeoffs, landings, or approaches to engaged runways due to ATC/ATM error, which are coded as ATM (and MAC if it resulted in a loss of separation). \r\n- Navigation errors at an aerodrome made by vehicles or pedestrians. Code RI if the navigation error results in the vehicle or pedestrian incorrectly entering a runway. Code RAMP if the error meets the usage notes for the RAMP category. \r\n\r\n\r\nCrossover to/from other occurrence categories: \r\n- Code both NAV and MAC if a navigation error causes an AIRPROX/loss of separation. \r\n- Code both NAV and RI for any navigation error that also meets the RI usage notes, including takeoffs/landings without a clearance, wrong runway takeoffs/landings, and wrong aerodrome landings. \r\n- Code both NAV and RAMP if a navigation error occurs during pushback or towing operations. \r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "106", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027274, "valueListId": 16578, "identifier": 98, "parent": 0, "level": 1, "description": "OTHR: Other", "detailed": "Any occurrence not covered under another category.", "explanation": "This category includes any occurrence type that is not covered by any other category.", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "98", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027275, "valueListId": 16578, "identifier": 8, "parent": 0, "level": 1, "description": "RAMP: Ground Handling", "detailed": "Occurrences during (or as a result of) ground handling operations.", "explanation": "Usage Notes: \r\n\r\nIncludes: \r\n- Occurrences that occur while servicing, boarding, loading, and deplaning the aircraft \r\n- Occurrences involving boarding and disembarking while a helicopter is hovering \r\n- Deficiencies or issues related to snow, frost, and/or ice removal from aircraft \r\n- Injuries to people from propeller/main rotor/tail rotor/fan blade strikes \r\n- Pushback/powerback/towing events \r\n- Jet Blast and Prop/rotor downwash ground handling occurrences \r\n- Aircraft external preflight configuration errors (e.g., improper loading and improperly secured doors and latches) that lead to subsequent events. \r\n- All parking areas (ramp, gate, tiedowns). \r\n- Operations at aerodromes, heliports, helidecks, and unprepared operating sites \r\n\r\n\r\nDoes NOT include: \r\n- Collisions while the aircraft is moving under its own power in the gate, ramp, or tiedown area, which are coded as GCOL (except during powerback, which is coded here) \r\n\r\nCrossover to/from other occurrence categories:\r\n- If an external load is involved with an event during ground handling operations, code both RAMP and EXTL. \r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "8", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027276, "valueListId": 16578, "identifier": 15, "parent": 0, "level": 1, "description": "RE: Runway excursion", "detailed": "A veer off or overrun off the runway surface.", "explanation": "Usage Notes:\r\n• Only applicable during either the takeoff or landing phase\r\n• The excursion may be intentional or unintentional. For example, the deliberate veer off to avoid a collision, brought about by a Runway Incursion. In this case, code both categories\r\n• Use RE in all cases where the aircraft left the runway/helipad/helideck regardless of whether the excursion was the consequence of another event or not.\t", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "15", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 3935632, "valueListId": 16578, "identifier": 16, "parent": 0, "level": 1, "description": "RI-O: Runway incursion - other", "detailed": "Collision with, risk of collision, or evasive action taken by an aircraft to avoid, a person or animal on a runway in use.", "explanation": "\tUsage Notes:\r\n·\tIncludes encounters with wildlife (other than birdstrikes which are coded as OTHER) on a runway in use.\r\n·\tIncludes instances where evasive action is taken by the cockpit crew that leads to a collision off the runway or to consequences other than a collision (e.g., gear collapsing).\r\n·\tRunway incursions may occur at controlled or uncontrolled airports.\r\n\r\nNote: Changed as result of ICAO ANC decision to RI-A  (taking out the person)\r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 2, "xsdTag": "16", "aliases": [], "displayOrder": null, "active": false, "alternativeText": null}, {"id": 3935633, "valueListId": 16578, "identifier": 17, "parent": 0, "level": 1, "description": "RI-VA: Rwy incursion-vehicle or a/c", "detailed": "Collision with, risk of collision, or evasive action taken by an aircraft to avoid, a vehicle or other aircraft on a runway in use.", "explanation": "\tUsage Notes:\r\n·\tIncludes instances where evasive action is taken by the cockpit crew to avoid a collision that leads to a later collision off the runway or, to consequences other than a collision (e.g., gear collapsing).\r\n·\tIncludes occurrences where an airborne aircraft lands on an aircraft stopped or moving on a runway in use.\r\n·\tRunway incursions may occur at controlled or uncontrolled airports.\r\n\r\nNote: changed to RI-VAP as result of ICAO Air Navigation Commission decision in 2004 \r\n\r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 2, "xsdTag": "17", "aliases": [], "displayOrder": null, "active": false, "alternativeText": null}, {"id": 4027277, "valueListId": 16578, "identifier": 28, "parent": 0, "level": 1, "description": "RI: Runway incursion - vehicle, aircraft or person", "detailed": "Any occurrence at an aerodrome involving the incorrect presence of an aircraft, vehicle or person on the protected area of a surface designated for the landing and take-off of aircraft.", "explanation": "Usage notes:\r\n·  Definition from Procedures for Air Navigation Services–Air Traffic Management (ICAO DOC 4444) and Manual on the Prevention of Runway Incursions (ICAO DOC 9870), first included in April 2004. \r\n\r\nDoes NOT include: \r\n- Events at unprepared/natural landing sites. \r\n- Occurrences involving animals or birds on the runway which are coded as Wildlife (WILD) or Bird (BIRD). \r\n\r\n\r\nCrossover to/from other occurrence categories: \r\n- Code both RI and NAV for runway incursions resulting from the improper navigation of an aircraft at an aerodrome, or takeoffs, aborted takeoffs, or landings on an unassigned runway. \r\n- Code both RI and ATM for runway incursions resulting from an ATC/ATM error. \r\n- Code both RI and MAC if a runway incursion event causes an AIRPROX/loss of separation while airborne. \r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "28", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027278, "valueListId": 16578, "identifier": 18, "parent": 0, "level": 1, "description": "SCF-NP: System/component failure or malfunction [non-powerplant]", "detailed": "Failure or malfunction of an aircraft system or component - other than the powerplant.", "explanation": "Usage Notes:\r\n• If the failure renders the aircraft uncontrollable it is coded as SCF–NP only, not as loss of control (Loss of Control–Inflight (LOC–I) or Loss of Control–Ground (LOC–G)). However, if the failure does not render the aircraft uncontrollable, but leads to a loss of control, code the event under both SCF–NP and LOC–I or LOC–G, as appropriate.\r\n• Rotorcraft main rotor and tail rotor system, drive system and flight control failures or malfunctions are also coded here.\r\n• Includes errors or failures in software and database systems.\r\n• Includes non-powerplant parts or pieces separating from an aircraft.\r\n• For unmanned aircraft, includes failure or malfunction of ground-based, transmission, or aircraft-based communication systems or components or datalink systems or components.\r\n• Includes failures/malfunctions of ground-based launch or recovery systems equipment.\r\n• Includes all failures/malfunctions, including those related to or caused by\r\nmaintenance issues.", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "18", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027279, "valueListId": 16578, "identifier": 19, "parent": 0, "level": 1, "description": "SCF-PP: powerplant failure or malfunction", "detailed": "Failure or malfunction of an aircraft system or component - related to the powerplant.", "explanation": "Usage Notes:\r\n• If the failure renders the aircraft uncontrollable it is coded as SCF-PP only, not as loss of control (LOC-I or LOC-G). However, if the failure does not render the aircraft uncontrollable, but leads to a loss of control, code the event under both SCF-PP and LOC-I or LOC-G, as appropriate.\r\n• Includes failures or malfunctions of any of the following: propellers, rotors, propeller/main rotor drive train (gearbox, transmission), reversers, and powerplant controls.\r\n• Includes powerplant parts or pieces separating from a powerplant.\r\n• Includes all failures/malfunctions, including those related to or caused by maintenance issues.\r\n• Rotorcraft cyclic, collective and tail rotor drive and control failures or malfunctions are coded as non-powerplant failures (SCF-NP), not SCF-PP.\r\n• The following fuel-related powerplant problems are coded under the category FUEL, not under the category SCF-PP: fuel exhaustion; fuel starvation/mismanagement; fuel contamination; wrong fuel; carburetor and induction icing.\r\n\r\nNOTE: For sub-categorization of SCF-PP, a separate taxonomy has been developed and can be found on the CICTT website http://intlaviationstandards.org.\r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "19", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027280, "valueListId": 16578, "identifier": 20, "parent": 0, "level": 1, "description": "SEC: Security related", "detailed": "Criminal/Security acts which result in accidents or incidents (per International Civil Aviation Organization [ICAO] Annex 13).", "explanation": "Usage Notes:\r\n• While security related acts can lead to accidents as defined as by ICAO Annex 13, they are not considered accidents by some organizations. Regardless, these events have similar consequences in that they result in serious injury or death to person(s) and/or substantial damage to the aircraft. For these reasons, they are categorized as security-related occurrences for prevention purposes only.\r\n• Examples include: a) hijacking and/or aircraft theft; b) interference with a crewmember (e.g., unruly passengers); c) flight control interference; d) ramp/runway/taxiway security; e) sabotage; f) suicide; and g) acts of war.\r\n\r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "20", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027281, "valueListId": 16578, "identifier": 21, "parent": 0, "level": 1, "description": "TURB: Turbulence encounter", "detailed": "In-flight turbulence encounter", "explanation": "Usage Notes:\r\n• Includes encounters with turbulence in clear air, mountain wave, mechanical, and/or cloud associated turbulence.\r\n• Wake vortex encounters are also included here.\r\n• Flights into windshear or thunderstorm related turbulence are coded as WSTRW.\r\n• Includes turbulence encountered by aircraft when operating around or at buildings, structures and objects.\r\n", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "21", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027282, "valueListId": 16578, "identifier": 100, "parent": 0, "level": 1, "description": "UIMC: Unintended flight in IMC", "detailed": "Unintended flight in Instrument Meteorological Conditions (IMC)", "explanation": "Usage Notes:\r\n• May be used as a precursor to CFIT, LOC-I or LALT.\r\n• Applicable if the pilot was flying according to Visual Flight Rules (VFR), as defined in Annex 2 – Rules of the Air – to the Convention on International Civil Aviation and by any reason found oneself inadvertently in IMC\r\n• Only to be used when loss of visual references is encountered,\r\n• Only to be used if pilot not qualified to fly in IMC and/or aircraft not equipped to fly in IMC", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "100", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027283, "valueListId": 16578, "identifier": 99, "parent": 0, "level": 1, "description": "UNK: Unknown or undetermined", "detailed": "Insufficient information exists to categorize the occurrence.", "explanation": "Usage Notes:\r\n• Includes cases where the aircraft is missing.\r\n• Includes those occurrences where there is not enough information at hand to classify the occurrence or where additional information is expected in due course to better classify the occurrence.", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "99", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027284, "valueListId": 16578, "identifier": 22, "parent": 0, "level": 1, "description": "USOS: Undershoot/overshoot", "detailed": "A touchdown off the runway surface.", "explanation": "Usage Notes:\r\n• An undershoot/overshoot of a runway/helipad/helideck occurs in close proximity to the runway/helipad/helideck and also includes offside touchdowns and any occurrence where the landing gear touches off the runway/helipad/helideck surface.\r\n• Off-airport emergency landings are excluded from this category.\r\n• To be used for occurrences during the landing phase.\r\n• Includes offside touchdowns on heliports, helidecks and other defined areas to be used wholly or in part for the arrival, departure and surface movement of helicopters (does not include helicopter unprepared or natural landing sites).\r\n\r\nDo not use ARC in conjunction with USOS.", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "22", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027285, "valueListId": 16578, "identifier": 27, "parent": 0, "level": 1, "description": "WILD: Collision Wildlife", "detailed": "Collision with, risk of collision, or evasive action taken by an aircraft to avoid wildlife on a runway or on a helipad/helideck in use.", "explanation": "Usage Notes:\r\nIncludes encounters with wildlife on a runway in use or on any other movement area of\r\nthe aerodrome.\r\n• Includes instances where evasive action is taken by the flight crew that leads to a collision off the movement area of the aerodrome or to consequences other than a collision (e.g., gear collapsing).\r\n• Wildlife encounters may occur at controlled or uncontrolled airports, or on unprepared/natural landing sites.\r\n• Excludes bird strikes, which are coded as Bird (BIRD).\r\n\r\nNote: Was previously described as RI-A.", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "27", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}, {"id": 4027286, "valueListId": 16578, "identifier": 23, "parent": 0, "level": 1, "description": "WSTRW: Windshear or thunderstorm", "detailed": "Flight into windshear or thunderstorm.", "explanation": "Usage Notes:\n• Includes flight into wind shear and/or thunderstorm-related weather.\n• Includes in-flight events related to hail.\n• Includes events related to lightning strikes.\n• Includes events related to heavy rain (not just in a thunderstorm).\n• Icing and turbulence encounters are coded separately (see Icing (ICE) and TurbulenceEncounter (TURB)).", "hasChild": false, "specialValue": {"id": 0, "name": "Normal"}, "status": 1, "xsdTag": "23", "aliases": [], "displayOrder": null, "active": true, "alternativeText": null}]