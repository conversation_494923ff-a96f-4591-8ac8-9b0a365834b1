//edit component
import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray, FormsModule, ReactiveFormsModule, AbstractControl, ValidationErrors } from '@angular/forms';
import { OccurrencesService } from '../../occurrences/occurrences.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Occurrence, Aircraft, AddressedTo, Country, User, Report } from '../../../../util/@types';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ReportsService } from '../reports.service';
import { ToastModule } from 'primeng/toast';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { DropdownModule } from 'primeng/dropdown';
import {ConfirmDialogModule } from 'primeng/confirmdialog';
import { ReportPdfService } from '../pdf.service';

@Component({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ToastModule,
    AutoCompleteModule,
    DropdownModule,
    ConfirmDialogModule,
  ],
  providers: [MessageService, ConfirmationService],
  standalone: true,
  selector: 'app-edit-notification-form',
  templateUrl: './edit.component.html',
})
export class EditReportComponent implements OnInit {
  notificationForm!: FormGroup;
  loading = false
  report!: Report
  involvedAircraft!: any[]
  addressedToOptions = Object.values(AddressedTo);

  countries: Country[] = [];
  filteredCountries: Country[] = [];

  investigators: User[] = []
  filteredInvestigators: User[] = []

  constructor(
    private fb: FormBuilder,
    private occurrenceService: OccurrencesService,
    private route: ActivatedRoute,
    private router: Router,
    private messageService: MessageService,
    private reportsService: ReportsService,
    private cdr: ChangeDetectorRef,
    private confirmationService: ConfirmationService,
    private pdfService: ReportPdfService
  ) { }

  ngOnInit(): void {
    this.initializeForms();
    // this.loadInvestigators()
    // this.loadCountries()
    this.loadReport()
  }

  get addressedToControls(): FormArray {
    return this.notificationForm.get('addressedTo') as FormArray;
  }

  addressedToStates: {[key: string]: boolean} = {
    stateOfRegistry: false,
    stateOfDesign: false,
    stateOfManufacturer: false,
    stateOfOperator: false,
    ICAO: false
  };

  initializeForms() {
    this.notificationForm = this.fb.group({
      referenceNumber: ['', Validators.required],

      addressedToRegistry: [false],
      addressedToDesign: [false],
      addressedToManufacturer: [false],
      addressedToOperator: [false],
      addressedToICAO: [false],

      stateOfRegistry: [''],
      stateOfDesignCountry: [''],
      stateOfManufacturerCountry: [''],
      stateOfOperatorCountry: [''],

      // a) Occurrence Type
      occurrenceType: ['', [Validators.required]],

      // Aircraft-related sections grouped together
      aircraftInfo: this.fb.array([this.createAircraftInfo()]),

      groundPeopleInjured: [0],
      groundPeoplePerished: [0],

      // d) Date and Time
      occurrenceDate: ['',],
      occurrenceTimeUTC: ['',],
      occurrenceTimeLocal: ['',],

      // f) Aircraft Location
      position: ['',],
      latitude: ['',],
      longitude: ['',],

      // h) Description
      occurrenceDescription: ['', Validators.required],
      damageExtent: ['', Validators.required],

      // i) Investigation Details
      investigationExtent: ['',],
      investigationDelegation: ['',],

      // j) Physical Characteristics
      areaCharacteristics: ['',],
      accessRequirements: ['',],

      // k) Investigation Authority
      originatingAuthority: ['', Validators.required],
      investigatorInCharge: this.fb.group({
        name: ['', Validators.required],
        mobile: ['', [Validators.required, Validators.pattern('^[0-9+()-\s]*$')]],
        email: ['', [Validators.required, Validators.email]]
      }),

      dangerousGoodsPresent: [false],
      dangerousGoodsDescription: [''],

    },
    {
      validators: this.atLeastOneSelectedValidator()
    });

    Object.keys(this.addressedToStates).forEach(key => {
      const controlName = 'addressedTo' + key.charAt(0).toUpperCase() + key.slice(1);
      this.notificationForm.get(controlName)?.valueChanges.subscribe(checked => {
        this.addressedToStates[key as keyof typeof this.addressedToStates] = checked;
        if (!checked) {
          const countryControl = this.notificationForm.get(key + 'Country');
          if (countryControl) {
            countryControl.setValue('');
          }
        }
      });
    });
  }

  loadCountries() {
    this.reportsService.getCountries().subscribe({
      next: (data) => {
        this.countries = data.sort((a, b)=> a.name.localeCompare(b.name));
        this.filteredCountries = [];
        this.cdr.detectChanges();
      },
      error: (error) => {
        this.handleError('Failed to load countries', error)
      }
    });
  }

  loadInvestigators(){
    this.reportsService.getInvestigators().subscribe({
      next: (data) => {
        this.investigators = data
        this.filteredInvestigators = [];
        this.cdr.detectChanges();
      },
      error: (error) => {
        this.handleError('Failed to load countries', error)
      }
    })
  }

  filterCountries(event: any) {
    const query = event.query.toLowerCase();
    this.filteredCountries = query ? 
      this.countries.filter(country => country.name.toLowerCase().includes(query)) :
      this.countries;
  }

  filterInvestigators(event: any) {
    const query = event.query.toLowerCase();
    this.filteredInvestigators = query ? 
      this.investigators.filter(i => i.name.toLowerCase().includes(query)) :
      this.investigators;
  }

  onClear() {
    this.filteredCountries = [];
    this.cdr.detectChanges();
  }

  async loadReport() {
    // await Promise.all([
      this.loadCountries(),
      this.loadInvestigators()
    // ]);
    try {
      this.loading = true;
      const id = this.route.snapshot.paramMap.get('id') || '';
  
      if (!id) {
        throw new Error('No Report ID provided');
      }
  
      this.report = await this.reportsService.getReportById(id);
      this.involvedAircraft = this.report.aircraftInfo || [];


      while (this.aircraftInfo.length !== 0) {
        this.aircraftInfo.removeAt(0);
      }
  
      this.involvedAircraft.forEach(() => {
        this.aircraftInfo.push(this.createAircraftInfo());
      });
  
      if (this.report.addressedTo) {
        this.notificationForm.patchValue({
          addressedToRegistry: this.report.addressedTo.includes(AddressedTo.StateOfRegistry),
          addressedToDesign: this.report.addressedTo.includes(AddressedTo.StateOfDesign),
          addressedToManufacturer: this.report.addressedTo.includes(AddressedTo.StateOfManufacturer),
          addressedToOperator: this.report.addressedTo.includes(AddressedTo.StateOfOperator),
          addressedToICAO: this.report.addressedTo.includes(AddressedTo.ICAO),
        });
      }

      const patchCountries = {
        stateOfRegistry: this.countries.find(c => c.name === this.report.stateOfRegistry),
        stateOfDesignCountry: this.countries.find(c => c.name === this.report.stateOfDesign),
        stateOfManufacturerCountry: this.countries.find(c => c.name === this.report.stateOfManufacturer),
        stateOfOperatorCountry: this.countries.find(c => c.name === this.report.stateOfOperator),
      };
    
      this.notificationForm.patchValue({
        referenceNumber: this.report.referenceNumber,
        occurrenceType: this.report.occurrenceType,
        occurrenceDate: this.formatDateForInput(this.report.occurrenceDate),
        occurrenceTimeUTC: this.formatTimeForInput(this.report.occurrenceTimeUTC),
        occurrenceTimeLocal: this.formatTimeForInput(this.report.occurrenceTimeLocal),
        position: this.report.position,
        latitude: this.report.latitude,
        longitude: this.report.longitude,
        occurrenceDescription: this.report.occurrenceDescription,
        damageExtent: this.report.damageExtent,
        investigationExtent: this.report.investigationExtent,
        investigationDelegation: this.report.investigationDelegation,
        areaCharacteristics: this.report.areaCharacteristics,
        accessRequirements: this.report.accessRequirements,
        dangerousGoodsPresent: this.report.dangerousGoodsPresent,
        dangerousGoodsDescription: this.report.dangerousGoodsDescription,
        groundPeopleInjured: this.report.groundPeopleInjured,
        groundPeoplePerished: this.report.groundPeoplePerished,
        originatingAuthority: this.report.originatingAuthority || 'AAID-Rwanda',
        ...patchCountries
      });

      const selectedInvestigator = this.investigators.find(i=>i.email === this.report.investigatorEmail)
      console.log(this.investigators)
      console.log(selectedInvestigator)
      this.notificationForm.get('investigatorInCharge')?.patchValue({
        name: selectedInvestigator,
        mobile: this.report.investigatorMobile,
        email: this.report.investigatorEmail
      });
  
      this.involvedAircraft.forEach((aircraft, index) => {
        const aircraftForm = this.aircraftInfo.at(index);
        
        aircraftForm.get('aircraftDetails')?.patchValue({
          manufacturer: aircraft.manufacturer,
          model: aircraft.model,
          nationality: aircraft.nationality,
          registrationMarks: aircraft.registrationMarks,
          serialNumber: aircraft.serialNumber,
          owner: aircraft.owner,
          operator: aircraft.operator,
          hirer: aircraft.hirer
        });
  
        aircraftForm.get('crewDetails')?.patchValue({
          pilotQualification: aircraft.pilotQualification,
          crewNationality: aircraft.crewNationality,
          passengerNationality: aircraft.passengerNationality
        });
  
        aircraftForm.get('flightDetails')?.patchValue({
          lastDeparturePoint: aircraft.lastDeparturePoint,
          intendedLandingPoint: aircraft.intendedLandingPoint
        });
  
        if (aircraft.casualties) {
          aircraftForm.get('casualties')?.patchValue({
            crew: {
              aboard: aircraft.casualties.crew?.aboard || 0,
              killed: aircraft.casualties.crew?.killed || 0,
              injured: aircraft.casualties.crew?.injured || 0
            },
            passengers: {
              aboard: aircraft.casualties.passengers?.aboard || 0,
              killed: aircraft.casualties.passengers?.killed || 0,
              injured: aircraft.casualties.passengers?.injured || 0
            }
          });
        }
      });
  
      this.loading = false;
      this.cdr.detectChanges();
  
    } catch (error) {
      this.handleError('Failed to load occurrence details', error);
      this.loading = false;
    }
    this.cdr.detectChanges();
  }

  private atLeastOneSelectedValidator(): ValidationErrors | null {
    return (formGroup: AbstractControl): ValidationErrors | null => {
      const addressedToControls = [
        formGroup.get('addressedToRegistry')?.value,
        formGroup.get('addressedToDesign')?.value,
        formGroup.get('addressedToManufacturer')?.value,
        formGroup.get('addressedToOperator')?.value,
        formGroup.get('addressedToICAO')?.value
      ];
  
      return addressedToControls.some(value => value === true) 
        ? null 
        : { addressedTo: true };
    };
  }

  get addressedTo(): FormArray {
    return this.notificationForm.get('addressedTo') as FormArray;
  }

  get addressedToError(): boolean {
    return this.addressedTo.errors?.['atLeastOne'] && this.addressedTo.touched;
  }

  formatDateForInput(date: Date | string | undefined): string {
    if (!date) return '';
    const d = new Date(date);
    return new Date(d.getTime() - d.getTimezoneOffset() * 60000).toISOString().slice(0, 10);
  }
  
  formatTimeForInput(date: Date | string | undefined): string {
    if (!date) return '';
    const d = new Date(date);
    return d.toTimeString().slice(0, 5);
  }

  createAircraftInfo(): FormGroup {
    return this.fb.group({
      // b) Aircraft Details
      aircraftDetails: this.fb.group({
        manufacturer: ['', Validators.required],
        model: ['', ],
        nationality: ['', ],
        registrationMarks: ['', ],
        serialNumber: ['', ],
        owner: ['', ],
        operator: ['', ],
        hirer: ['']
      }),

      // c) Crew and Passenger Details
      crewDetails: this.fb.group({
        pilotQualification: ['', ],
        crewNationality: ['', ],
        passengerNationality: ['', ]
      }),

      // e) Flight Path
      flightDetails: this.fb.group({
        lastDeparturePoint: ['', ],
        intendedLandingPoint: ['', ]
      }),

      // g) Casualties
      casualties: this.fb.group({
        crew: this.fb.group({
          aboard: [0,],
          killed: [0,],
          injured: [0,]
        }),
        passengers: this.fb.group({
          aboard: [0,],
          killed: [0,],
          injured: [0,]
        }),
      })
    });
  }

  get aircraftInfo(): FormArray {
    return this.notificationForm.get('aircraftInfo') as FormArray;
  }

  addAircraft(): void {
    this.aircraftInfo.push(this.createAircraftInfo());
  }

  removeAircraft(index: number): void {
    if (this.aircraftInfo.length > 1) {
      this.aircraftInfo.removeAt(index);
    }
  }


  async onSubmit(): Promise<void> {

    if (this.notificationForm.valid) {
      this.loading = true;
      try {
        const formValue = this.notificationForm.value;
        
        await this.reportsService.updateReport(this.report.id, formValue);
  
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Report updated successfully'
        });
  
        // this.router.navigate(['/portal/reports']);
      } catch (error: any) {
        console.error('Error submitting report:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: error.response?.data?.message || 'Failed to update report'
        });
      } finally {
        this.loading = false;
      }
    } else {
      this.markFormGroupTouched(this.notificationForm);
      this.messageService.add({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Please fill in all required fields correctly'
      });
    }
  }

  confirmSubmit(){
    this.confirmationService.confirm({
      message: `Are you sure you want to submit this report (${this.report.referenceNumber}) for approval?`,
      header: 'Confirm Submission',
      icon: 'pi pi-info-circle',
      acceptIcon:"none",
      rejectIcon:"none",
      acceptButtonStyleClass:"p-button-danger p-button-text",
      rejectButtonStyleClass:"p-button-text p-button-text mr-4", 
      accept: () => this.submitForApproval()
    });
  }

  async submitForApproval(){
    try {

      await this.onSubmit()
      await this.reportsService.submitForApproval(this.report.id)

      this.messageService.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Report submitted successfully'
      });
      this.loadReport()
    } catch (error) {
      this.handleError('Failed to submit report', error)
    } 
  }

  private markFormGroupTouched(formGroup: FormGroup | FormArray): void {
    Object.values(formGroup.controls).forEach(control => {
      if (control instanceof FormGroup || control instanceof FormArray) {
        this.markFormGroupTouched(control);
      } else {
        control.markAsTouched();
      }
    });
  }

  private handleError(message: string, error: any) {
    console.error(error);
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: error.response?.data?.message || error.message || message
    });
  }

  private formatDate(dateString: string | undefined): string {
    if (!dateString) return '';
    return new Date(dateString).toISOString().split('T')[0];
  }

  private formatTime(dateString: string | undefined): string {
    if (!dateString) return '';
    return new Date(dateString).toISOString().split('T')[1].substr(0, 5);
  }

  private formatCoordinate(coordinate: string, positiveDirection: string, negativeDirection: string): string {
    if (!coordinate || coordinate.trim() === '') return '';

    const num = parseFloat(coordinate);
    if (isNaN(num)) return '';

    const direction = num >= 0 ? positiveDirection : negativeDirection;
    const abs = Math.abs(num);
    const degrees = Math.floor(abs);
    const minutes = Math.floor((abs - degrees) * 60);
    const seconds = Math.round(((abs - degrees) * 60 - minutes) * 60);

    return `${degrees}°${minutes}'${seconds}"${direction}`;
  }

  private mapOccurrenceType(type: string): string {
    const typeMap: { [key: string]: string } = {
      'ACCIDENT': 'ACCID',
      'SERIOUS_INCIDENT': 'SINCID',
      'INCIDENT': 'INCID'
    };
    return typeMap[type] || '';
  }


  onInvestigatorChange(event: any): void {
    const selectedInvestigator = event.value;
    if (selectedInvestigator) {
      const investigatorGroup = this.notificationForm.get('investigatorInCharge');
      console.log("selected investigator: ", selectedInvestigator)
      investigatorGroup?.patchValue({
        email: selectedInvestigator.email || '',
        mobile: selectedInvestigator.telephone || '',
      });
    }
  }

  onUTCTimeChange(event: any): void {
    const utcTime = event.target.value;
    if (utcTime) {
      const date = this.notificationForm.get('occurrenceDate')?.value;
      if (date) {
        const [hours, minutes] = utcTime.split(':');
        const utcDate = new Date(date);
        utcDate.setUTCHours(parseInt(hours), parseInt(minutes));
        
        const localTime = this.formatTimeForInput(utcDate);
        this.notificationForm.patchValue({
          occurrenceTimeLocal: localTime
        });
      }
    }
  }

  onLocalTimeChange(event: any): void {
    const localTime = event.target.value;
    if (localTime) {
      const date = this.notificationForm.get('occurrenceDate')?.value;
      if (date) {
        const [hours, minutes] = localTime.split(':');
        const localDate = new Date(date);
        localDate.setHours(parseInt(hours), parseInt(minutes));
        
        const utcTime = this.formatTimeForInput(new Date(localDate.getTime() + localDate.getTimezoneOffset() * 60000));
        this.notificationForm.patchValue({
          occurrenceTimeUTC: utcTime
        });
      }
    }
  }

  async downloadPdf(){
    await this.pdfService.generateReportPdf(this.report)
  }

  async preview(){
    await this.pdfService.generatePreview(this.report)
  }
}