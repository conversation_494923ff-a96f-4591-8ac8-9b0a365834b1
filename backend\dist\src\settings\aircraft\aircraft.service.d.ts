import { PrismaService } from 'src/prisma/prisma.service';
import { ApiResponse } from 'src/utils/@types';
import { CreateAircraftDto, UpdateAircraftDto } from './dto';
export declare class AircraftService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    create(data: CreateAircraftDto): Promise<ApiResponse<any>>;
    findAll(page?: number, limit?: number): Promise<ApiResponse<any>>;
    findOne(id: string): Promise<ApiResponse<any>>;
    update(id: string, data: UpdateAircraftDto): Promise<ApiResponse<any>>;
    remove(id: string): Promise<ApiResponse<any>>;
    search(query: string, page: number, limit?: number): Promise<ApiResponse<any>>;
    searchManufacturers(query: string): Promise<{
        data: string[];
    }>;
    searchModels(query: string, manufacturer: string): Promise<{
        data: string[];
    }>;
}
