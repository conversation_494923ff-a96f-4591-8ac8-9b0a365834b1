<div class="w-full h-full p-8 flex flex-col gap-4 overflow-scroll bg-gray-50">
    <!-- Header Section -->
    <div class="flex items-center justify-between">
        <h2 class="text-2xl font-bold text-gray-800">Occurrence Reports</h2>
        <!-- <button class="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors">
            New Report
        </button> -->
    </div>

    <!-- Search and Filter Section -->
    <div class="flex items-center gap-4 pt-8">
        <div class="relative w-full">
            <i class="pi pi-search text-lg absolute top-3 left-3 text-gray-500"></i>
            <input [(ngModel)]="searchQuery" (input)="onSearch($event)"
                class="outline-none bg-white py-3 pl-10 border border-gray-300 rounded-lg text-gray-700 w-full focus:ring-2 focus:ring-primary-500 transition-all"
                type="search" placeholder="Search reports by reference number, type, or location...">
        </div>
        <select [(ngModel)]="selectedType" (change)="onTypeChange($event)"
            class="py-3 px-4 border border-gray-300 rounded-lg text-gray-700 focus:ring-2 focus:ring-primary-500">
            <option value="">All Types</option>
            <option value="ACCID">Accident</option>
            <option value="SINCID">Serious Incident</option>
            <option value="INCID">Incident</option>
        </select>
    </div>

    <!-- Reports Grid -->
    <div class="pt-6">
        <div *ngIf="filteredReports.length === 0" class="text-center py-12 bg-white rounded-lg border border-gray-300">
            <i class="pi pi-file text-4xl text-gray-400"></i>
            <p class="text-gray-600 mt-2">No reports found.</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div *ngFor="let report of filteredReports"
                class="bg-white border border-gray-300 rounded-lg hover:shadow-lg transition-shadow">
                <!-- Card Header -->
                <div class="p-4 border-b border-gray-200">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800">{{ report.referenceNumber }}</h3>
                            <p class="text-sm text-gray-500">{{ formatDateTime(report.createdAt) }}</p>
                        </div>
                        <span [ngClass]="{
                            'bg-yellow-100 text-yellow-800': report.status === 'PENDING',
                            'bg-green-100 text-green-800': report.status === 'APPROVED',
                            'bg-red-100 text-red-800': report.status === 'REVERTED',
                            'bg-gray-100 text-gray-800': !report.status
                        }" class="text-xs px-3 py-1 rounded-full font-medium">
                            {{report.status ?? "Not Submitted"}}
                        </span>
                    </div>
                </div>

                <!-- Card Content -->
                <div class="p-4 space-y-3">
                    <div class="flex items-center gap-2 text-sm">
                        <i class="pi pi-tag text-gray-400"></i>
                        <span class="text-gray-700">{{report.occurrenceType}}</span>
                    </div>

                    <div *ngIf="report.occurrenceDate" class="flex items-center gap-2 text-sm">
                        <i class="pi pi-calendar text-gray-400"></i>
                        <span class="text-gray-700">{{formatDate(report.occurrenceDate)}}</span>
                    </div>

                    <div *ngIf="report.position" class="flex items-center gap-2 text-sm">
                        <i class="pi pi-map-marker text-gray-400"></i>
                        <span class="text-gray-700">{{report.position}}</span>
                    </div>
                   
                </div>

                <!-- Card Footer -->
                <div class="p-4 border-t border-gray-200 flex justify-between items-center">
                    <a [routerLink]="['/dg/reports/edit', report.id]"
                        class="text-primary-500 hover:text-primary-600 font-medium flex items-center gap-1">
                        <span>Open Report</span>
                        <i class="pi pi-arrow-right"></i>
                    </a>
                    <button class="text-gray-400 hover:text-gray-600">
                        <i class="pi pi-ellipsis-v"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <p-confirmDialog></p-confirmDialog>
    <p-toast position="top-right"></p-toast>
</div>