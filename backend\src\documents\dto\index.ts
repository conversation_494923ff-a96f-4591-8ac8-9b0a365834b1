import { ApiProperty, PartialType } from "@nestjs/swagger";
import { DocumentType } from "@prisma/client";
import { IsEnum, IsString, IsUrl } from "class-validator";

export class CreateDocumentDto {

    @ApiProperty()
    @IsString()
    name: string

    @ApiProperty()
    @IsString()
    url: string

    @ApiProperty()
    @IsEnum(DocumentType)
    type: DocumentType

    @ApiProperty()
    @IsString()
    occurrenceId: string
}

export class UpdateDocumentDto extends PartialType(CreateDocumentDto) { }