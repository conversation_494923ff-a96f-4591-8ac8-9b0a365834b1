import { Injectable } from "@angular/core";
import { AircraftInfo, PaginatedResponse } from "../../../util/@types";
import { AxiosService } from "../../../util/axios/axios.service";

@Injectable({
  providedIn: 'root'
})
export class AircraftsService {
  constructor(private axiosService: AxiosService) { }

  async getAircrafts(page: number = 1, limit: number = 10): Promise<PaginatedResponse<AircraftInfo>> {
    try {
      const response = await this.axiosService.axios.get('/settings/aircraft', {
        params: { page, limit }
      });
      console.log(response.data)
      return response.data.data as PaginatedResponse<AircraftInfo>;
    } catch (error) {
      throw error;
    }
  }

  async getAircraft(id: string): Promise<AircraftInfo> {
    try {
      const response = await this.axiosService.axios.get(`/settings/aircraft/${id}`);
      return response.data.data as AircraftInfo;
    } catch (error) {
      throw error;
    }
  }

  async createAircraft(
    modelFullName: string,
    description: string,
    designator: string,
    manufacturerCode: string,
    aircraftDescription: string,
    engineCount?: number,
    engineType?: string,
    wtc?: string,
    wtg?: string,
  ): Promise<AircraftInfo> {
    try {
      const response = await this.axiosService.axios.post('/settings/aircraft', {
        modelFullName,
        description,
        designator,
        manufacturerCode,
        aircraftDescription,
        engineCount,
        engineType,
        wtc,
        wtg,
      })
      return response.data.data as AircraftInfo
    } catch (error) {
      throw error
    }
  }

  async updateAircraft(
    id: string,
    modelFullName: string,
    description: string,
    designator: string,
    manufacturerCode: string,
    aircraftDescription: string,
    engineCount?: number,
    engineType?: string,
    wtc?: string,
    wtg?: string,
  ): Promise<AircraftInfo> {
    try {
      const response = await this.axiosService.axios.patch(`/settings/aircraft/${id}`, {
        modelFullName,
        description,
        designator,
        manufacturerCode,
        aircraftDescription,
        engineCount,
        engineType,
        wtc,
        wtg,
      })
      return response.data.data as AircraftInfo
    } catch (error) {
      throw error
    }
  }

  async deleteAircraft(id: string): Promise<AircraftInfo> {
    try {
      const response = await this.axiosService.axios.delete(`/settings/aircraft/${id}`)
      return response.data.data as AircraftInfo
    } catch (error) {
      throw error
    }
  }

  async search(query: string, page: number, limit: number = 10): Promise<PaginatedResponse<AircraftInfo>> {
    try {
      const response = await this.axiosService.axios.get('/settings/aircraft/search', {
        params: { query, page, limit }
      });
      return response.data.data as PaginatedResponse<AircraftInfo>;
    } catch (error) {
      throw error;
    }
  }

  searchManufacturers(query: string): Promise<any> {
    return this.axiosService.axios.get(`/settings/aircraft/manufacturers?q=${query}`)
  }

  searchModels(query: string, manufacturer: string): Promise<any> {
    return this.axiosService.axios.get(`/settings/aircraft/models?q=${query}&manufacturer=${manufacturer}`)
  }
}