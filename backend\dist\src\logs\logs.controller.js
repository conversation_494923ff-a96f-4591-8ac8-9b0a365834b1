"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LogsController = void 0;
const common_1 = require("@nestjs/common");
const logs_service_1 = require("./logs.service");
const swagger_1 = require("@nestjs/swagger");
const client_1 = require("@prisma/client");
const role_guard_1 = require("../utils/guards/role.guard");
const role_decorator_1 = require("../utils/decorators/role.decorator");
let LogsController = class LogsController {
    constructor(logsService) {
        this.logsService = logsService;
    }
    async getLogs(page, pageSize, action, sourceUrl, sourceIpAddress, sourceOS, sourceBrowser, url, method, userId, sortBy) {
        const filters = {
            action,
            sourceUrl,
            sourceIpAddress,
            sourceOS,
            sourceBrowser,
            url,
            method,
            userId,
        };
        Object.keys(filters).forEach(key => filters[key] === undefined && delete filters[key]);
        return this.logsService.getLogs(filters, sortBy, page, pageSize);
    }
    search(query) {
        return this.logsService.search(query);
    }
    async getUserLogs(userId, page, pageSize, sortBy) {
        return this.logsService.getUserLogs(userId, sortBy, page, pageSize);
    }
};
exports.LogsController = LogsController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'pageSize', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'action', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'sourceUrl', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'sourceIpAddress', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'sourceOS', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'sourceBrowser', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'url', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'method', required: false, enum: client_1.HTTP_Method }),
    (0, swagger_1.ApiQuery)({ name: 'userId', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'sortBy', required: false }),
    __param(0, (0, common_1.Query)('page', new common_1.DefaultValuePipe(1), common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)('limit', new common_1.DefaultValuePipe(10), common_1.ParseIntPipe)),
    __param(2, (0, common_1.Query)('action')),
    __param(3, (0, common_1.Query)('sourceUrl')),
    __param(4, (0, common_1.Query)('sourceIpAddress')),
    __param(5, (0, common_1.Query)('sourceOS')),
    __param(6, (0, common_1.Query)('sourceBrowser')),
    __param(7, (0, common_1.Query)('url')),
    __param(8, (0, common_1.Query)('method')),
    __param(9, (0, common_1.Query)('userId')),
    __param(10, (0, common_1.Query)('sortBy')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String, String, String, String, String, String, String, String, Object]),
    __metadata("design:returntype", Promise)
], LogsController.prototype, "getLogs", null);
__decorate([
    (0, common_1.Get)('search'),
    __param(0, (0, common_1.Query)('query')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LogsController.prototype, "search", null);
__decorate([
    (0, common_1.Get)('user/:userId'),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'pageSize', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'sortBy', required: false }),
    __param(0, (0, common_1.Query)('userId')),
    __param(1, (0, common_1.Query)('page', new common_1.DefaultValuePipe(1), common_1.ParseIntPipe)),
    __param(2, (0, common_1.Query)('pageSize', new common_1.DefaultValuePipe(10), common_1.ParseIntPipe)),
    __param(3, (0, common_1.Query)('sortBy')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number, Object]),
    __metadata("design:returntype", Promise)
], LogsController.prototype, "getUserLogs", null);
exports.LogsController = LogsController = __decorate([
    (0, common_1.UseGuards)(role_guard_1.RolesGuard),
    (0, role_decorator_1.Roles)(client_1.Role.ADMIN),
    (0, swagger_1.ApiTags)('Logs'),
    (0, common_1.Controller)('logs'),
    __metadata("design:paramtypes", [logs_service_1.LogsService])
], LogsController);
//# sourceMappingURL=logs.controller.js.map