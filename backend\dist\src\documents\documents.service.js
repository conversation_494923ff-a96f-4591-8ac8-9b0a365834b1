"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const _types_1 = require("../utils/@types");
let DocumentsService = class DocumentsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createDocument(data) {
        const occurence = await this.prisma.occurrence.findUnique({
            where: {
                id: data.occurrenceId
            }
        });
        if (!occurence) {
            throw new common_1.BadRequestException('Occurrence does not exist');
        }
        try {
            const newDoc = await this.prisma.document.create({
                data
            });
            return new _types_1.ApiResponse(true, 'Document created', newDoc);
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException();
        }
    }
    async getDocuments() {
        const docs = await this.prisma.document.findMany({
            orderBy: {
                createdAt: 'desc'
            },
            include: {
                occurrence: {
                    select: {
                        referenceNumber: true,
                        type: true,
                        occurrenceCategory: true
                    }
                }
            }
        });
        return new _types_1.ApiResponse(true, '', docs);
    }
    async getDocument(id) {
        const doc = await this.prisma.document.findUnique({
            where: { id },
            include: {
                occurrence: true
            }
        });
        if (!doc) {
            throw new common_1.NotFoundException('document not found');
        }
        return new _types_1.ApiResponse(true, '', doc);
    }
    async updateDocument(id, data) {
        const doc = await this.prisma.document.findUnique({
            where: { id }
        });
        if (!doc) {
            throw new common_1.NotFoundException('Document not found');
        }
        const updatedDoc = await this.prisma.document.update({
            where: { id },
            data
        });
        return new _types_1.ApiResponse(true, 'Document Updated', updatedDoc);
    }
    async remove(id) {
        const doc = await this.prisma.document.findUnique({
            where: {
                id
            }
        });
        if (!doc) {
            throw new common_1.NotFoundException('Document not found');
        }
        const deletedDoc = await this.prisma.document.delete({
            where: { id }
        });
        return new _types_1.ApiResponse(true, '', deletedDoc);
    }
};
exports.DocumentsService = DocumentsService;
exports.DocumentsService = DocumentsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], DocumentsService);
//# sourceMappingURL=documents.service.js.map