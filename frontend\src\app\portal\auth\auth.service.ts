import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { jwtDecode, JwtPayload } from 'jwt-decode';
import { AxiosService } from '../../util/axios/axios.service';

export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  telephone: string
  profilePicture: string
}

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  constructor(private axiosService: AxiosService, private router: Router) { }

  /**
   * Logs in the user and stores the token in localStorage.
   * @param email User's email
   * @param password User's password
   */
  async login(identifier: string, password: string): Promise<any> {
    try {
      const response = await this.axiosService.axios.post('/auth/login', { identifier, password });
      const { token } = response.data.data;
      if (token) {
        localStorage.setItem('jwt_token', token);
        return true;
      }
      return response.data;
    } catch (error: any) {
      throw error
    }
  }

  async verify2fa(id: string, code: string) {
    try {
      const response = await this.axiosService.axios.post('/auth/verify-2fa', { id,  code });
      const { token } = response.data.data;
      if (token) {
        localStorage.setItem('jwt_token', token);
        return true;
      }
      return false;
    } catch (error) {
      throw error
    }
  }

  async sendResetLink(email: string): Promise<boolean> {
    try {
      const response = await this.axiosService.axios.post('/users/reset-password', { email });
      const resp = response.data.data

      if (resp) {
        return true
      }
      return false
    } catch (error: any) {
      throw error
    }
  }

  async confirmResetPassword(token: string, newPassword: string): Promise<boolean> {
    try {
      const response = await this.axiosService.axios.post('/users/confirm-reset-password', { token, newPassword });
      const resp = response.data.data

      if (resp) {
        return true
      }
      return false
    } catch (error: any) {
      throw error
    }
  }

  async activateAccount(token: string, password: string) {
    try {
      const response = await this.axiosService.axios.patch('/users/activate', { token, password });
      const resp = response.data.data
      return resp as { qrCode: string, secret: string }
    } catch (error: any) {
      throw error
    }
  }


  /**
   * Decodes the JWT token to extract user information.
   * @returns User object if token is valid, otherwise null.
   */
  getUserInfo(): User | null {
    const token = localStorage.getItem('jwt_token');
    if (token) {
      try {
        const decodedToken = jwtDecode<JwtPayload & User>(token);
        return {
          id: decodedToken.id,
          name: decodedToken.name,
          email: decodedToken.email,
          role: decodedToken.role,
          telephone: decodedToken.telephone,
          profilePicture: decodedToken.profilePicture
        };
      } catch (error) {
        console.error('Invalid token', error);
        return null;
      }
    }
    return null;
  }

  /**
   * Gets the user's role from the JWT token.
   * @returns Role as a string or null if not available.
   */
  getRole(): string | null {
    const userInfo = this.getUserInfo();
    return userInfo ? userInfo.role : null;
  }

  /**
   * Logs out the user by clearing localStorage and navigating to the login page.
   */
  logout(): void {
    localStorage.removeItem('jwt_token');
    this.router.navigate(['/portal/auth/login']);
  }

  /**
   * Checks if the user is logged in based on the presence of a token.
   */
  isLoggedIn(): boolean {
    return !!localStorage.getItem('jwt_token');
  }
}
