<style>

</style>
<!-- dashboard.component.html -->
<div class="w-full h-full p-4 md:p-8 flex flex-col gap-4 overflow-y-scroll">
    <!-- Header and Time Filter -->
    <div class="flex items-center justify-between gap-4">
        <h2 class="text-xl font-semibold text-gray-700">Dashboard</h2>
        
    </div>

    <div class="mt-2">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Total Occurrences Card -->
            <div class="bg-white p-4 rounded-lg flex flex-col gap-3">
                <p class="text-sm text-gray-700">Total occurences</p>
                <div class="flex items-center justify-between">
                    <span
                        class="font-semibold text-gray-700 text-3xl lg:text-4xl">{{dashboardData?.total?.occurrences?.total}}</span>
                    <div class="bg-primary-50 p-2 px-3 rounded-lg">
                        <i class="pi pi-exclamation-triangle text-xl lg:text-2xl text-primary-500"></i>
                    </div>
                </div>
                <div>
                    <p class="text-green-500 text-xs font-light">
                        @if (dashboardData?.total?.occurrences?.increase === 0) {
                        Same as last month
                        } @else {
                        +{{dashboardData?.total?.occurrences?.increase}}% from last month
                        }
                    </p>
                </div>
            </div>

            <!-- Total Reports Card -->
            <div class="bg-white p-4 rounded-lg flex flex-col gap-3">
                <p class="text-sm text-gray-700">Total Reports</p>
                <div class="flex items-center justify-between">
                    <span
                        class="font-semibold text-gray-700 text-3xl lg:text-4xl">{{dashboardData?.total?.reports?.total}}</span>
                    <div class="bg-primary-50 p-2 px-3 rounded-lg">
                        <i class="pi pi-file text-xl lg:text-2xl text-primary-500"></i>
                    </div>
                </div>
                <div></div>
            </div>

            <!-- Incidents Card -->
            <div class="bg-white p-4 rounded-lg flex flex-col gap-3">
                <p class="text-sm text-gray-700">Incidents</p>
                <div class="flex items-center justify-between">
                    <span
                        class="font-semibold text-gray-700 text-3xl lg:text-4xl">{{dashboardData?.total?.incidents?.total}}</span>
                    <div class="bg-red-100 p-2 px-3 rounded-lg">
                        <i class="pi pi-exclamation-circle text-xl lg:text-2xl text-red-400"></i>
                    </div>
                </div>
                <div>
                    <p class="text-green-500 text-xs font-light">
                        @if (dashboardData?.total?.incidents?.increase === 0) {
                        Same as last month
                        } @else {
                        +{{dashboardData?.total?.incidents?.increase}}% from last month
                        }
                    </p>
                </div>
            </div>

            <!-- Accidents Card -->
            <div class="bg-white p-4 rounded-lg flex flex-col gap-3">
                <p class="text-sm text-gray-700">Accidents</p>
                <div class="flex items-center justify-between">
                    <span
                        class="font-semibold text-gray-700 text-3xl lg:text-4xl">{{dashboardData?.total?.accidents?.total}}</span>
                    <div class="bg-red-100 p-2 px-3 rounded-lg">
                        <i class="pi pi-exclamation-triangle text-xl lg:text-2xl text-red-400"></i>
                    </div>
                </div>
                <div>
                    <p class="text-green-500 text-xs font-light">
                        @if (dashboardData?.total?.accidents?.increase === 0) {
                        Same as last month
                        } @else {
                        +{{dashboardData?.total?.accidents?.increase}}% from last month
                        }
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="flex justify-between mt-4">
        <div class=""></div>
        <div class="flex gap-2 items-center">
            <p-dropdown [options]="yearOptions" [(ngModel)]="selectedYear" (onChange)="onYearChange()"
                placeholder="Select Year" class="w-[150px]"></p-dropdown>
            <p-dropdown [options]="monthOptions" [(ngModel)]="selectedMonth" (onChange)="updateCharts()"
                placeholder="Select Month" class="w-[150px]"></p-dropdown>
        </div>
    </div>

    <!-- First Section: Pie Chart and Type Bar Chart -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-2 flex-1">
        <!-- Pie Chart -->
        <div class="bg-white p-4 rounded-lg flex flex-col h-full">
            <h3 class="font-semibold mb-4">Occurrence Class Distribution</h3>
            <div class="flex-1">
                <p-chart type="pie" [data]="pieData" [options]="chartOptions"></p-chart>
            </div>
        </div>

        <!-- Type Bar Chart -->
        <div class="bg-white p-4 rounded-lg flex flex-col h-full">
            <h3 class="font-semibold mb-4">Occurrences by Class</h3>
            <div class="flex-1">
                <p-chart type="bar" [data]="typeBarData" [options]="chartOptions" [height]="'300px'"></p-chart>
            </div>
        </div>
    </div>

    <!-- Second Section: Category Bar Chart -->
    <div class="mt-6 bg-white p-4 rounded-lg flex flex-col">
        <h3 class="font-semibold mb-4">Occurrences by Category</h3>
        <div class="flex-1">
            <p-chart type="bar" [data]="categoryBarData" [options]="categoryChartOptions" [height]="'300px'"></p-chart>
        </div>
    </div>
</div>