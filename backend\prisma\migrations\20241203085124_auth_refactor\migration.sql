/*
  Warnings:

  - A unique constraint covering the columns `[telephone]` on the table `User` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `sourceBrowser` to the `Logs` table without a default value. This is not possible if the table is not empty.
  - Added the required column `sourceIpAddress` to the `Logs` table without a default value. This is not possible if the table is not empty.
  - Added the required column `sourceOS` to the `Logs` table without a default value. This is not possible if the table is not empty.
  - Added the required column `sourceUrl` to the `Logs` table without a default value. This is not possible if the table is not empty.
  - Added the required column `passwordExpiryDate` to the `User` table without a default value. This is not possible if the table is not empty.
  - Added the required column `telephone` to the `User` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "Logs" ADD COLUMN     "sourceBrowser" TEXT NOT NULL,
ADD COLUMN     "sourceIpAddress" TEXT NOT NULL,
ADD COLUMN     "sourceOS" TEXT NOT NULL,
ADD COLUMN     "sourceUrl" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "lastLoginAt" TIMESTAMP(3),
ADD COLUMN     "multiFactorEnabled" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "passwordExpiryDate" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "profilePicture" TEXT,
ADD COLUMN     "resetTokenExpiry" TIMESTAMP(3),
ADD COLUMN     "telephone" TEXT NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "User_telephone_key" ON "User"("telephone");
