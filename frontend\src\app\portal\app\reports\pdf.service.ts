import { Injectable } from '@angular/core';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import { AddressedTo, Report } from '../../../util/@types';
import { logo } from './img';
import QRCode from 'qrcode'
import { AxiosService } from '../../../util/axios/axios.service';
import { SignatureService } from '../../admin/signature/signature.service';
import { environment } from '../../../../environments/environment';
import { bgImage } from './img/bg';

pdfMake.vfs = pdfFonts.vfs;

@Injectable({
  providedIn: 'root',
})
export class ReportPdfService {

  constructor(
    private axiosService: AxiosService,
    private signatureService: SignatureService
  ) { }

  async generateReportPdf(report: Report) {
    const documentDefinition = await this.getDocumentDefinition(report);
    const date = new Date()
    const docName = `AAID ${report.referenceNumber}`
    pdfMake.createPdf(documentDefinition).download(docName);
  }

  async generatePreview(report: Report) {
    const documentDefinition = await this.getDemoDocumentDefinition(report);
    pdfMake.createPdf(documentDefinition).open();
  }

  private async getDocumentDefinition(report: Report): Promise<any> {
    const footer = await this.getFooter(report);

    return {
      content: [
        this.getHeader(),
        this.getTitleSection(report),
        this.getNotificationSection(report),
        this.details(report),
        footer,
      ],
      background: {
        image: bgImage, 
        // width: 0,
        height: 842,
        // opacity: 0.5,
        x: 0,
        y: 0
      },
      footer: (currentPage: any, pageCount: any) => ({
        text: `Page ${currentPage} of ${pageCount}`,
        style: 'xs',
        alignment: 'center',
        margin: [0, 0, 0, 10],
      }),
      styles: this.getStyles(),
    };
  }

  private async getDemoDocumentDefinition(report: Report): Promise<any> {
    const footer = await this.getFooter(report);

    return {
      content: [
        this.getHeader(),
        this.getTitleSection(report),
        this.getNotificationSection(report),
        this.details(report),
        // footer,
      ],
      background: {
        image: bgImage, 
        // width: 0,
        height: 842,
        // opacity: 0.5,
        x: 0,
        y: 0
      },
      footer: (currentPage: any, pageCount: any) => ({
        text: `Page ${currentPage} of ${pageCount}`,
        style: 'xs',
        alignment: 'center',
        margin: [0, 0, 0, 10],
      }),
      styles: this.getStyles(),
    };
  }

  private getHeader() {
    return [
      {
        text: 'REPUBLIC OF RWANDA',
        style: 'header',
        alignment: 'center',
        margin: [0, 5, 0, 5],
      },
      {
        image: logo,
        width: 50,
        alignment: 'center',
        margin: [0, 0, 0, 0],
      },
      {
        text: 'MINISTRY OF INFRASTRUCTURE',
        style: 'subheader',
        alignment: 'center',
        margin: [0, 5, 0, 5],
      },
      {
        text: 'P.O. BOX 24 KIGALI\nEmail: <EMAIL>',
        style: 'tableheader',
        alignment: 'center',
        margin: [0, 0, 0, 5],
      },
      {
        text: 'Aviation Accident and Incident Investigation Directorate (AAID)',
        style: 'subheader',
        alignment: 'center',
        margin: [0, 5, 0, 5],
      },
      {
        canvas: [
          {
            type: 'line',
            x1: 0,
            y1: 0,
            x2: 500,
            y2: 0,
            lineWidth: 0.5,
            lineColor: '#5555',
          },
        ],
        margin: [0, 0, 0, 10],
      },
    ];
  }

  private getTitleSection(report: Report) {
    return [
      {
        text: `ACCIDENT/INCIDENT NOTIFICATION FORM: ${report.referenceNumber}`,
        style: 'header',
        alignment: 'center',
        margin: [0, 0, 0, 15],
      },
      {
        text: 'Disclaimer: The following information is preliminary and subject to change; it is not for public release without the permission of Aviation Accident and Incident Investigation Directorate (AAID).',
        style: 'disclaimer',
        margin: [0, 0, 0, 15],
      },
    ];
  }

  private getNotificationSection(report: Report) {
    return [
      {
        text: 'Notification addressed to:',
        style: 'subheader',
        alignment: 'left',
        margin: [0, 0, 0, 10],
      },
      ...this.generateCheckboxes(report.addressedTo, report),
    ];
  }

  private generateCheckboxes(addressedToList: AddressedTo[], report: Report): any[] {
    const checkboxes: any[] = [];
    const addressedToOptions = [
      { label: 'State of Registry', country: report.stateOfRegistry, value: AddressedTo.StateOfRegistry },
      { label: 'State of Design', country: report.stateOfDesign, value: AddressedTo.StateOfDesign },
      { label: 'State of Manufacturer', country: report.stateOfManufacturer, value: AddressedTo.StateOfManufacturer },
      { label: 'State of Operator', country: report.stateOfOperator, value: AddressedTo.StateOfOperator },
      { label: 'ICAO', value: AddressedTo.ICAO },
    ];

    addressedToOptions.forEach(option => {
      console.log(addressedToList)
      if (addressedToList.includes(option.value.replace(/\s+/g, '') as AddressedTo)) {
        checkboxes.push({
          text: `${option.label}${option.country ? ': ' + option.country : ''}`,
          margin: [0, 0, 0, 5],
          style: 'sm',
        });
      }
    });

    return checkboxes;
  }

  private details(report: Report) {
    return [
      {
        table: {
          widths: ['5%', '45%', '50%'],
          body: [
            [
              { text: '#', style: 'tableHeader', alignment: 'center' },
              { text: '# Description', style: 'tableHeader', },
              { text: 'Details', style: 'tableHeader', },
            ],
            [
              { text: 'a)', style: 'tableText', alignment: 'center' },
              {
                text: 'For accidents the identifying abbreviation ACCID, for serious incidents SINCID, for incident INCID;',
                style: 'tableText',
                alignment: 'left'
              },
              {
                text: report.occurrenceType,
                style: 'tableText',
                alignment: 'left'
              },
            ],
            [
              { text: 'b)', style: 'tableText', alignment: 'center' },
              {
                text: 'Manufacturer, model, nationality and registration marks, and serial number of the aircraft;',
                style: 'tableText',
                alignment: 'left'
              },
              {
                text: report.aircraftInfo
                  .map(
                    (aircraft) =>
                      `Manufacturer: ${aircraft.manufacturer}\nModel: ${aircraft.model}\nNationality: ${aircraft.nationality}\nRegistration Marks: ${aircraft.registrationMarks}\n`
                  )
                  .join('\n'),
                style: 'tableText',
                alignment: 'left',
              },
            ],
            [
              { text: 'c)', style: 'tableText', alignment: 'center' },
              {
                text: 'Name of owner, operator and hirer, if any, of the aircraft;',
                style: 'tableText',
                alignment: 'left'
              },
              {
                text: report.aircraftInfo
                  .map(
                    (aircraft) =>
                      `${report.aircraftInfo.length > 1 ? 'Aircraft Reg Mark: ' + aircraft.registrationMarks + '\n' : ''}Owner: ${aircraft.owner ?? 'N/A'}\nOperator: ${aircraft.operator ?? 'N/A'}\nHirer: ${aircraft.hirer ?? 'N/A'}\n`
                  )
                  .join('\n'),
                style: 'tableText',
                alignment: 'left',
              },
            ],
            [
              { text: 'd)', style: 'tableText', alignment: 'center' },
              {
                text: 'Qualification of the pilot-in-command, and nationality of crew and passengers;',
                style: 'tableText',
                alignment: 'left',
              },
              {
                text: report.aircraftInfo
                  .map((aircraft) => {
                    return `${report.aircraftInfo.length > 1 ? 'Aircraft Reg Mark: ' + aircraft.registrationMarks : ''}\nPilot Qualification: ${aircraft.pilotQualification ?? 'N/A'}\nCrew Nationality: ${aircraft.crewNationality ?? 'N/A'}\nPassenger Nationality: ${aircraft.passengerNationality ?? 'N/A'}\n\n`;
                  })
                  .join(''),
                style: 'tableText',
                alignment: 'left',
              },
            ],
            [
              { text: 'e)', style: 'tableText', alignment: 'center' },
              {
                text: 'Date and time (local time or UTC) of the accident or incident;',
                style: 'tableText',
                alignment: 'left'
              },
              {
                text: `Local Time: ${report.occurrenceTimeLocal ? new Date(report.occurrenceTimeLocal).toLocaleString() : ''}\nUTC Time: ${report.occurrenceTimeUTC ? new Date(report.occurrenceTimeUTC).toLocaleString() : ''}\n\n`,
                style: 'tableText',
                alignment: 'left'
              },
            ],
            [
              { text: 'f)', style: 'tableText', alignment: 'center' },
              {
                text: 'Last point of departure and point of intended landing of the aircraft;',
                style: 'tableText',
                alignment: 'left',
              },
              {
                text: report.aircraftInfo
                  .map(
                    (aircraft) =>
                      `${report.aircraftInfo.length > 1 ? 'Aircraft Reg Mark: ' + aircraft.registrationMarks + '\n' : ''} Last Point of Departure: ${aircraft.lastDeparturePoint ?? 'N/A'}\nPoint of Intended Landing: ${aircraft.intendedLandingPoint ?? 'N/A'}\n`
                  )
                  .join('\n'),
                style: 'tableText',
                alignment: 'left',
              },
            ],
            [
              { text: 'g)', style: 'tableText', alignment: 'center' },
              {
                text: 'Position of the aircraft with reference to some easily defined geographical point and latitude and longitude;',
                style: 'tableText',
                alignment: 'left',
              },
              {
                text: `Geographical Reference: ${report.position ?? 'N/A'}\nLatitude: ${report.latitude ?? 'N/A'}\nLongitude: ${report.longitude ?? 'N/A'}\n\n`
                ,
                style: 'tableText',
                alignment: 'left',
              },
            ],
            [
              { text: 'h)', style: 'tableText', alignment: 'center' },
              { text: 'Number of crew and passengers; aboard, killed, seriously injured, and others;', style: 'tableText', alignment: 'left' },
              {
                table: {
                  widths: ['31%', '23%', '23%', '23%'], // Added injured column
                  body: [
                    // Headers
                    [
                      { text: 'Category', bold: true, alignment: 'center' },
                      { text: 'Aboard', bold: true, alignment: 'center' },
                      { text: 'Killed', bold: true, alignment: 'center' },
                      { text: 'Injured', bold: true, alignment: 'center' },
                    ],
                    // Data rows
                    ...report.aircraftInfo.map(aircraft => {
                      const casualties = aircraft.casualties;
                      return [
                        { text: 'Crew', alignment: 'left' },
                        { text: casualties?.crew?.aboard ?? 'N/A', alignment: 'center' },
                        { text: casualties?.crew?.killed ?? 'N/A', alignment: 'center' },
                        { text: casualties?.crew?.injured ?? 'N/A', alignment: 'center' },
                      ];
                    }),
                    ...report.aircraftInfo.map(aircraft => {
                      const casualties = aircraft.casualties;
                      return [
                        { text: 'Passengers', alignment: 'left' },
                        { text: casualties?.passengers?.aboard ?? 'N/A', alignment: 'center' },
                        { text: casualties?.passengers?.killed ?? 'N/A', alignment: 'center' },
                        { text: casualties?.passengers?.injured ?? 'N/A', alignment: 'center' },
                      ];
                    }),
                    [
                      { text: 'Others', alignment: 'left' },
                      { text: 'N/A', alignment: 'center' },
                      { text: report.groundPeoplePerished ?? 'N/A', alignment: 'center' },
                      { text: report.groundPeopleInjured ?? 'N/A', alignment: 'center' },
                    ],
                  ],
                },
                layout: 'lightHorizontal',
              },
            ],
            [
              { text: 'i)', style: 'tableText', alignment: 'center' },
              {
                text: 'Description of the accident or incident and the extent of damage to the aircraft so far as is known;',
                style: 'tableText',
                alignment: 'left',
              },
              {
                text: `Description: ${report.occurrenceDescription ?? 'N/A'}\nExtent of Damage: ${report.damageExtent ?? 'N/A'}\n`,
                style: 'tableText',
                alignment: 'left',
              },
            ],
            [
              { text: 'j)', style: 'tableText', alignment: 'center' },
              {
                text: 'An indication to what extent the investigation will be conducted or is proposed to be delegated by the State of Occurrence;',
                style: 'tableText',
                alignment: 'left',
              },
              {
                text: `Investigation Extent: ${report.investigationExtent ?? 'N/A'}\nInvestigation Delegation: ${report.investigationDelegation ?? 'N/A'}`,
                style: 'tableText',
                alignment: 'left',
              },
            ],
            [
              { text: 'k)', style: 'tableText', alignment: 'center' },
              {
                text: 'Physical characteristics of the accident or incident area, as well as an indication of access difficulties or special requirements to reach the site;',
                style: 'tableText',
                alignment: 'left',
              },
              {
                text: `Area Characteristics: ${report.areaCharacteristics ?? 'N/A'}\nAccess Requirements: ${report.accessRequirements ?? 'N/A'}`,
                style: 'tableText',
                alignment: 'left',
              },
            ],
            [
              { text: 'l)', style: 'tableText', alignment: 'center' },

              {
                text: 'Identification of the originating authority and means to contact the investigator-in-charge and the accident investigation authority of the State of Occurrence at any time;',
                style: 'tableText',
                alignment: 'left',
              },
              {
                table: {
                  widths: ['40%', '60%'],
                  body: [
                    [
                      { text: 'Originating Authority:', style: 'sub', alignment: 'left' },
                      { text: `Name: ${report.originatingAuthority ?? 'AAID-RWANDA'} \n\nEmail: <EMAIL>`, style: 'tableText', alignment: 'left' },
                    ],
                    [
                      { text: 'Investigator in Charge:', style: 'sub', alignment: 'left' },
                      {
                        text: `Name: ${report.investigatorName ?? 'N/A'}\nMobile: ${report.investigatorMobile ?? 'N/A'}\nEmail: ${report.investigatorEmail?.toLocaleLowerCase() ?? 'N/A'}`,
                        style: 'tableText',
                        alignment: 'left'
                      },
                    ],
                  ],
                },
                layout: 'lightHorizontal',
              },
            ],
            [
              { text: 'm)', style: 'tableText', alignment: 'center' },

              {
                text: 'Presence and description of dangerous goods on board the aircraft;',
                style: 'tableText',
                alignment: 'left',
              },
              {
                text: `Dangerous Goods Present: ${report.dangerousGoodsPresent ? 'Yes' : 'No'}\n${report.dangerousGoodsPresent && report.dangerousGoodsDescription? `Description: ${report.dangerousGoodsDescription}`
                    : ''}`,
                style: 'tableText',
                alignment: 'left',
              },
            ]
          ],
        },
        layout: 'lightHorizontal',
        margin: [0, 10, 0, 0],
      },
    ];
  }

  private async getFooter(report: Report) {
    try {
      const qrCodeData = await this.getQRcode(
        `${environment.FE_URL}/report-verification?refNo=${report.referenceNumber}&id=${report.id}`
      );
      const signature = await this.getSignature();
      const signatureImg =
        signature.length > 0 ? await this.fetchImageAsBase64(signature[0].imageUrl) : null;

      return [
        {
          table: {
            widths: ['70%', '30%'],
            body: [
              [
                {
                  stack: [
                    signatureImg
                      ? {
                        image: signatureImg,
                        width: 50,
                        margin: [0, 0, 0, 0],
                      }
                      : null,
                    {
                      text: `${signature[0]?.name ?? '. . . . . . . .'}`,
                      style: 'footer',
                      margin: [0, 0, 0, 2],
                    },
                    {
                      text: `AAID Chief Investigator`,
                      style: 'footer',
                      margin: [0, 0, 0, 0],
                    },
                  ].filter(Boolean),
                  alignment: 'left',
                },
                {
                  stack: [
                    {
                      image: qrCodeData,
                      width: 80,
                      margin: [0, 0, 0, 0],
                      alignment: 'right'
                    },
                    {
                      text: 'Scan this code for validity.',
                      style: 'xs',
                      // alignment: 'right',
                    },
                  ],
                  alignment: 'right',
                },
              ],
            ],
          },
          layout: 'noBorders', // Remove visible borders
          margin: [0, 20, 0, 0],
        },
        {
          canvas: [
            {
              type: 'line',
              x1: 0,
              y1: 0,
              x2: 500,
              y2: 0,
              lineWidth: 0.5,
              lineColor: '#4444',
            },
          ],
          margin: [0, 20, 0, 0],
        },
      ];
    } catch (error) {
      console.error('Error generating footer:', error);
      return [
        {
          text: 'AAID Chief Investigator: ',
          style: 'footer',
          alignment: 'left',
          margin: [0, 20, 0, 10],
        },
        {
          text: 'Signature: . . . . . . . . . . . . . ',
          style: 'footer',
          alignment: 'left',
          margin: [0, 0, 0, 10],
        },
      ];
    }
  }


  private async getSignature() {
    try {
      const response = await this.signatureService.getSignatures()
      return response.data
    } catch (error: any) {
      throw new Error(`Failed to signature: ${error.message}`);

    }
  }

  async fetchImageAsBase64(url: string): Promise<string> {
    try {
      const response = await this.axiosService.fileAxios.get(url, {
        responseType: 'blob',
      });

      const blob = response.data;

      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          resolve(reader.result as string);
        };
        reader.onerror = () => {
          reject(new Error('Failed to read blob as Base64'));
        };
        reader.readAsDataURL(blob);
      });
    } catch (error: any) {
      throw new Error(`Failed to fetch image: ${error.message}`);
    }
  }


  private async getQRcode(data: string): Promise<string> {
    try {
      return await QRCode.toDataURL(data, {
        width: 400,
      });
    } catch (err) {
      console.error('Error generating QR code:', err);
      throw err;
    }
  }

  private getStyles() {
    return {
      header: {
        fontSize: 14,
        bold: true,
        alignment: 'center',
      },
      subheader: {
        fontSize: 12,
        bold: true,
        alignment: 'center',
      },
      sm: {
        fontSize: 10,
        alignment: 'left',
        bold: true,
      },
      title: {
        fontSize: 14,
        bold: true,
        alignment: 'center',
      },
      disclaimer: {
        fontSize: 10,
        italics: true,
        alignment: 'center',
      },
      tableText: {
        fontSize: 10,
        // margin: [5, 2],
      },
      sub: {
        fontSize: 10,
        bold: true,
        margin: [5, 2],
        alignment: 'left',
      },
      tableHeader: {
        fontSize: 12,
        bold: true,
        margin: [5, 2],
        alignment: 'left',
      },
      footer: {
        fontSize: 10,
        italics: true,
      },
      horizontalLine: {
        border: [false, false, false, true],
        margin: [0, 20, 0, 0],
      },
      xs: {
        fontSize: 8,
        italics: true,
      }
    };
  }
}
