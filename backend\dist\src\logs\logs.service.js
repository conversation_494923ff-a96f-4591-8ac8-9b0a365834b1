"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LogsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const _types_1 = require("../utils/@types");
let LogsService = class LogsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getLogs(filters, sortBy, page = 1, pageSize = 10) {
        const skip = (page - 1) * pageSize;
        const where = {};
        if (filters) {
            if (filters.action) {
                where.action = { contains: filters.action };
            }
            if (filters.sourceUrl) {
                where.sourceUrl = { contains: filters.sourceUrl };
            }
            if (filters.sourceIpAddress) {
                where.sourceIpAddress = { contains: filters.sourceIpAddress };
            }
            if (filters.sourceOS) {
                where.sourceOS = { contains: filters.sourceOS };
            }
            if (filters.sourceBrowser) {
                where.sourceBrowser = { contains: filters.sourceBrowser };
            }
            if (filters.url) {
                where.url = { contains: filters.url };
            }
            if (filters.method) {
                where.method = filters.method;
            }
            if (filters.userId) {
                where.userId = filters.userId;
            }
        }
        const logs = await this.prisma.logs.findMany({
            where,
            orderBy: sortBy ? { [sortBy]: 'desc' } : { createdAt: 'desc' },
            skip,
            take: pageSize,
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true
                    }
                }
            }
        });
        const totalLogs = await this.prisma.logs.count({ where });
        return new _types_1.ApiResponse(true, 'Logs retrieved successfully', {
            data: logs,
            total: totalLogs,
            page,
            pageSize,
            totalPages: Math.ceil(totalLogs / pageSize)
        });
    }
    async search(query, page = 1, pageSize = 10) {
        const results = await this.prisma.logs.findMany({
            where: {
                OR: [
                    { action: { contains: query, mode: 'insensitive' } },
                    { sourceUrl: { contains: query, mode: 'insensitive' } },
                    { sourceIpAddress: { contains: query, mode: 'insensitive' } },
                    { sourceOS: { contains: query, mode: 'insensitive' } },
                    { sourceBrowser: { contains: query, mode: 'insensitive' } },
                    { url: { contains: query, mode: 'insensitive' } },
                    {
                        user: {
                            name: { contains: query, mode: 'insensitive' },
                            email: { contains: query, mode: 'insensitive' },
                            telephone: { contains: query, mode: 'insensitive' }
                        }
                    }
                ]
            },
            include: {
                user: {
                    select: {
                        email: true,
                        name: true,
                        telephone: true
                    }
                }
            }
        });
        return new _types_1.ApiResponse(true, 'Logs retrieved successfully', {
            data: results,
            total: results.length,
            page,
            pageSize,
            totalPages: Math.ceil(results.length / pageSize)
        });
    }
    async getUserLogs(userId, sortBy, page = 1, pageSize = 10) {
        return this.getLogs({ userId }, sortBy, page, pageSize);
    }
};
exports.LogsService = LogsService;
exports.LogsService = LogsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], LogsService);
//# sourceMappingURL=logs.service.js.map