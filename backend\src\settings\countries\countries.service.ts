import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';

@Injectable()
export class CountriesService {
    constructor(
        private prisma: PrismaService
    ){}

    async getCountries(){
        try {
            const countries = await this.prisma.countriesInfo.findMany()
            return countries
        } catch (error) {
            throw new InternalServerErrorException()
        }
    }
}
