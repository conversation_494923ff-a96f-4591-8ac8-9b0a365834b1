import { AddressedTo, ReportOccurrenceType } from "@prisma/client";
export declare class AircraftDto {
    manufacturer?: string;
    model?: string;
    nationality?: string;
    registrationMarks?: string;
    serialNumber?: string;
    owner?: string;
    operator?: string;
    hirer?: string;
    pilotQualification?: string;
    crewNationality?: string;
    passengerNationality?: string;
    lastDeparturePoint?: string;
    intendedLandingPoint?: string;
    casualties?: {
        crew: {
            aboard: number;
            killed: number;
            injured: number;
        };
        passengers: {
            aboard: number;
            killed: number;
            injured: number;
        };
    };
}
export declare class CreateReportDto {
    addressedTo: AddressedTo[];
    referenceNumber: string;
    stateOfRegistry?: string;
    stateOfDesign?: string;
    stateOfManufacturer?: string;
    stateOfOperator?: string;
    occurrenceType: ReportOccurrenceType;
    occurrenceDate?: string;
    occurrenceTimeUTC: string;
    occurrenceTimeLocal?: string;
    position?: string;
    latitude?: string;
    longitude?: string;
    occurrenceDescription: string;
    damageExtent: string;
    dangerousGoodsPresent: boolean;
    dangerousGoodsDescription?: string;
    aircraftInfo?: AircraftDto[];
    investigationExtent?: string;
    investigationDelegation?: string;
    areaCharacteristics?: string;
    accessRequirements?: string;
    originatingAuthority?: string;
    investigatorName: string;
    investigatorMobile: string;
    investigatorEmail: string;
    groundPeopleInjured?: number;
    groundPeoplePerished?: number;
}
declare const UpdateReportDto_base: import("@nestjs/common").Type<Partial<CreateReportDto>>;
export declare class UpdateReportDto extends UpdateReportDto_base {
}
export declare class ApproveDTO {
    comment?: string;
}
export {};
