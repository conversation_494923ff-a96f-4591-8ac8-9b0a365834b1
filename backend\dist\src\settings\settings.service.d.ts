import { PrismaService } from 'src/prisma/prisma.service';
import { ApiResponse } from 'src/utils/@types';
import { CreateContactInfoDto, CreateOccurrenceCategoryDto, UpdateContactInfoDto, UpdateOccurrenceCategoryDto } from './dto';
export declare class SettingsService {
    private prisma;
    constructor(prisma: PrismaService);
    createContactInfo(createDto: CreateContactInfoDto): Promise<ApiResponse<any>>;
    getAllContactInfo(): Promise<ApiResponse<any>>;
    getContactInfoById(id: string): Promise<ApiResponse<any>>;
    updateContactInfo(id: string, updateDto: UpdateContactInfoDto): Promise<ApiResponse<any>>;
    deleteContactInfo(id: string): Promise<ApiResponse<any>>;
    createOccurrenceCategory(createDto: CreateOccurrenceCategoryDto): Promise<ApiResponse<any>>;
    getAllOccurrenceCategories(): Promise<ApiResponse<any>>;
    getOccurrenceCategoryById(id: string): Promise<ApiResponse<any>>;
    updateOccurrenceCategory(id: string, updateDto: UpdateOccurrenceCategoryDto): Promise<ApiResponse<any>>;
    deleteOccurrenceCategory(id: string): Promise<ApiResponse<any>>;
}
