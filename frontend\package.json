{"name": "aaid-notification-system", "version": "1.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "start:prod": "ng serve --configuration production", "build:prod": "ng build --configuration production"}, "private": true, "dependencies": {"@angular/animations": "^18.2.0", "@angular/common": "^18.2.0", "@angular/compiler": "^18.2.0", "@angular/core": "^18.2.0", "@angular/forms": "^18.2.0", "@angular/platform-browser": "^18.2.0", "@angular/platform-browser-dynamic": "^18.2.0", "@angular/router": "^18.2.0", "axios": "^1.7.7", "chart.js": "^4.4.6", "jwt-decode": "^4.0.0", "ng2-pdf-viewer": "^10.4.0", "pdfmake": "^0.2.18", "primeicons": "^7.0.0", "primeng": "^17.18.12", "qrcode": "^1.5.4", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.12", "@angular/cli": "^18.2.12", "@angular/compiler-cli": "^18.2.0", "@types/jasmine": "~5.1.0", "@types/pdfmake": "^0.2.11", "@types/qrcode": "^1.5.5", "autoprefixer": "^10.4.20", "jasmine-core": "~5.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.15", "typescript": "~5.5.2"}}