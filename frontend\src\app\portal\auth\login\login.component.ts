import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink, RouterModule } from '@angular/router';
import { MessageService } from 'primeng/api';
import { AuthService } from '../auth.service';
import { ToastModule } from 'primeng/toast';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterLink,
    ToastModule,
    RouterModule
  ],
  templateUrl: './login.component.html',
  providers: [MessageService],
})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;
  showPassword = false;
  loading = false;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private messageService: MessageService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.loginForm = this.fb.group({
      identifier: ['', [Validators.required, Validators.pattern(
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$|^\+?[1-9]\d{1,14}$/
      )]],
      password: ['', [Validators.required, Validators.minLength(6)]],
    });
  }
  ngOnInit(): void {
    console.log(this.authService.isLoggedIn)
    //  if(this.authService.isLoggedIn()){
    //   this.router.navigate([
    //     this.authService.getRole() === 'ADMIN'? 'admin' : 'portal'
    //   ]);
    //  }
  }

  togglePassword(): void {
    this.showPassword = !this.showPassword;
  }

  async onSubmit(): Promise<void> {
    if (this.loginForm.valid) {
      this.loading = true;
      const { identifier, password } = this.loginForm.value;

      const returnUrl = this.route.snapshot.queryParamMap.get('returnUrl')
      console.log(returnUrl)

      try {
        const resp = await this.authService.login(identifier, password);

        if (resp.message === '2FA REQUIRED') {
          console.log(resp.data)
          this.router.navigate(['/portal/auth/login/verify'],
            { queryParams: { id: resp.data.user.id, returnUrl: returnUrl } })
          return
        }
        const userInfo = this.authService.getUserInfo();
        this.messageService.add({
          severity: 'success',
          summary: 'Login Successful',
          detail: `Welcome, ${userInfo?.name}!`,
        });


        if (returnUrl) {
          this.router.navigate([returnUrl])
        } else {
          this.router.navigate([
            this.authService.getRole() === 'ADMIN' ? 'admin' : 
            this.authService.getRole() === 'INVESTIGATOR' ? 'portal' : 'dg'
          ]);
        }

      } catch (error: any) {
        console.log(error.response.data.message)
        const errorMessage = error.response.data.message || error.response.data.message[0] || error.message
        this.messageService.add({
          severity: 'error',
          summary: 'Login Failed',
          detail: errorMessage,
        });
      } finally {
        this.loading = false;
      }
    } else {
      Object.keys(this.loginForm.controls).forEach((key) => {
        const control = this.loginForm.get(key);
        if (control?.invalid) {
          control.markAsTouched();
        }
      });
      this.messageService.add({
        severity: 'warn',
        summary: 'Form Incomplete',
        detail: 'Please fill in all required fields.',
      });
    }
  }

}
