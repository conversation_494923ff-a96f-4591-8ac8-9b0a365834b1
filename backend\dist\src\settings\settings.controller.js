"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const settings_service_1 = require("./settings.service");
const dto_1 = require("./dto");
const role_guard_1 = require("../utils/guards/role.guard");
const client_1 = require("@prisma/client");
const role_decorator_1 = require("../utils/decorators/role.decorator");
let SettingsController = class SettingsController {
    constructor(settingsService) {
        this.settingsService = settingsService;
    }
    async createContactInfo(createDto) {
        return this.settingsService.createContactInfo(createDto);
    }
    async getAllContactInfo() {
        return this.settingsService.getAllContactInfo();
    }
    async getContactInfoById(id) {
        return this.settingsService.getContactInfoById(id);
    }
    async updateContactInfo(id, updateDto) {
        return this.settingsService.updateContactInfo(id, updateDto);
    }
    async deleteContactInfo(id) {
        return this.settingsService.deleteContactInfo(id);
    }
    async createOccurrenceCategory(createDto) {
        return this.settingsService.createOccurrenceCategory(createDto);
    }
    async getAllOccurrenceCategories() {
        return this.settingsService.getAllOccurrenceCategories();
    }
    async getOccurrenceCategoryById(id) {
        return this.settingsService.getOccurrenceCategoryById(id);
    }
    async updateOccurrenceCategory(id, updateDto) {
        return this.settingsService.updateOccurrenceCategory(id, updateDto);
    }
    async deleteOccurrenceCategory(id) {
        return this.settingsService.deleteOccurrenceCategory(id);
    }
};
exports.SettingsController = SettingsController;
__decorate([
    (0, role_decorator_1.Roles)(client_1.Role.ADMIN),
    (0, common_1.Post)("contacts"),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateContactInfoDto]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "createContactInfo", null);
__decorate([
    (0, common_1.Get)("contacts"),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "getAllContactInfo", null);
__decorate([
    (0, common_1.Get)('contacts/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "getContactInfoById", null);
__decorate([
    (0, role_decorator_1.Roles)(client_1.Role.ADMIN),
    (0, common_1.Patch)('contacts/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateContactInfoDto]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "updateContactInfo", null);
__decorate([
    (0, role_decorator_1.Roles)(client_1.Role.ADMIN),
    (0, common_1.Delete)('contact/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "deleteContactInfo", null);
__decorate([
    (0, role_decorator_1.Roles)(client_1.Role.ADMIN),
    (0, common_1.Post)("category"),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateOccurrenceCategoryDto]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "createOccurrenceCategory", null);
__decorate([
    (0, common_1.Get)("category"),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "getAllOccurrenceCategories", null);
__decorate([
    (0, common_1.Get)('category/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "getOccurrenceCategoryById", null);
__decorate([
    (0, role_decorator_1.Roles)(client_1.Role.ADMIN),
    (0, common_1.Patch)('category/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateOccurrenceCategoryDto]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "updateOccurrenceCategory", null);
__decorate([
    (0, role_decorator_1.Roles)(client_1.Role.ADMIN),
    (0, common_1.Delete)('category/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "deleteOccurrenceCategory", null);
exports.SettingsController = SettingsController = __decorate([
    (0, swagger_1.ApiTags)("Admin Settings"),
    (0, common_1.UseGuards)(role_guard_1.RolesGuard),
    (0, common_1.Controller)('settings'),
    __metadata("design:paramtypes", [settings_service_1.SettingsService])
], SettingsController);
//# sourceMappingURL=settings.controller.js.map