-- CreateEnum
CREATE TYPE "MeteologicalCondition" AS ENUM ('IMC', 'VMC');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "FlightRules" AS ENUM ('IFR', 'VFR');

-- CreateEnum
CREATE TYPE "OperationType" AS ENUM ('SCHEDULED', 'CHARTER', 'TRAINING', 'PRIVATE', 'DEMONSTRATION', 'AEROBIC', 'AERIAL_TOUR', 'FIXED_WING_AIRCRAFT', 'HELICOPTER', 'BALLOON', 'DRONE', 'OTHER');

-- CreateEnum
CREATE TYPE "OccurrenceType" AS ENUM ('INCID', 'CINCID', 'ACCID');

-- Create<PERSON>num
CREATE TYPE "HTTP_Method" AS ENUM ('GET', 'POST', 'PUT', 'PATCH', 'DELETE');

-- CreateEnum
CREATE TYPE "NotificationType" AS ENUM ('ERROR', 'INFO', 'WARNING');

-- CreateEnum
CREATE TYPE "DocumentType" AS ENUM ('EVIDENCE', 'PHOTOGRAPHS', 'STATEMENTS', 'OTHER');

-- CreateTable
CREATE TABLE "Aircraft" (
    "id" TEXT NOT NULL,
    "model" TEXT NOT NULL,
    "manufacturer" TEXT NOT NULL,
    "operator" TEXT,
    "registrationMark" TEXT,
    "operatorNationality" TEXT,
    "intendedLandingDateTime" TIMESTAMP(3),
    "intendedLandingPoint" TEXT,
    "lastDeparturePoint" TEXT,
    "crewOnBoard" INTEGER,
    "crewInjured" INTEGER,
    "crewPerished" INTEGER,
    "passengersOnBoard" INTEGER,
    "passengersInjured" INTEGER,
    "passengersPerished" INTEGER,
    "occurrenceId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Aircraft_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Occurrence" (
    "id" TEXT NOT NULL,
    "referenceNumber" TEXT,
    "reporterName" TEXT NOT NULL,
    "reporterEmail" TEXT NOT NULL,
    "reporterPhone" TEXT NOT NULL,
    "pilotInCommandName" TEXT,
    "pilotInCommandEmail" TEXT,
    "pilotInCommandPhone" TEXT,
    "groundPeoplePerished" INTEGER,
    "groundPeopleInjured" INTEGER,
    "generalWeatherConditions" TEXT,
    "skyCoverage" TEXT,
    "meteologicalCondition" "MeteologicalCondition" NOT NULL,
    "flightRules" "FlightRules" NOT NULL,
    "occurrenceTime" TIMESTAMP(3) NOT NULL,
    "operationType" "OperationType" NOT NULL,
    "latitude" TEXT NOT NULL,
    "longitude" TEXT NOT NULL,
    "occurrenceLocation" TEXT NOT NULL,
    "dangerousGoodCarriedOnBoard" TEXT,
    "type" "OccurrenceType",
    "occurrenceCategory_id" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Occurrence_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OccurrenceCategory" (
    "id" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "OccurrenceCategory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Document" (
    "id" TEXT NOT NULL,
    "type" "DocumentType" NOT NULL,
    "url" TEXT NOT NULL,
    "occurenceId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Document_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SafetyRecommendation" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "occurenceId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SafetyRecommendation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Notification" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Notification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Logs" (
    "id" TEXT NOT NULL,
    "type" "NotificationType" NOT NULL,
    "url" TEXT NOT NULL,
    "method" "HTTP_Method" NOT NULL,
    "userId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ContactsInfo" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "desription" TEXT,
    "email" TEXT NOT NULL,
    "telephone" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ContactsInfo_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Occurrence_referenceNumber_key" ON "Occurrence"("referenceNumber");

-- CreateIndex
CREATE UNIQUE INDEX "ContactsInfo_email_key" ON "ContactsInfo"("email");

-- CreateIndex
CREATE UNIQUE INDEX "ContactsInfo_telephone_key" ON "ContactsInfo"("telephone");

-- AddForeignKey
ALTER TABLE "Aircraft" ADD CONSTRAINT "Aircraft_occurrenceId_fkey" FOREIGN KEY ("occurrenceId") REFERENCES "Occurrence"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Occurrence" ADD CONSTRAINT "Occurrence_occurrenceCategory_id_fkey" FOREIGN KEY ("occurrenceCategory_id") REFERENCES "OccurrenceCategory"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_occurenceId_fkey" FOREIGN KEY ("occurenceId") REFERENCES "Occurrence"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SafetyRecommendation" ADD CONSTRAINT "SafetyRecommendation_occurenceId_fkey" FOREIGN KEY ("occurenceId") REFERENCES "Occurrence"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Logs" ADD CONSTRAINT "Logs_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
