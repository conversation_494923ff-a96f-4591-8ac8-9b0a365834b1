import { Module } from '@nestjs/common';
import { SettingsService } from './settings.service';
import { SettingsController } from './settings.controller';
import { PrismaService } from 'src/prisma/prisma.service';
import { AircraftController } from './aircraft/aircraft.controller';
import { AircraftService } from './aircraft/aircraft.service';
import { SignatureController } from './signature/signature.controller';
import { SignatureService } from './signature/signature.service';
import { CountriesModule } from './countries/countries.module';
import { CountriesService } from './countries/countries.service';
import { CountriesController } from './countries/countries.controller';

@Module({
  providers: [SettingsService, AircraftService, SignatureService, CountriesService, PrismaService],
  controllers: [SettingsController, AircraftController, SignatureController, CountriesController],
})
export class SettingsModule {}
