"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const prisma_service_1 = require("../prisma/prisma.service");
const _types_1 = require("../utils/@types");
const dayjs = require("dayjs");
let DashboardService = class DashboardService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getAll() {
        const currentMonth = dayjs().startOf('month').toDate();
        const lastMonth = dayjs().subtract(1, 'month').startOf('month').toDate();
        const endOfLastMonth = dayjs(lastMonth).endOf('month').toDate();
        const occurrences = await this.prisma.occurrence.count({});
        const lastMonthOccurrences = await this.prisma.occurrence.count({
            where: {
                createdAt: {
                    gte: lastMonth,
                    lte: endOfLastMonth,
                },
            },
        });
        const incidents = await this.prisma.occurrence.count({
            where: {
                type: client_1.OccurrenceType.INCIDENT,
            },
        });
        const lastMonthIncidents = await this.prisma.occurrence.count({
            where: {
                type: client_1.OccurrenceType.INCIDENT,
                createdAt: {
                    gte: lastMonth,
                    lte: endOfLastMonth,
                },
            },
        });
        const seriousIncidents = await this.prisma.occurrence.count({
            where: {
                type: client_1.OccurrenceType.SERIOUS_INCIDENT,
            },
        });
        const lastMonthSeriousIncidents = await this.prisma.occurrence.count({
            where: {
                type: client_1.OccurrenceType.SERIOUS_INCIDENT,
                createdAt: {
                    gte: lastMonth,
                    lte: endOfLastMonth,
                },
            },
        });
        const incidentsToBeInvestigated = await this.prisma.occurrence.count({
            where: {
                type: client_1.OccurrenceType.INCIDENT_TO_BE_INVESTIGATED,
            },
        });
        const lastMonthIncidentsToBeInvestigated = await this.prisma.occurrence.count({
            where: {
                type: client_1.OccurrenceType.INCIDENT_TO_BE_INVESTIGATED,
                createdAt: {
                    gte: lastMonth,
                    lte: endOfLastMonth,
                },
            },
        });
        const reports = await this.prisma.report.count();
        const lastMonthReports = await this.prisma.report.count({
            where: {
                createdAt: {
                    gte: lastMonth,
                    lte: endOfLastMonth,
                },
            },
        });
        const accidents = await this.prisma.occurrence.count({
            where: {
                OR: [
                    { type: client_1.OccurrenceType.ACCIDENT },
                    { type: client_1.OccurrenceType.MAJOR_ACCIDENT }
                ],
            },
        });
        const lastMonthAccidents = await this.prisma.occurrence.count({
            where: {
                OR: [
                    { type: client_1.OccurrenceType.ACCIDENT },
                    { type: client_1.OccurrenceType.MAJOR_ACCIDENT }
                ],
                createdAt: {
                    gte: lastMonth,
                    lte: endOfLastMonth,
                },
            },
        });
        const calculateIncrease = (current, previous) => {
            if (previous === 0)
                return current > 0 ? 100 : 0;
            return Number((((current - previous) / previous) * 100).toFixed(1));
        };
        const lastSevenMonths = Array.from({ length: 7 }, (_, i) => {
            const startOfMonth = dayjs().subtract(i, 'month').startOf('month').toDate();
            const endOfMonth = dayjs().subtract(i, 'month').endOf('month').toDate();
            return {
                month: dayjs(startOfMonth).format('MMMM'),
                data: {
                    incidents: this.prisma.occurrence.count({
                        where: {
                            type: client_1.OccurrenceType.INCIDENT,
                            createdAt: {
                                gte: startOfMonth,
                                lte: endOfMonth,
                            },
                        },
                    }),
                    seriousIncidents: this.prisma.occurrence.count({
                        where: {
                            type: client_1.OccurrenceType.SERIOUS_INCIDENT,
                            createdAt: {
                                gte: startOfMonth,
                                lte: endOfMonth,
                            },
                        },
                    }),
                    incidentsToBeInvestigated: this.prisma.occurrence.count({
                        where: {
                            type: client_1.OccurrenceType.INCIDENT_TO_BE_INVESTIGATED,
                            createdAt: {
                                gte: startOfMonth,
                                lte: endOfMonth,
                            },
                        },
                    }),
                    accidents: this.prisma.occurrence.count({
                        where: {
                            type: client_1.OccurrenceType.ACCIDENT,
                            createdAt: {
                                gte: startOfMonth,
                                lte: endOfMonth,
                            },
                        },
                    }),
                },
            };
        });
        const chartData = await Promise.all(lastSevenMonths.map(async ({ month, data }) => {
            return {
                [month]: {
                    incidents: await data.incidents,
                    seriousIncidents: await data.seriousIncidents,
                    incidentsToBeInvestigated: await data.incidentsToBeInvestigated,
                    accidents: await data.accidents,
                },
            };
        }));
        const _occurrences = await this.prisma.occurrence.findMany({
            include: {
                occurrenceCategory: true
            }
        });
        return new _types_1.ApiResponse(true, 'Dashboard data fetched successfully.', {
            total: {
                occurrences: {
                    total: occurrences,
                    increase: calculateIncrease(occurrences, lastMonthOccurrences),
                },
                incidents: {
                    total: incidents,
                    increase: calculateIncrease(incidents, lastMonthIncidents),
                },
                seriousIncidents: {
                    total: seriousIncidents,
                    increase: calculateIncrease(seriousIncidents, lastMonthSeriousIncidents),
                },
                incidentsToBeInvestigated: {
                    total: incidentsToBeInvestigated,
                    increase: calculateIncrease(incidentsToBeInvestigated, lastMonthIncidentsToBeInvestigated),
                },
                accidents: {
                    total: accidents,
                    increase: calculateIncrease(accidents, lastMonthAccidents),
                },
                reports: {
                    total: reports,
                    increase: calculateIncrease(reports, lastMonthReports),
                }
            },
            chart: Object.assign({}, ...chartData),
            occurrences: _occurrences
        });
    }
};
exports.DashboardService = DashboardService;
exports.DashboardService = DashboardService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], DashboardService);
//# sourceMappingURL=dashboard.service.js.map