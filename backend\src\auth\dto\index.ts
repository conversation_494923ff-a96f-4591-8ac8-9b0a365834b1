import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString, Matches } from 'class-validator';

export class LoginDTO {
  @ApiProperty({
    description: 'Identifier, either an email or a phone number',
    example: '<EMAIL> or +1234567890',
  })
  @IsNotEmpty()
  @IsString()
  @Matches(
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$|^\+?[1-9]\d{1,14}$/,
    { message: 'identifier must be a valid email or phone number' },
  )
  identifier: string;
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  password: string;
}

export class Verify2faDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsString()
  code: string;
}
