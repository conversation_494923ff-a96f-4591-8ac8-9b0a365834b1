import { Injectable } from "@angular/core";
import { AxiosService } from "../../../util/axios/axios.service";
import { PaginatedResponse, User } from "../../../util/@types";

@Injectable({
  providedIn: 'root'
})
export class UsersService {
  constructor(
    private axiosService: AxiosService,
  ) {}

  async getUsers(page: number = 1, limit: number = 10): Promise<PaginatedResponse<User>> {
    try {
      const response = await this.axiosService.axios.get('/users', {
        params: { page, limit }
      });
      return response.data.data as PaginatedResponse<User>;
    } catch (error) {
      throw error;
    }
  }

  async search(query: string, page: number, limit: number = 10): Promise<PaginatedResponse<User>>{
    try {
      const response = await this.axiosService.axios.get('/users/search', {
        params: { query, page, limit }
      });
      return response.data.data as PaginatedResponse<User>;
    } catch (error) {
      throw error;
    }
  }

  async getUser(id: string): Promise<User> {
    try {
      const response = await this.axiosService.axios.get(`/users/${id}`);
      return response.data.data as User;
    } catch (error) {
      throw error;
    }
  }

  async createUser(name: string, email: string, telephone: string, role: string, profilePicture?: string): Promise<User>{
    try {
      const response = await this.axiosService.axios.post('/users', {
        name, 
        email,
        telephone,
        role,
        profilePicture
      })
      return response.data.data as User
    } catch (error) {
      throw error
    }
  }

  async updateUser(id: string, name: string, email: string, telephone: string, role: string, profilePicture?: string): Promise<User>{
    try {
      const response = await this.axiosService.axios.patch(`/users/${id}`, {
        name,
        email,
        telephone, 
        profilePicture,
        role
      })
      return response.data.data as User
    } catch (error) {
      throw error
    }
  }

  async deleteUser(id: string): Promise<User> {
    try {
      const response = await this.axiosService.axios.delete(`/users/${id}`)
      return response.data.data as User
    } catch (error) {
      throw error
    }
  }

  async activate(id: string): Promise<User>{
    try {
      const response = await this.axiosService.axios.patch(`/users/activate-user/${id}`)
      return response.data.data as User
    } catch (error) {
      throw error
    }
  }

  async deactivate(id: string): Promise<User>{
    try {
      const response = await this.axiosService.axios.patch(`/users/deactivate-user/${id}`)
      return response.data.data as User
    } catch (error) {
      throw error
    }
  }

  async uploadProfilePicture(data: FormData): Promise<string>{
    try {
      const resp = await this.axiosService.fileAxios.post('/attachment/upload', data, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
      return resp.data.url
    } catch (error) {
      throw error
    }
  }
}
