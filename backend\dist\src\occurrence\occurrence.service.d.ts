import { PrismaService } from '../prisma/prisma.service';
import { CreateOccurrenceDto, UpdateOccurrenceDto } from './dto';
import { ApiResponse } from 'src/utils/@types';
import { MailService } from 'src/utils/mail/mail.service';
export declare class OccurrenceService {
    private readonly prisma;
    private mailService;
    constructor(prisma: PrismaService, mailService: MailService);
    createOccurrence(data: CreateOccurrenceDto): Promise<ApiResponse<any>>;
    getOccurrences(): Promise<ApiResponse<any>>;
    getOccurrencesById(id: string): Promise<ApiResponse<any>>;
    updateOccurrence(id: string, data: UpdateOccurrenceDto): Promise<{
        id: string;
        status: import("@prisma/client").$Enums.OccurrenceStatus | null;
        createdAt: Date;
        updatedAt: Date;
        type: import("@prisma/client").$Enums.OccurrenceType | null;
        reporterName: string;
        reporterEmail: string;
        reporterPhone: string;
        pilotInCommandName: string | null;
        pilotInCommandEmail: string | null;
        pilotInCommandPhone: string | null;
        groundPeoplePerished: number | null;
        groundPeopleInjured: number | null;
        generalWeatherConditions: string | null;
        skyCoverage: string | null;
        meteologicalCondition: import("@prisma/client").$Enums.MeteologicalCondition | null;
        flightRules: import("@prisma/client").$Enums.FlightRules | null;
        occurrenceTime: Date | null;
        operationType: import("@prisma/client").$Enums.OperationType | null;
        flightPhase: string | null;
        latitude: string | null;
        longitude: string | null;
        occurrenceLocation: string | null;
        dangerousGoodCarriedOnBoard: string | null;
        occurrenceCategory_id: string | null;
        referenceNumber: string | null;
    }>;
    deleteOccurrence(id: string): Promise<ApiResponse<any>>;
    generateReferenceNumber(id: string): Promise<ApiResponse<{
        id: string;
        status: import("@prisma/client").$Enums.OccurrenceStatus | null;
        createdAt: Date;
        updatedAt: Date;
        type: import("@prisma/client").$Enums.OccurrenceType | null;
        reporterName: string;
        reporterEmail: string;
        reporterPhone: string;
        pilotInCommandName: string | null;
        pilotInCommandEmail: string | null;
        pilotInCommandPhone: string | null;
        groundPeoplePerished: number | null;
        groundPeopleInjured: number | null;
        generalWeatherConditions: string | null;
        skyCoverage: string | null;
        meteologicalCondition: import("@prisma/client").$Enums.MeteologicalCondition | null;
        flightRules: import("@prisma/client").$Enums.FlightRules | null;
        occurrenceTime: Date | null;
        operationType: import("@prisma/client").$Enums.OperationType | null;
        flightPhase: string | null;
        latitude: string | null;
        longitude: string | null;
        occurrenceLocation: string | null;
        dangerousGoodCarriedOnBoard: string | null;
        occurrenceCategory_id: string | null;
        referenceNumber: string | null;
    }>>;
    searchOccurrence(query: string): Promise<ApiResponse<any>>;
}
