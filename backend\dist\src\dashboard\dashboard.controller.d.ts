import { DashboardService } from './dashboard.service';
export declare class DashboardController {
    private dashboardService;
    constructor(dashboardService: DashboardService);
    getAll(): Promise<import("../utils/@types").ApiResponse<{
        total: {
            occurrences: {
                total: number;
                increase: number;
            };
            incidents: {
                total: number;
                increase: number;
            };
            seriousIncidents: {
                total: number;
                increase: number;
            };
            incidentsToBeInvestigated: {
                total: number;
                increase: number;
            };
            accidents: {
                total: number;
                increase: number;
            };
            reports: {
                total: number;
                increase: number;
            };
        };
        chart: any;
        occurrences: ({
            occurrenceCategory: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                description: string;
                category: string;
                explanation: string | null;
            };
        } & {
            id: string;
            status: import("@prisma/client").$Enums.OccurrenceStatus | null;
            createdAt: Date;
            updatedAt: Date;
            type: import("@prisma/client").$Enums.OccurrenceType | null;
            reporterName: string;
            reporterEmail: string;
            reporterPhone: string;
            pilotInCommandName: string | null;
            pilotInCommandEmail: string | null;
            pilotInCommandPhone: string | null;
            groundPeoplePerished: number | null;
            groundPeopleInjured: number | null;
            generalWeatherConditions: string | null;
            skyCoverage: string | null;
            meteologicalCondition: import("@prisma/client").$Enums.MeteologicalCondition | null;
            flightRules: import("@prisma/client").$Enums.FlightRules | null;
            occurrenceTime: Date | null;
            operationType: import("@prisma/client").$Enums.OperationType | null;
            flightPhase: string | null;
            latitude: string | null;
            longitude: string | null;
            occurrenceLocation: string | null;
            dangerousGoodCarriedOnBoard: string | null;
            occurrenceCategory_id: string | null;
            referenceNumber: string | null;
        })[];
    }>>;
}
