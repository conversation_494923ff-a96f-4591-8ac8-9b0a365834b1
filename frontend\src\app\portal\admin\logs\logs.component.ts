import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MessageService, ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { Logs } from '../../../util/@types';
import { LogsService } from './logs.service';

@Component({
  selector: 'app-logs',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    TableModule,
    DialogModule,
    ToastModule,
    ConfirmDialogModule
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './logs.component.html'
})
export class LogsComponent {

  logs: Logs[] = [];
  totalLogs: number = 0;
  currentPage: number = 1;
  pageSize: number = 10;

  first: number = 0

  searchQuery: string = ''

  aircraftsModalVisible: boolean = false;
  isEditMode: boolean = false;

  currentLogs: Partial<Logs> = {};

  constructor(
    private logsService: LogsService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService
  ) { }

  ngOnInit(): void {
    this.loadLogs();
  }

  async loadLogs(page: number = 1, pageSize: number = 10) {
    try {
      const response = await this.logsService.getLogs(page, pageSize);
      this.logs = response.data;
      this.totalLogs = response.total;
    } catch (error) {
      this.handleError('Failed to load logs', error);
    }
  }

  async search(pageSize: number = 10) {
    try {
      if (this.searchQuery === '') {
        this.loadLogs();
        return;
      }
      const resp = await this.logsService.search(this.searchQuery, this.currentPage, pageSize);
      this.logs = resp.data;
      this.totalLogs = resp.total;
    } catch (error) {
      this.handleError('search failed', error);
    }
  }

  formatDate(date: Date | string){
    return new Date(date).toLocaleString()
  }

  onPageChange(event: any) {
    this.first = event.first;
    this.currentPage = Math.floor(event.first / event.rows) + 1; // Calculate page from first and rows
    this.pageSize = event.rows;

    console.log(this.pageSize)
    
    const safePageSize = Number(this.pageSize) || 10;
    
    if (this.searchQuery) {
      this.search(safePageSize);
    } else {
      this.loadLogs(this.currentPage, safePageSize);
    }
}

  private handleError(message: string, error: any) {
    console.error(error);
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: error.response.data.message || error.response.data.message[0] || error.message
    });
  }

}
