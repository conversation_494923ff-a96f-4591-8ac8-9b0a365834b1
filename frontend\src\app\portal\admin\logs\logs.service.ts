import { Injectable } from "@angular/core";
import { AircraftInfo, Logs, PaginatedResponse } from "../../../util/@types";
import { AxiosService } from "../../../util/axios/axios.service";

@Injectable({
  providedIn: 'root'
})
export class LogsService {
  constructor(private axiosService: AxiosService) { }

  async getLogs(page: number = 1, limit: number = 10): Promise<PaginatedResponse<Logs>> {
    try {
      const response = await this.axiosService.axios.get('/logs/', {
        params: { page, limit }
      });
      console.log(response.data)
      return response.data.data as PaginatedResponse<Logs>;
    } catch (error) {
      throw error;
    }
  }

  async getLog(id: string): Promise<Logs> {
    try {
      const response = await this.axiosService.axios.get(`/logs/${id}`);
      return response.data.data as Logs;
    } catch (error) {
      throw error;
    }
  }

  async search(query: string, page: number, limit: number = 10): Promise<PaginatedResponse<Logs>> {
    try {
      const response = await this.axiosService.axios.get('/logs/search', {
        params: { query, page, limit }
      });
      return response.data.data as PaginatedResponse<Logs>;
    } catch (error) {
      throw error;
    }
  }
}