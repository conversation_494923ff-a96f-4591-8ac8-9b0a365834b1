import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import { AuthService } from '../auth.service';

@Component({
  selector: 'app-reset-password',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ToastModule,
    RouterLink
  ],
  providers: [MessageService],
  templateUrl: './reset-password.component.html',
})
export class ResetPasswordComponent {
  resetForm: FormGroup;
  showPassword = false;
  loading = false;
  linkSent = false;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private messageService: MessageService,
    private router: Router
  ) {
    this.resetForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
    });
  }
  // ngOnInit(): void {
  //   console.log(this.authService.isLoggedIn)
  //  if(this.authService.isLoggedIn()){
  //   this.router.navigate(['/portal'])
  //  }
  // }

  async onSubmit(): Promise<void> {
    if (this.resetForm.valid) {
      this.loading = true;
      const { email } = this.resetForm.value;
  
      try {
        await this.authService.sendResetLink(email)
        this.linkSent = true

      } catch (error: any) {
        console.log(error.response.data.message)
        const errorMessage = error.response.data.message || error.response.data.message[0] || error.message 
        this.messageService.add({
          severity: 'error',
          summary: 'Something went wrong',
          detail: errorMessage,
        });
      } finally {
        this.loading = false;
      }
    } else {
      Object.keys(this.resetForm.controls).forEach((key) => {
        const control = this.resetForm.get(key);
        if (control?.invalid) {
          control.markAsTouched();
        }
      });
      this.messageService.add({
        severity: 'warn',
        summary: 'Form Incomplete',
        detail: 'Please fill in all required fields.',
      });
    }
  }
}
