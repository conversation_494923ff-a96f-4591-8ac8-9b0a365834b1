import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MessageService, ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { OccurrenceCategory } from '../../../util/@types';
import { CategoriesService } from './categories.service';

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    TableModule,
    DialogModule,
    ToastModule,
    ConfirmDialogModule
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './categories.component.html',
})
export class CategoriesComponent {

  categories: OccurrenceCategory[] = [];
  totalCategories: number = 0;
  currentPage: number = 1;
  pageSize: number = 10;

  categoriesModalVisible: boolean = false;
  isEditMode: boolean = false;

  currentCategory: Partial<OccurrenceCategory> = {};

  constructor(
    private categoriesService: CategoriesService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService
  ) { }

  ngOnInit(): void {
    this.loadCategories();
  }

  async loadCategories(page: number = 1) {
    try {
      const response = await this.categoriesService.getCategories(page, this.pageSize);
      this.categories = response.data;
      this.totalCategories = response.total;
    } catch (error) {
      this.handleError('Failed to load aicrafts', error);
    }
  }

  onPageChange(event: any) {
    this.currentPage = event.page + 1;
    this.pageSize = event.rows;
    this.loadCategories(this.currentPage);
  }

  openCategoriesModal(mode: 'create' | 'edit', category?: OccurrenceCategory) {
    this.isEditMode = mode === 'edit';
    this.currentCategory = this.isEditMode
      ? { ...category }
      : { category: '', description: '', explanation: ''};
    this.categoriesModalVisible = true;
  }

  async saveCategory() {
    try {
      if (this.isEditMode) {
        await this.categoriesService.updateCategory(
          this.currentCategory.id!,
          this.currentCategory.category!,
          this.currentCategory.description!,
          this.currentCategory.explanation
        );
        this.messageService.add({
          severity: 'success',
          summary: 'Category Updated',
          detail: 'Category has been successfully updated.'
        });
      } else {
        await this.categoriesService.createCategory(
          this.currentCategory.category!,
          this.currentCategory.description!,
          this.currentCategory.explanation
        );
        this.messageService.add({
          severity: 'success',
          summary: 'Category Created',
          detail: `New Category has been successfully created.`,
          life: 1000
        });
      }

      this.categoriesModalVisible = false;
      this.loadCategories(this.currentPage);
    } catch (error) {
      this.handleError('Failed to save Category', error);
    }
  }

  confirmDelete(category: OccurrenceCategory) {
    this.confirmationService.confirm({
      message: `Are you sure you want to delete Category ${category.category}?`,
      header: 'Confirm Deletion',
      icon: 'pi pi-info-circle',
      acceptIcon: "none",
      rejectIcon: "none",
      acceptButtonStyleClass: "p-button-danger p-button-text",
      rejectButtonStyleClass: "p-button-text p-button-text mr-4",
      accept: () => this.deleteCategory(category.id!)
    });
  }

  async deleteCategory(id: string) {
    try {
      await this.categoriesService.deleteCategory(id);
      this.messageService.add({
        severity: 'success',
        summary: 'Category Deleted',
        detail: 'Category has been successfully deleted.'
      });
      this.loadCategories(this.currentPage);
    } catch (error) {
      this.handleError('Failed to delete Category', error);
    }
  }

  private handleError(message: string, error: any) {
    console.error(error);
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: error.response.data.message || error.response.data.message[0] || error.message
    });
  }

}
