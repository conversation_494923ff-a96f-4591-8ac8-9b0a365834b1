import { PrismaService } from '../prisma/prisma.service';
import { Logs } from '@prisma/client';
import { ApiResponse } from 'src/utils/@types';
export declare class LogsService {
    private prisma;
    constructor(prisma: PrismaService);
    getLogs(filters?: Partial<Logs>, sortBy?: keyof Logs, page?: number, pageSize?: number): Promise<ApiResponse<any>>;
    search(query: string, page?: number, pageSize?: number): Promise<ApiResponse<{
        data: ({
            user: {
                name: string;
                email: string;
                telephone: string;
            };
        } & {
            id: string;
            createdAt: Date;
            url: string;
            action: string | null;
            sourceUrl: string;
            sourceIpAddress: string;
            sourceOS: string;
            sourceBrowser: string;
            method: import("@prisma/client").$Enums.HTTP_Method;
            userId: string | null;
        })[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>>;
    getUserLogs(userId: string, sortBy?: keyof Logs, page?: number, pageSize?: number): Promise<ApiResponse<any>>;
}
