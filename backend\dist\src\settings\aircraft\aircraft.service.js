"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AircraftService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const _types_1 = require("../../utils/@types");
let AircraftService = class AircraftService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(data) {
        try {
            const aircraft = await this.prisma.aircraftData.create({ data });
            return new _types_1.ApiResponse(true, 'Aircraft created successfully', aircraft);
        }
        catch (error) {
            throw new common_1.BadRequestException('Error creating aircraft');
        }
    }
    async findAll(page = 1, limit = 10) {
        const offset = (page - 1) * limit;
        const [aircrafts, total] = await Promise.all([
            this.prisma.aircraftData.findMany({
                skip: offset,
                take: limit,
            }),
            this.prisma.aircraftData.count(),
        ]);
        const totalPages = Math.ceil(total / limit);
        return new _types_1.ApiResponse(true, 'Aircraft data retrieved successfully', {
            data: aircrafts,
            page,
            totalPages,
            total,
        });
    }
    async findOne(id) {
        const aircraft = await this.prisma.aircraftData.findUnique({ where: { id } });
        if (!aircraft)
            throw new common_1.NotFoundException('Aircraft not found');
        return new _types_1.ApiResponse(true, 'Aircraft retrieved successfully', aircraft);
    }
    async update(id, data) {
        try {
            const aircraft = await this.prisma.aircraftData.findUnique({ where: { id } });
            if (!aircraft)
                throw new common_1.NotFoundException('Aircraft not found');
            const updatedAircraft = await this.prisma.aircraftData.update({
                where: { id },
                data,
            });
            return new _types_1.ApiResponse(true, 'Aircraft updated successfully', updatedAircraft);
        }
        catch (error) {
            throw new common_1.BadRequestException('Error updating aircraft');
        }
    }
    async remove(id) {
        try {
            const aircraft = await this.prisma.aircraftData.findUnique({ where: { id } });
            if (!aircraft)
                throw new common_1.NotFoundException('Aircraft not found');
            await this.prisma.aircraftData.delete({ where: { id } });
            return new _types_1.ApiResponse(true, 'Aircraft deleted successfully', null);
        }
        catch (error) {
            throw new common_1.BadRequestException('Error deleting aircraft');
        }
    }
    async search(query, page, limit = 10) {
        const results = await this.prisma.aircraftData.findMany({
            where: {
                OR: [
                    { modelFullName: { contains: query, mode: 'insensitive' } },
                    { manufacturerCode: { contains: query, mode: 'insensitive' } },
                    { description: { contains: query, mode: 'insensitive' } },
                    { designator: { contains: query, mode: 'insensitive' } },
                    { engineType: { contains: query, mode: 'insensitive' } },
                ]
            },
        });
        const total = await this.prisma.aircraftData.count({
            where: {
                OR: [
                    { modelFullName: { contains: query, mode: 'insensitive' } },
                    { manufacturerCode: { contains: query, mode: 'insensitive' } },
                    { description: { contains: query, mode: 'insensitive' } },
                    { designator: { contains: query, mode: 'insensitive' } },
                    { engineType: { contains: query, mode: 'insensitive' } },
                ]
            },
        });
        return new _types_1.ApiResponse(true, 'Search results retrieved successfully', {
            data: results,
            total,
            page
        });
    }
    async searchManufacturers(query) {
        const manufacturers = await this.prisma.aircraftData.findMany({
            where: {
                manufacturerCode: {
                    contains: query.toUpperCase(),
                    mode: 'insensitive'
                }
            },
            distinct: ['manufacturerCode'],
            select: { manufacturerCode: true },
            take: 10
        });
        return { data: manufacturers.map(m => m.manufacturerCode) };
    }
    async searchModels(query, manufacturer) {
        const models = await this.prisma.aircraftData.findMany({
            where: {
                AND: [
                    {
                        manufacturerCode: {
                            equals: manufacturer,
                            mode: 'insensitive'
                        }
                    },
                    {
                        OR: [
                            { modelFullName: { contains: query, mode: 'insensitive' } },
                            { designator: { contains: query.toUpperCase(), mode: 'insensitive' } }
                        ]
                    }
                ]
            },
            select: { modelFullName: true, designator: true },
            take: 10
        });
        return {
            data: models.map(m => m.modelFullName)
        };
    }
};
exports.AircraftService = AircraftService;
exports.AircraftService = AircraftService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], AircraftService);
//# sourceMappingURL=aircraft.service.js.map