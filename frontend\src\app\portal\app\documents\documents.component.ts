import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MessageService, ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { FileUploadModule } from 'primeng/fileupload';

import { DocumentsService } from './documents.service';
import { AxiosService } from '../../../util/axios/axios.service';
import { Document, DocumentType, Occurrence } from '../../../util/@types';
import { OccurrencesService } from '../occurrences/occurrences.service'; // Assuming you have this service
import { PdfViewerModule } from 'ng2-pdf-viewer';

@Component({
  selector: 'app-documents',
  standalone: true,
  imports: [
    CommonModule,
    HttpClientModule,
    FormsModule,
    ButtonModule,
    TableModule,
    DialogModule,
    ToastModule,
    ConfirmDialogModule,
    InputTextModule,
    DropdownModule,
    FileUploadModule,
    PdfViewerModule
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './documents.component.html'
})
export class DocumentsComponent implements OnInit {
  documents: Document[] = [];
  occurrences: Occurrence[] = [];

  filteredDocuments: Document[] = []
  searchQuery: string = ''
  
  occurrenceModalVisible = false;
  documentModalVisible = false;
  
  modalMode: 'create' | 'edit' = 'create';
  
  selectedOccurrence: Occurrence | null = null;
  currentDocument: Partial<Document> = {};
  
  documentTypes = [
    { label: 'Evidence', value: 'EVIDENCE' },
    { label: 'Photographs', value: 'PHOTOGRAPHS' },
    { label: 'Statements', value: 'STATEMENTS' },
    { label: 'Other', value: 'OTHER' }
  ];

  constructor(
    private documentsService: DocumentsService,
    private occurrencesService: OccurrencesService,
    private axiosService: AxiosService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService
  ) {}

  ngOnInit() {
    this.loadDocuments();
    this.loadOccurrences();
  }

  onSearch(){
    this.filteredDocuments = [...this.documents];

    // Apply search query filter
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase().trim();
      this.filteredDocuments = this.filteredDocuments.filter(doc => 
        doc.name?.toLowerCase().includes(query) ||
        doc.occurrence.referenceNumber?.toLowerCase().includes(query)
      );
    }

  }

  async loadDocuments() {
    try {
      const response = await this.documentsService.getDocuments();
      this.documents = response.data;
    } catch (error) {
      this.messageService.add({
        severity: 'error', 
        summary: 'Error', 
        detail: 'Failed to load documents'
      });
    }
  }

  async loadOccurrences() {
    try {
      const response = await this.occurrencesService.getOccurrences();
      this.occurrences = response.data;
      this.currentDocument = {}
    } catch (error) {
      this.messageService.add({
        severity: 'error', 
        summary: 'Error', 
        detail: 'Failed to load occurrences'
      });
    }
  }

  openOccurrenceSelectionModal() {
    this.selectedOccurrence = null;
    this.occurrenceModalVisible = true;
  }

  openDocumentsModal() {
    if (!this.selectedOccurrence) return;

    this.occurrenceModalVisible = false;
    this.documentModalVisible = true;
    this.modalMode = 'create';
    this.currentDocument = {
      occurrenceId: this.selectedOccurrence.id
    };
  }

  editDocument(document: Document) {
    this.modalMode = 'edit';
    this.selectedOccurrence = document.occurrence;
    this.currentDocument = { ...document };
    this.documentModalVisible = true;
  }

  closeDocumentModal() {
    this.documentModalVisible = false;
    this.currentDocument = {};
  }

  isDocumentValid(): boolean {
    return !!(this.currentDocument.name && 
              this.currentDocument.type && 
              this.currentDocument.url);
  }

  async onFileSelect(event: any) {
    const file = event.files[0];
    try {
      const data = new FormData()
      data.append('file', file)
      const uploadResponse = await this.axiosService.fileAxios.post('/attachment/upload', data, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
      this.currentDocument.url = uploadResponse.data.url;
    } catch (error) {
      console.log(error)
      this.messageService.add({
        severity: 'error', 
        summary: 'Upload Error', 
        detail: 'File upload failed'
      });
    }
  }

  getFileType(url: string): 'image' | 'doc' {
    const _arr = url.split('.');
    const extension = _arr.pop()?.toLowerCase() ?? '';
    
    if (['jpg', 'jpeg', 'png', 'webp', 'gif', 'bmp', 'svg'].includes(extension)) {
      return 'image';
    } else {
      return 'doc';
    }
  }
  

  async saveDocument() {
    try {
      if (this.modalMode === 'create') {
        await this.documentsService.createDocument({
          ...this.currentDocument as Document,
          occurrenceId: this.selectedOccurrence!.id
        });
        this.messageService.add({
          severity: 'success', 
          summary: 'Success', 
          detail: 'Document created'
        });
      } else {
        await this.documentsService.updateDocument(
          this.currentDocument.id!, 
          this.currentDocument
        );
        this.messageService.add({
          severity: 'success', 
          summary: 'Success', 
          detail: 'Document updated'
        });
      }
      
      this.documentModalVisible = false;
      this.loadDocuments();
    } catch (error) {
      this.messageService.add({
        severity: 'error', 
        summary: 'Error', 
        detail: this.modalMode === 'create' 
          ? 'Failed to create document' 
          : 'Failed to update document'
      });
    }
  }

  formatDate(date: Date | string | undefined): string {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  deleteDocument(id: string) {
    this.confirmationService.confirm({
      message: 'Are you sure you want to delete this document?',
      header: 'Delete Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'bg-red-400 py-2 px-4 mx-2 text-white',
      rejectButtonStyleClass: 'bg-gray-400 py-2 px-4 mx-2 text-white',
      accept: async () => {
        try {
          await this.documentsService.deleteDocument(id);
          this.messageService.add({
            severity: 'success', 
            summary: 'Success', 
            detail: 'Document deleted successfully'
          });
          this.loadDocuments();
        } catch (error) {
          this.messageService.add({
            severity: 'error', 
            summary: 'Error', 
            detail: 'Failed to delete document'
          });
        }
      }
    });
  }
}