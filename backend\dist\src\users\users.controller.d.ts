import { UsersService } from './users.service';
import { ActivateAccountDto, CreateUserDto, ResetPasswordConfirmDto, ResetPasswordRequestDto, SearchDto, UpdateUserDto } from './dto';
export declare class UsersController {
    private userService;
    constructor(userService: UsersService);
    createUser(createUserDto: CreateUserDto): Promise<import("../utils/@types").ApiResponse<any>>;
    activateAccount(activateAccountDto: ActivateAccountDto): Promise<import("../utils/@types").ApiResponse<any>>;
    getAllUsers(): Promise<import("../utils/@types").ApiResponse<any>>;
    getUser(id: string): Promise<import("../utils/@types").ApiResponse<{
        id: string;
        name: string;
        email: string;
        telephone: string;
        password: string | null;
        role: import("@prisma/client").$Enums.Role;
        profilePicture: string | null;
        multiFactorEnabled: boolean;
        multiFactorSecret: string | null;
        resetToken: string | null;
        resetTokenExpiry: Date | null;
        passwordExpiryDate: Date;
        status: import("@prisma/client").$Enums.AccountStatus;
        createdAt: Date;
        updatedAt: Date;
        lastLoginAt: Date | null;
    }>>;
    searchUsers(searchDto: SearchDto): Promise<import("../utils/@types").ApiResponse<{
        data: {
            id: string;
            name: string;
            email: string;
            telephone: string;
            password: string | null;
            role: import("@prisma/client").$Enums.Role;
            profilePicture: string | null;
            multiFactorEnabled: boolean;
            multiFactorSecret: string | null;
            resetToken: string | null;
            resetTokenExpiry: Date | null;
            passwordExpiryDate: Date;
            status: import("@prisma/client").$Enums.AccountStatus;
            createdAt: Date;
            updatedAt: Date;
            lastLoginAt: Date | null;
        }[];
        total: number;
        page: number;
    }>>;
    updateUser(id: string, updateUserDto: UpdateUserDto): Promise<import("../utils/@types").ApiResponse<any>>;
    resetPassword(resetPasswordRequestDto: ResetPasswordRequestDto): Promise<import("../utils/@types").ApiResponse<any>>;
    confirmResetPassword(resetPasswordConfirmDto: ResetPasswordConfirmDto): Promise<import("../utils/@types").ApiResponse<any>>;
    activateUser(id: string): Promise<import("../utils/@types").ApiResponse<any>>;
    deactivateUser(id: string): Promise<import("../utils/@types").ApiResponse<any>>;
    enableTwoFactor(req: any): Promise<import("../utils/@types").ApiResponse<{
        qrCode: string;
    }>>;
    verifyTwoFactorSetup(req: any, token: string): Promise<import("../utils/@types").ApiResponse<null>>;
    disableTwoFactor(req: any): Promise<import("../utils/@types").ApiResponse<null>>;
    getInvestigators(): Promise<import("../utils/@types").ApiResponse<{
        id: string;
        name: string;
        email: string;
        telephone: string;
        role: import("@prisma/client").$Enums.Role;
        createdAt: Date;
        updatedAt: Date;
    }[]>>;
}
