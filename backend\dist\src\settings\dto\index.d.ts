export declare class CreateContactInfoDto {
    name: string;
    description?: string;
    email: string;
    telephone: string;
}
declare const UpdateContactInfoDto_base: import("@nestjs/common").Type<Partial<CreateContactInfoDto>>;
export declare class UpdateContactInfoDto extends UpdateContactInfoDto_base {
}
export declare class CreateOccurrenceCategoryDto {
    category: string;
    description: string;
}
declare const UpdateOccurrenceCategoryDto_base: import("@nestjs/common").Type<Partial<CreateOccurrenceCategoryDto>>;
export declare class UpdateOccurrenceCategoryDto extends UpdateOccurrenceCategoryDto_base {
}
export {};
