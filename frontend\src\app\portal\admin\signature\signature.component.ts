import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { SignatureService } from './signature.service';
import { MessageService } from 'primeng/api';
import { ConfirmationService } from 'primeng/api';
import { Signature, User } from '../../../util/@types';
import { AuthService } from '../../auth/auth.service';

@Component({
  selector: 'app-users',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    TableModule,
    DialogModule,
    ToastModule,
    ConfirmDialogModule
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './signature.component.html',
})
export class SignatureComponent implements OnInit {
  signatures: Signature[] = [];
  totalSignatures: number = 0;
  currentPage: number = 1;
  pageSize: number = 10;

  searchQuery: string = ''

  signatureModalVisible: boolean = false;
  isEditMode: boolean = false;
  
  currentSignature: Partial<Signature> = {};
  me!: User | null
  imageFile: File | undefined = undefined
  imagePreview: string | null = null

  constructor(
    private signatureService: SignatureService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.loadSignatures();
    this.me = this.authService.getUserInfo()
  }

  onFileSelected(event: Event) {
    const file = (event.target as HTMLInputElement).files?.[0];
    this.imageFile = file
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        this.imagePreview = reader.result as string;
        console.log(this.imagePreview)
      };
      reader.readAsDataURL(file);
    }
  }

  async loadSignatures(page: number = 1) {
    try {
      const response = await this.signatureService.getSignatures(page, this.pageSize);
      console.log(response)
      this.signatures = response.data;
      this.totalSignatures = response.total;
    } catch (error) {
      this.handleError('Failed to load users', error);
    }
  }

  onPageChange(event: any) {
    this.currentPage = event.page + 1;
    this.pageSize = event.rows;
    this.loadSignatures(this.currentPage);
  }

  openModal(mode: 'create' | 'edit', signature?: Signature) {
    this.imageFile = undefined
    this.imagePreview =  null
    this.isEditMode = mode === 'edit';
    this.currentSignature = this.isEditMode 
      ? { ...signature } 
      : { name: '',};
    this.signatureModalVisible = true;
  }

  async save() {
    try {
      if (this.isEditMode) {
        let imageUrl
        if(this.imageFile){
          const formData = new FormData()
          formData.append('file', this.imageFile)
          imageUrl = await this.signatureService.upload(formData)
          console.log(imageUrl)
        }
        
        await this.signatureService.updateSignature(
          this.currentSignature.id!,
          this.currentSignature.name!, 
          imageUrl ?? this.currentSignature.imageUrl ?? ''
        );
        this.messageService.add({
          severity: 'success', 
          summary: 'Signature Updated', 
          detail: 'Signature has been successfully updated.'
        });
      } else {
        let imageUrl
        if(this.imageFile){
          const formData = new FormData()
          formData.append('file', this.imageFile)
          imageUrl = await this.signatureService.upload(formData)
          console.log(imageUrl)
        }
    
        await this.signatureService.createSignature(
          this.currentSignature.name!, 
          imageUrl??this.currentSignature.imageUrl??''
        );
        this.messageService.add({
          severity: 'success', 
          summary: 'Signature Created', 
          detail: `new signature has been created successfully`,
          life: 1000
        });
      }
      
      this.signatureModalVisible = false;
      this.loadSignatures(this.currentPage);
    } catch (error) {
      this.handleError('Failed to save signature', error);
    }
  }

  confirmDelete(signature: Signature) {
    this.confirmationService.confirm({
      message: `Are you sure you want to delete this signature`,
      header: 'Confirm Deletion',
      icon: 'pi pi-info-circle',
      acceptIcon:"none",
      rejectIcon:"none",
      acceptButtonStyleClass:"p-button-danger p-button-text",
      rejectButtonStyleClass:"p-button-text p-button-text mr-4", 
      accept: () => this.deleteSignature(signature.id!)
    });
  }

  async deleteSignature(id: string) {
    try {
      await this.signatureService.deleteSignature(id);
      this.messageService.add({
        severity: 'success', 
        summary: 'Signature Deleted', 
        detail: 'Signature has been successfully deleted.'
      });
      this.loadSignatures(this.currentPage);
    } catch (error) {
      this.handleError('Failed to delete signature', error);
    }
  }

  private handleError(message: string, error: any) {
    console.error(error);
    this.messageService.add({
      severity: 'error', 
      summary: 'Error', 
      detail: error.response.data.message || error.response.data.message[0] || error.message
    });
  }
}