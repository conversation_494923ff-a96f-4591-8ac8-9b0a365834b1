import { BadRequestException, Controller, Get, NotAcceptableException, Param, Post, Res, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { createReadStream, existsSync } from 'fs';
import { join, extname } from 'path';
import { diskStorage } from 'multer';
import { v4 as uuidv4} from 'uuid'

@ApiTags('Attachment')
@Controller('attachment')
export class AttachmentController {

  @ApiOperation({ summary: 'Upload file' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        kind: { type: 'string' },
        unique_reference: { type: 'string' },
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @Post('upload')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads',
        filename: (req, file, callback) => {
          const uniqueSuffix = `${Date.now()}-${uuidv4()}`;
          const originalName = file.originalname;
          const extension = originalName.split('.').pop();
          const uniqueFilename = `${uniqueSuffix}.${extension}`;
          callback(null, uniqueFilename);
        },
      }),
    }),
  )
  uploadFile(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new NotAcceptableException('File upload failed')
    }

    return {
      success: true,
      message: 'File uploaded successfully!',
      url: `${process.env.SERVER_DOMAIN}/attachment/path/${file.filename}`, 
    }
  }

  // Serve file endpoint
  @ApiOperation({ summary: 'Get file by filename' })
  @Get('/path/:filename')
  getFile(@Param('filename') filename: string, @Res() res: Response) {
    const filePath = join(process.cwd(), `uploads/${filename}`);
    if (!existsSync(filePath)) {
      return res.status(404).json({ status: false, message: 'File not found!' });
    }

    const fileStream = createReadStream(filePath);
    fileStream.on('error', (err) => {
      res.status(500).json({ status: false, message: 'Error reading file' });
    });

    res.set({
      'Content-Disposition': `attachment; filename="${filename}"`,
      'Content-Type': 'application/octet-stream',
    });
    fileStream.pipe(res);
  }
}

