import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MessageService, ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { AircraftInfo, OccurrenceCategory } from '../../../util/@types';
import { AircraftsService } from './aircrafts.service';

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    TableModule,
    DialogModule,
    ToastModule,
    ConfirmDialogModule
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './aircrafts.component.html'
})
export class AircraftsComponent {

  aircrafts: AircraftInfo[] = [];
  totalAircrafts: number = 0;
  currentPage: number = 1;
  pageSize: number = 10;

  first: number = 0

  searchQuery: string = ''

  aircraftsModalVisible: boolean = false;
  isEditMode: boolean = false;

  currentAircraft: Partial<AircraftInfo> = {};

  constructor(
    private airCraftsService: AircraftsService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService
  ) { }

  ngOnInit(): void {
    this.loadAicrafts();
  }

  async loadAicrafts(page: number = 1) {
    try {
      const response = await this.airCraftsService.getAircrafts(page, this.pageSize);
      this.aircrafts = response.data;
      this.totalAircrafts = response.total;
    } catch (error) {
      this.handleError('Failed to load aicrafts', error);
    }
  }

  async search(){
    try {
      if(this.searchQuery === '') {
        this.loadAicrafts()
        return
      }
      const resp = await this.airCraftsService.search(this.searchQuery, this.currentPage)
      this.aircrafts = resp.data
    } catch (error) {
      this.handleError('search failed', error);
    }
  }

  onPageChange(event: any) {
    this.currentPage = event.page + 1;
    this.pageSize = event.rows;
    this.loadAicrafts(this.currentPage);
  }

  openCategoriesModal(mode: 'create' | 'edit', aircraft?: AircraftInfo) {
    this.isEditMode = mode === 'edit';
    this.currentAircraft = this.isEditMode
      ? { ...aircraft }
      : { 
        modelFullName: '',
        description: '',
        designator: '',
        manufacturerCode: '',
        aircraftDescription: '',
        engineType: '',
        wtc: '',
        wtg: '',
      };
    this.aircraftsModalVisible = true;
  }

  async saveAicraft() {
    try {
      if (this.isEditMode) {
        await this.airCraftsService.updateAircraft(
          this.currentAircraft.id!,
          this.currentAircraft.modelFullName!,
          this.currentAircraft.description!,
          this.currentAircraft.designator!,
          this.currentAircraft.manufacturerCode!,
          this.currentAircraft.aircraftDescription!,
          this.currentAircraft.engineCount!,
          this.currentAircraft.engineType!,
          this.currentAircraft.wtc!,
          this.currentAircraft.wtg!,
        );
        this.messageService.add({
          severity: 'success',
          summary: 'Aicraft Updated',
          detail: 'Aicraft has been successfully updated.'
        });
      } else {
        await this.airCraftsService.createAircraft(
          this.currentAircraft.modelFullName!,
          this.currentAircraft.description!,
          this.currentAircraft.designator!,
          this.currentAircraft.manufacturerCode!,
          this.currentAircraft.aircraftDescription!,
          this.currentAircraft.engineCount!,
          this.currentAircraft.engineType!,
          this.currentAircraft.wtc!,
          this.currentAircraft.wtg!,
        );
        this.messageService.add({
          severity: 'success',
          summary: 'Aicraft Created',
          detail: `New Aicraft has been successfully created.`,
          life: 1000
        });
      }

      this.aircraftsModalVisible = false;
      this.loadAicrafts(this.currentPage);
    } catch (error) {
      this.handleError('Failed to save aircraft', error);
    }
  }

  confirmDelete(aircraft: AircraftInfo) {
    this.confirmationService.confirm({
      message: `Are you sure you want to delete aicraft ${aircraft.modelFullName}?`,
      header: 'Confirm Deletion',
      icon: 'pi pi-info-circle',
      acceptIcon: "none",
      rejectIcon: "none",
      acceptButtonStyleClass: "p-button-danger p-button-text",
      rejectButtonStyleClass: "p-button-text p-button-text mr-4",
      accept: () => this.deleteAicraft(aircraft.id!)
    });
  }

  async deleteAicraft(id: string) {
    try {
      await this.airCraftsService.deleteAircraft(id);
      this.messageService.add({
        severity: 'success',
        summary: 'Aicraft Deleted',
        detail: 'Aicraft has been successfully deleted.'
      });
      this.loadAicrafts(this.currentPage);
    } catch (error) {
      this.handleError('Failed to delete aircraft', error);
    }
  }

  private handleError(message: string, error: any) {
    console.error(error);
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: error.response.data.message || error.response.data.message[0] || error.message
    });
  }

}
