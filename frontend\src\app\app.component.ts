import { Component, On<PERSON><PERSON>roy, AfterViewInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { UserInactivityService } from './util/guards/inactivity.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements AfterViewInit, OnDestroy {
  title = 'AAID Notification';

  constructor(private userInactivityService: UserInactivityService) {}

  ngAfterViewInit(): void {
    this.userInactivityService.initializeInactivityTimer();
  }

  ngOnDestroy(): void {
    this.userInactivityService.destroy();
  }
}
