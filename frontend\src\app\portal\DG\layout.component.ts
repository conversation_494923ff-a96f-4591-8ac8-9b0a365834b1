import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Router, RouterLink, RouterModule } from '@angular/router';
import { MenuModule } from 'primeng/menu'
import { ButtonModule } from 'primeng/button';
import { User } from '../../util/@types';
import { AuthService } from '../auth/auth.service';
import { MenuItem, MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import { ReportsService } from '../app/reports/reports.service';

@Component({
  selector: 'app-admin-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    RouterLink,
    MenuModule,
    ButtonModule,
    ToastModule
  ],
  providers: [MessageService],
  templateUrl: './layout.component.html',
})
export class DGLayoutComponent implements OnInit {

  user: User | null = null
  isSidebarOpen = false;
  pending: number = 0

  constructor(
    private authService: AuthService,
    public router: Router,
    private reportsService: ReportsService
  ){}

  ngOnInit(): void {
    this.user = this.authService.getUserInfo()
    console.log(this.user)
    this.laoadPending()
  }

  userMenuItems: MenuItem[] = [
    {
      separator: true
    },
    {
      label: 'Sign Out',
      icon: 'pi pi-sign-out',
      command: () => this.signOut()
    }
  ];

  toggleSidebar() {
    this.isSidebarOpen = !this.isSidebarOpen;
  }

  navigateToProfile() {
  }

  navigateToSettings() {
  }

  signOut() {
    this.authService.logout()
  }

  async laoadPending(){
   try {
    let reports = await this.reportsService.getAllReports()
    this.pending = reports.filter(i=> i.status === 'PENDING').length
   } catch (error) {
    console.log('failed to get pending reports')
   }
  }

}
