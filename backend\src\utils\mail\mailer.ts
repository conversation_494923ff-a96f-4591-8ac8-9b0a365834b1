import { Logger } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import { env } from '../env';

const mailer = nodemailer.createTransport({
  host: env.SMTP_SERVER,
  port: env.SMTP_PORT,
  //   secure: true,
  //   auth: {
  //     user: env.SMTP_USERNAME,
  //     pass: env.SMTP_PASSWORD,
  //   },
});

mailer.verify((error: any, success: any) => {
  if (error) {
    Logger.error('SMTP Connection failed', 'NodeMailerConn');
  } else if (success) {
    Logger.log('Smtp service connected', 'NodeMailerConn');
  }
});

export default mailer;
