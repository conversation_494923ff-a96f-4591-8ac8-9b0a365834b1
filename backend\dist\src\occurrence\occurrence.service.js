"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OccurrenceService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const _types_1 = require("../utils/@types");
const mail_service_1 = require("../utils/mail/mail.service");
const client_1 = require("@prisma/client");
let OccurrenceService = class OccurrenceService {
    constructor(prisma, mailService) {
        this.prisma = prisma;
        this.mailService = mailService;
    }
    async createOccurrence(data) {
        try {
            const occurrence = await this.prisma.occurrence.create({
                data: {
                    ...data,
                    involvedAircraft: {
                        create: data.involvedAircraft.map((aircraft) => ({
                            ...aircraft,
                        })),
                    },
                },
                include: {
                    involvedAircraft: true,
                },
            });
            const investigators = await this.prisma.user.findMany({
                where: {
                    role: client_1.Role.INVESTIGATOR
                }
            });
            investigators.forEach((investigator) => {
                this.mailService.sendMail(mail_service_1.MailType.OCCURENCE_SUBMIT, {
                    name: investigator.name,
                    to: investigator.email,
                    subject: "New occurence | AAID Notification",
                    values: {
                        reporterName: occurrence.reporterName,
                        reporterEmail: occurrence.reporterEmail,
                        occurrenceTime: occurrence.occurrenceTime?.toLocaleString() || 'N/A',
                        occurrenceLocation: occurrence.occurrenceLocation || 'N/A'
                    }
                });
            });
            return new _types_1.ApiResponse(true, 'Occurrence successfully created.', occurrence);
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException(error.message);
        }
    }
    async getOccurrences() {
        try {
            const occurrences = await this.prisma.occurrence.findMany({
                orderBy: {
                    createdAt: "desc"
                },
                include: {
                    involvedAircraft: true,
                    occurrenceCategory: true
                }
            });
            return new _types_1.ApiResponse(true, 'Occurrences', occurrences);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException(error.message);
        }
    }
    async getOccurrencesById(id) {
        const occurence = await this.prisma.occurrence.findUnique({
            where: {
                id
            },
            include: {
                involvedAircraft: true,
                occurrenceCategory: true
            }
        });
        if (!occurence) {
            throw new common_1.NotFoundException("Occurrence not found");
        }
        return new _types_1.ApiResponse(true, 'Occurrences', occurence);
    }
    async updateOccurrence(id, data) {
        const { involvedAircraft, occurrenceCategory_id, ...occurrenceData } = data;
        const existingOccurrence = await this.prisma.occurrence.findUnique({
            where: { id },
        });
        if (!existingOccurrence) {
            throw new common_1.NotFoundException(`Occurrence with ID ${id} not found`);
        }
        return this.prisma.$transaction(async (prisma) => {
            const updatedOccurrence = await prisma.occurrence.update({
                where: { id },
                data: {
                    ...occurrenceData,
                    ...(occurrenceCategory_id
                        ? {
                            occurrenceCategory: {
                                connect: { id: occurrenceCategory_id },
                            },
                        }
                        : {}),
                },
            });
            if (involvedAircraft) {
                await this.prisma.aircraft.deleteMany({
                    where: {
                        occurrenceId: existingOccurrence.id
                    }
                });
                await Promise.all(involvedAircraft.map(async (item) => {
                    await this.prisma.aircraft.create({
                        data: {
                            model: item.model,
                            crewInjured: item.crewInjured,
                            crewOnBoard: item.crewOnBoard,
                            crewPerished: item.crewPerished,
                            passengersInjured: item.passengersInjured,
                            passengersOnBoard: item.passengersOnBoard,
                            passengersPerished: item.passengersPerished,
                            intendedLandingDateTime: item.intendedLandingDateTime,
                            intendedLandingPoint: item.intendedLandingPoint,
                            lastDeparturePoint: item.lastDeparturePoint,
                            manufacturer: item.manufacturer,
                            operator: item.operator,
                            operatorNationality: item.operatorNationality,
                            registrationMark: item.registrationMark,
                            occurrence: { connect: { id } },
                        },
                    });
                }));
            }
            return updatedOccurrence;
        });
    }
    async deleteOccurrence(id) {
        const occurence = await this.prisma.occurrence.findUnique({
            where: {
                id
            }
        });
        if (!occurence) {
            throw new common_1.NotFoundException("Occurrence not found");
        }
        const deleted = await this.prisma.occurrence.delete({
            where: {
                id
            }
        });
        return new _types_1.ApiResponse(true, 'Occurrence deleted', deleted);
    }
    async generateReferenceNumber(id) {
        const occurrence = await this.prisma.occurrence.findUnique({
            where: { id },
            include: { occurrenceCategory: true },
        });
        if (!occurrence) {
            throw new common_1.NotFoundException('Occurrence not found');
        }
        if (!occurrence.type) {
            throw new common_1.NotAcceptableException('The occurrence must have a type first');
        }
        if (occurrence.referenceNumber) {
            await this.prisma.occurrence.update({
                where: { id },
                data: { referenceNumber: null },
            });
        }
        const lastNumber = await this.prisma.occurrence.count({
            where: { referenceNumber: { not: null } },
        });
        const { createdAt, type } = occurrence;
        const year = createdAt.getFullYear();
        const typeMapping = {
            [client_1.OccurrenceType.ACCIDENT]: 'ACCID',
            [client_1.OccurrenceType.MAJOR_ACCIDENT]: 'ACCID',
            [client_1.OccurrenceType.INCIDENT]: 'INCID',
            [client_1.OccurrenceType.INCIDENT_TO_BE_INVESTIGATED]: 'INCID',
            [client_1.OccurrenceType.SERIOUS_INCIDENT]: 'SINCID',
        };
        const referenceType = typeMapping[type] || 'OCC';
        const newRefNumber = `${year}-${referenceType}-${String(lastNumber + 1).padStart(4, '0')}`;
        const updatedOccurrence = await this.prisma.occurrence.update({
            where: { id },
            data: { referenceNumber: newRefNumber },
        });
        return new _types_1.ApiResponse(true, 'Reference number generated successfully', updatedOccurrence);
    }
    async searchOccurrence(query) {
        const results = await this.prisma.occurrence.findMany({
            where: {
                OR: [
                    {
                        referenceNumber: {
                            contains: query,
                            mode: 'insensitive',
                        },
                    },
                    {
                        reporterName: {
                            contains: query,
                            mode: 'insensitive',
                        },
                    },
                    {
                        reporterEmail: {
                            contains: query,
                            mode: 'insensitive',
                        },
                    },
                    {
                        occurrenceLocation: {
                            contains: query,
                            mode: 'insensitive',
                        },
                    },
                    {
                        occurrenceCategory: {
                            category: {
                                contains: query,
                                mode: 'insensitive',
                            },
                            description: {
                                contains: query,
                                mode: 'insensitive',
                            },
                        }
                    }
                ],
            },
            include: {
                occurrenceCategory: true
            }
        });
        console.log(`results for (): ${query}` + results.length);
        return new _types_1.ApiResponse(true, 'Search results', { data: results, count: results.length });
    }
};
exports.OccurrenceService = OccurrenceService;
exports.OccurrenceService = OccurrenceService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService, mail_service_1.MailService])
], OccurrenceService);
//# sourceMappingURL=occurrence.service.js.map