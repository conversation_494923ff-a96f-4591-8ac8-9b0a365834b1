import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { UsersService } from './users.service';
import { RolesGuard } from 'src/utils/guards/role.guard';
import { Role } from '@prisma/client';
import { Roles } from 'src/utils/decorators/role.decorator';
import { ActivateAccountDto, CreateUserDto, ResetPasswordConfirmDto, ResetPasswordRequestDto, SearchDto, UpdateUserDto } from './dto';

@ApiTags("Users")
@UseGuards(RolesGuard)
@Controller('users')
export class UsersController {
  constructor(private userService: UsersService) { }

  @Post()
  @Roles(Role.ADMIN)
  createUser(@Body() createUserDto: CreateUserDto) {
    return this.userService.createUser(createUserDto);
  }

  @Patch('activate')
  activateAccount(@Body() activateAccountDto: ActivateAccountDto) {
    return this.userService.activateAccount(activateAccountDto);
  }

  @Get()
  @Roles(Role.ADMIN)
  getAllUsers() {
    return this.userService.getAllUsers();
  }

  @Get('id/:id')
  @Roles(Role.ADMIN)
  getUser(@Param('id') id: string) {
    return this.userService.getUser(id)
  }

  @Get('search')
  @Roles(Role.ADMIN)
  searchUsers(@Query() searchDto: SearchDto) {
    const { query, page, limit } = searchDto;
    return this.userService.searchUsers(query, page, limit);
  }

  @Patch(':id')
  @Roles(Role.ADMIN)
  async updateUser(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
  ) {
    return this.userService.updateUser(id, updateUserDto);
  }


  @Post('reset-password')
  async resetPassword(
    @Body() resetPasswordRequestDto: ResetPasswordRequestDto) {
    return this.userService.initiatePasswordReset(resetPasswordRequestDto);
  }

  @Post('confirm-reset-password')
  async confirmResetPassword(
    @Body() resetPasswordConfirmDto: ResetPasswordConfirmDto) {
    return this.userService.confirmPasswordReset(
      resetPasswordConfirmDto,
    );
  }

  @Patch('activate-user/:id')
  activateUser(@Param('id') id: string) {
    return this.userService.activateUserAccount(id)
  }

  @Patch('deactivate-user/:id')
  deactivateUser(@Param('id') id: string) {
    return this.userService.deactivateUserAccount(id)
  }

  @Post('enable-2fa')
  async enableTwoFactor(@Req() req) {
    const userId = req.user.id; 
    const result = await this.userService.generateTwoFactorSecret(userId);
    return result;
  }

  @Post('verify-2fa-setup')
  async verifyTwoFactorSetup(
    @Req() req,
    @Body('token') token: string
  ) {
    const userId = req.user.id;
    return this.userService.verifyTwoFactorSetup(userId, token);
  }

  @Post('disable-2fa')
  async disableTwoFactor(@Req() req) {
    const userId = req.user.id;
    return this.userService.disableTwoFactor(userId);
  }

  @Get('investigators')
  async getInvestigators(){
    return this.userService.getInvestigators()
  }
}
