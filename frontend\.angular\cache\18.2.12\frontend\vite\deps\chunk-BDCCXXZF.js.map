{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/primeng@17.18.12_@angular+c_33670b6b2f9b97f9e20c1b9af5055bd5/node_modules/primeng/fesm2022/primeng-baseicon.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input } from '@angular/core';\nimport { ObjectUtils } from 'primeng/utils';\nconst _c0 = [\"*\"];\nclass BaseIcon {\n  label;\n  spin = false;\n  styleClass;\n  role;\n  ariaLabel;\n  ariaHidden;\n  ngOnInit() {\n    this.getAttributes();\n  }\n  getAttributes() {\n    const isLabelEmpty = ObjectUtils.isEmpty(this.label);\n    this.role = !isLabelEmpty ? 'img' : undefined;\n    this.ariaLabel = !isLabelEmpty ? this.label : undefined;\n    this.ariaHidden = isLabelEmpty;\n  }\n  getClassNames() {\n    return `p-icon ${this.styleClass ? this.styleClass + ' ' : ''}${this.spin ? 'p-icon-spin' : ''}`;\n  }\n  static ɵfac = function BaseIcon_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BaseIcon)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: BaseIcon,\n    selectors: [[\"ng-component\"]],\n    hostAttrs: [1, \"p-element\", \"p-icon-wrapper\"],\n    inputs: {\n      label: \"label\",\n      spin: [2, \"spin\", \"spin\", booleanAttribute],\n      styleClass: \"styleClass\"\n    },\n    standalone: true,\n    features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function BaseIcon_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseIcon, [{\n    type: Component,\n    args: [{\n      template: ` <ng-content></ng-content> `,\n      standalone: true,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element p-icon-wrapper'\n      }\n    }]\n  }], null, {\n    label: [{\n      type: Input\n    }],\n    spin: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    styleClass: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseIcon };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAGA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,WAAN,MAAM,UAAS;AAAA,EACb;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW;AACT,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,gBAAgB;AACd,UAAM,eAAe,YAAY,QAAQ,KAAK,KAAK;AACnD,SAAK,OAAO,CAAC,eAAe,QAAQ;AACpC,SAAK,YAAY,CAAC,eAAe,KAAK,QAAQ;AAC9C,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,gBAAgB;AACd,WAAO,UAAU,KAAK,aAAa,KAAK,aAAa,MAAM,EAAE,GAAG,KAAK,OAAO,gBAAgB,EAAE;AAAA,EAChG;AAAA,EACA,OAAO,OAAO,SAAS,iBAAiB,mBAAmB;AACzD,WAAO,KAAK,qBAAqB,WAAU;AAAA,EAC7C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,WAAW,CAAC,GAAG,aAAa,gBAAgB;AAAA,IAC5C,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,MAAM,CAAC,GAAG,QAAQ,QAAQ,gBAAgB;AAAA,MAC1C,YAAY;AAAA,IACd;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,0BAA6B,mBAAmB;AAAA,IAC9D,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,kBAAkB,IAAI,KAAK;AAC5C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;", "names": []}