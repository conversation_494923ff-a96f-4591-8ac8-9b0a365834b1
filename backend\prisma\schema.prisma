generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgres"
  url      = env("DATABASE_URL")
}

model Aircraft {
  id                      String     @id @default(uuid())
  model                   String
  manufacturer            String?
  operator                String?
  registrationMark        String?
  operatorNationality     String?
  intendedLandingDateTime DateTime?
  intendedLandingPoint    String?
  lastDeparturePoint      String?
  crewOnBoard             Int?
  crewInjured             Int?
  crewPerished            Int?
  passengersOnBoard       Int?
  passengersInjured       Int?
  passengersPerished      Int?
  occurrenceId            String
  occurrence              Occurrence @relation(fields: [occurrenceId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Occurrence {
  id              String  @id @default(uuid())
  referenceNumber String? @unique

  // General Info
  reporterName        String
  reporterEmail       String
  reporterPhone       String
  pilotInCommandName  String?
  pilotInCommandEmail String?
  pilotInCommandPhone String?

  // Involved Aircraft
  involvedAircraft     Aircraft[]
  groundPeoplePerished Int?
  groundPeopleInjured  Int?

  // Details
  generalWeatherConditions String?
  skyCoverage              String?
  meteologicalCondition    MeteologicalCondition?
  flightRules              FlightRules?
  occurrenceTime           DateTime?
  operationType            OperationType?
  flightPhase              String?

  // Coordinates
  latitude           String?
  longitude          String?
  occurrenceLocation String?

  dangerousGoodCarriedOnBoard String?

  type                  OccurrenceType?
  occurrenceCategory    OccurrenceCategory? @relation(fields: [occurrenceCategory_id], references: [id], onDelete: SetNull)
  occurrenceCategory_id String?

  status OccurrenceStatus?

  documents             Document[]
  safetyRecommendations SafetyRecommendation[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model OccurrenceCategory {
  id          String       @id @default(uuid())
  category    String       @unique
  description String
  explanation String?
  occurences  Occurrence[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model User {
  id             String  @id @default(uuid())
  name           String
  email          String  @unique
  telephone      String  @unique
  password       String?
  role           Role
  profilePicture String?

  // Multi-factor authentication
  multiFactorEnabled Boolean @default(false)
  multiFactorSecret  String?

  // Verification and reset tokens
  resetToken       String?
  resetTokenExpiry DateTime?

  passwordExpiryDate DateTime
  status             AccountStatus @default(PENDING)

  notifications Notification[]
  logs          Logs[]

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  lastLoginAt DateTime?

  Report Report[]

  Approved Report[] @relation(name: "approval")
}

model countriesInfo {
  id String @id @default(uuid())
  name String @unique
  flag String

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model Document {
  id           String       @id @default(uuid())
  name         String
  type         DocumentType
  url          String
  occurrenceId String
  occurrence   Occurrence   @relation(fields: [occurrenceId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model SafetyRecommendation {
  id          String     @id @default(uuid())
  title       String
  description String     @db.Text
  occurenceId String
  occurence   Occurrence @relation(fields: [occurenceId], references: [id], onDelete: Cascade)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
}

model Notification {
  id          String  @id @default(uuid())
  title       String
  description String?
  userId      String
  user        User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Logs {
  id              String      @id @default(uuid())
  action          String?
  sourceUrl       String
  sourceIpAddress String
  sourceOS        String
  sourceBrowser   String
  url             String
  method          HTTP_Method
  userId          String?
  user            User?       @relation(fields: [userId], references: [id], onDelete: SetNull)

  createdAt DateTime @default(now())
}

model ContactsInfo {
  id          String   @id @default(uuid())
  name        String
  description String?
  email       String   @unique
  telephone   String   @unique
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model AircraftData {
  id                  String   @id @default(uuid())
  modelFullName       String
  description         String
  wtc                 String?
  wtg                 String?
  designator          String
  manufacturerCode    String
  aircraftDescription String
  engineCount         Int?
  engineType          String?
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  @@index([modelFullName, designator, manufacturerCode], map: "aircraft_search_index")
}

enum ReportOccurrenceType {
  ACCID
  SINCID
  INCID
}

enum AddressedTo {
  StateOfRegistry
  StateOfDesign
  StateOfManufacturer
  StateOfOperator
  ICAO
}

enum ReportStatus {
  PENDING
  APPROVED
  REVERTED
}

model Report {
  id                  String        @id @default(uuid())
  addressedTo         AddressedTo[]
  stateOfRegistry     String?
  stateOfDesign       String?
  stateOfManufacturer String?
  stateOfOperator     String?

  status  ReportStatus?
  comment String?

  referenceNumber String               @unique
  occurrenceType  ReportOccurrenceType

  // Aircraft-related sections grouped together
  aircraftInfo Json?

  groundPeopleInjured  Int?
  groundPeoplePerished Int?

  // Date and Time
  occurrenceDate      DateTime?
  occurrenceTimeUTC   DateTime?
  occurrenceTimeLocal DateTime?

  // Aircraft Location
  position  String?
  latitude  String?
  longitude String?

  // Description
  occurrenceDescription String
  damageExtent          String

  // Investigation Details
  investigationExtent     String?
  investigationDelegation String?

  // Physical Characteristics
  areaCharacteristics String?
  accessRequirements  String?

  // Investigation Authority
  originatingAuthority  String
  investigatorName      String
  investigatorMobile    String
  investigatorEmail     String

  // Dangerous Goods
  dangerousGoodsPresent     Boolean
  dangerousGoodsDescription String?

  submittedBy User? @relation(fields: [submittedById], references: [id])
  submittedById String?

  approvedBy User? @relation(fields: [approvedById], references: [id], name: "approval")
  approvedById String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Signature {
  id        String   @id @default(uuid())
  name      String   @unique()
  imageUrl  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum Role {
  ADMIN
  INVESTIGATOR
  DG
  ACTING_DG
}

enum AccountStatus {
  ACTIVE
  DEACTIVATED
  PENDING
}

enum OccurrenceStatus {
  OPEN
  UNDER_INVESTIGATION
  CLOSED
}

enum MeteologicalCondition {
  IMC
  VMC
}

enum FlightRules {
  IFR
  VFR
}

enum OperationType {
  SCHEDULED
  CHARTER
  TRAINING
  PRIVATE
  DEMONSTRATION
  AEROBIC
  AERIAL_TOUR
  FIXED_WING_AIRCRAFT
  HELICOPTER
  BALLOON
  DRONE
  OTHER
}

enum OccurrenceType {
  MAJOR_ACCIDENT
  ACCIDENT
  SERIOUS_INCIDENT
  INCIDENT_TO_BE_INVESTIGATED
  INCIDENT
  ABNORMAL_OCCURRENCE
}

enum HTTP_Method {
  GET
  POST
  PUT
  PATCH
  DELETE
}

enum NotificationType {
  ERROR
  INFO
  WARNING
}

enum DocumentType {
  EVIDENCE
  PHOTOGRAPHS
  STATEMENTS
  OTHER
}
