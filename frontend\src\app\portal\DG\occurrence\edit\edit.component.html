<style>
    ::ng-deep .tooltip {
        max-width: 70vw;
    }
</style>

<div class="w-full h-full p-8 flex flex-col gap-4 overflow-scroll">
    <div class="flex flex-col gap-2">
        <h2 class="text-xl font-semibold text-gray-700">Edit Occurrence </h2>
        <label class="text-gray-700 font-medium mt-4">Occurrence classification <span
                class="text-red-300 text-sm">*</span></label>
        <div class="flex justify-between items-center ">

            <div class="flex gap-2">
                <div class="flex flex-col">
                    <p-dropdown [options]="typeOptions" [ngModel]="typeControl?.value"
                        (ngModelChange)="typeControl?.setValue($event)" optionLabel="label" optionValue="value"
                        [ngClass]="{'ng-invalid ng-dirty': typeControl?.invalid && typeControl?.touched}"
                        placeholder="Select Type">
                    </p-dropdown>
                    <small class="text-red-500" *ngIf="typeControl?.invalid && typeControl?.touched">
                        Type is required
                    </small>
                </div>

                <div class="flex flex-col">
                    <p-dropdown [options]="categoryOptions" [ngModel]="categoryControl?.value"
                        (ngModelChange)="categoryControl?.setValue($event)" optionLabel="label" optionValue="value"
                        [ngClass]="{'ng-invalid ng-dirty': categoryControl?.invalid && categoryControl?.touched}"
                        placeholder="Select Category">
                        <ng-template let-item pTemplate="item">
                            <div [pTooltip]="item.explanation" tooltipPosition="right" tooltipStyleClass="tooltip">
                                {{ item.label }}
                            </div>
                        </ng-template>
                    </p-dropdown>
                    <small class="text-red-500" *ngIf="categoryControl?.invalid && categoryControl?.touched">
                        Category is required
                    </small>
                </div>

                <!-- <p-dropdown 
                        [ngModel]="statusControl?.value" 
                        (ngModelChange)="statusControl?.setValue($event)"
                        [options]="statusOptions" 
                        placeholder="Select Status">
                    </p-dropdown> -->

            </div>
            <a *ngIf="occurrence.referenceNumber" href="/dg/reports/new/{{occurrence.id}}"
                class="py-2 px-3 block bg-primary-500 text-white rounded-lg text-sm">Notify other states</a>
        </div>
    </div>
    <form [formGroup]="occurrenceForm" class="pt-4 flex flex-col gap-4">
        <!-- AAID Information Section -->
        <div class="p-4 rounded-lg bg-white">
            <h4 class="text-gray-700 font-semibold">AAID Information</h4>
            <div class="flex items-center gap-2">
                <div class="max-w-sm w-full">
                    <label class="block text-sm font-medium text-gray-700">Reference Number</label>
                    <p>{{occurrence.referenceNumber ?? "N/A"}}</p>
                </div>
                @if(occurrence.occurrenceCategory){
                <button (click)="getRefNumber()"
                    class="px-4 py-2 mt-4 rounded-lg text-white bg-primary-500 active:scale-95">Generate</button>
                }
            </div>
        </div>

        <!-- General Information Section -->
        <div class="p-4 rounded-lg bg-white">
            <h4 class="text-gray-700 font-semibold">General Information</h4>
            <div class="pt-2 grid grid-cols-2 gap-4">
                <div>
                    <h5 class="font-semibold text-gray-700 mb-2">Reporter Information <span
                            class="text-red-300">*</span></h5>
                    <div class="grid grid-cols-1 gap-2">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Name</label>
                            <input pInputText type="text" formControlName="reporterName"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">

                            <small class="text-red-500" *ngIf="isFieldInvalid('reporterName')">
                                {{ getErrorMessage('reporterName') }}
                            </small>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Email</label>
                            <input pInputText type="email" formControlName="reporterEmail"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">

                            <small class="text-red-500" *ngIf="isFieldInvalid('reporterEmail')">
                                {{ getErrorMessage('reporterEmail') }}
                            </small>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Phone</label>
                            <input pInputText type="tel" formControlName="reporterPhone"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">

                            <small class="text-red-500" *ngIf="isFieldInvalid('reporterPhone')">
                                {{ getErrorMessage('reporterPhone') }}
                            </small>
                        </div>
                    </div>
                </div>
                <div>
                    <h5 class="font-semibold text-gray-700 mb-2">Pilot In Command Information <span
                            class="text-red-300">*</span></h5>
                    <div class="grid grid-cols-1 gap-2">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Name</label>
                            <input pInputText type="text" formControlName="pilotInCommandName"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Email</label>
                            <input pInputText type="email" formControlName="pilotInCommandEmail"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Phone</label>
                            <input pInputText type="tel" formControlName="pilotInCommandPhone"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Involved Aircraft Section -->
        <div class="p-4 rounded-lg bg-white">
            <div class="flex justify-between items-center mb-4">
                <h4 class="text-gray-700 font-semibold">Involved Aircraft <span class="text-red-300">*</span></h4>
                <button pButton type="button" label="Add Aircraft" (click)="openAircraftDialog()"
                    class="p-button-primary"></button>
            </div>
            <p-table [value]="involvedAircraft" [tableStyle]="{'min-width': '50rem'}">
                <ng-template pTemplate="header">
                    <tr>
                        <th>Manufacturer</th>
                        <th>Model</th>
                        <th>Registration Mark</th>
                        <th>Actions</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-aircraft>
                    <tr>
                        <td>{{aircraft.manufacturer}}</td>
                        <td>{{aircraft.model}}</td>
                        <td>{{aircraft.registrationMark ?? 'N/A'}}</td>
                        <td>
                            <div class="flex gap-2">
                                <button pButton type="button" icon="pi pi-pencil" (click)="openAircraftDialog(aircraft)"
                                    class="p-button-text"></button>
                                <button pButton type="button" icon="pi pi-trash" (click)="removeAircraft(aircraft)"
                                    class="p-button-danger"></button>
                            </div>
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </div>

        <div class="p-4 rounded-lg bg-white">
            <div class="grid md:grid-cols-2 grid-cols-1 gap-4">
                <div class="">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Ground people injured</label>
                    <input pInputText type="number" formControlName="groundPeopleInjured"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                </div>
                <div class="">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Ground people perished</label>
                    <input pInputText type="number" formControlName="groundPeoplePerished"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                </div>
            </div>
        </div>

        <!-- Occurrence Details Section -->
        <div class="p-4 rounded-lg bg-white">
            <h4 class="text-gray-700 font-semibold">Occurrence Details</h4>

            <div class="pt-2 grid grid-cols-2 gap-4">
                <div>
                    <div class="mb-3">
                        <label class="block text-gray-700 text-sm font-medium mb-2">
                            General weather conditions
                        </label>
                        <textarea formControlName="generalWeatherConditions"
                            class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700">Occurrence Time <span
                                class="text-red-300">*</span></label>
                        <input type="datetime-local" pInputText formControlName="occurrenceTime"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                        <small class="text-red-500" *ngIf="isFieldInvalid('occurrenceTime')">
                            {{ getErrorMessage('occurrenceTime') }}
                        </small>
                    </div>
                    <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700">Operation Type </label>
                        <select pInputText type="text" formControlName="operationType"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                            <option *ngFor="let type of operationTypeOptions" [value]="type.key">
                                {{ type.value }}
                            </option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700">Flight Rules </label>
                        <select pInputText type="text" formControlName="flightRules"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                            <option *ngFor="let rule of flightRulesOptions" [value]="rule">
                                {{ rule }}
                            </option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Meteorological Condition</label>
                        <select pInputText type="text" formControlName="meteologicalCondition"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                            <option *ngFor="let condition of meteologicalConditionOptions" [value]="condition">
                                {{ condition }}
                            </option>
                        </select>
                    </div>
                </div>
                <div>
                    <div class="mb-3">
                        <label class="block text-gray-700 font-medium text-sm  mb-2">
                            Sky Coverage
                        </label>
                        <textarea formControlName="skyCoverage"
                            class="outline-none border rounded-[10px] w-full py-2 px-4 text-gray-700 placeholder:text-sm focus:border-primary-500 transition-colors"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700">Occurrence Location</label>
                        <input pInputText type="text" formControlName="occurrenceLocation"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700">Latitude</label>
                        <input pInputText type="text" formControlName="latitude"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700">Longitude</label>
                        <input pInputText type="text" formControlName="longitude"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Flight Phase</label>
                        <select pInputText type="text" formControlName="flightPhase"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                            <option *ngFor="let phase of flightPhaseOptions" [value]="phase.key">
                                {{phase.value}}
                            </option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <!-- Save Button -->
        <div class="flex justify-end mt-2">
            <button type="button" (click)="saveOccurrence()"
                class="px-4 py-2 bg-primary-500 text-white active:scale-95 rounded-lg">Save Occurrence</button>
        </div>
    </form>


    <!-- Aircraft Dialog -->
    <p-dialog [(visible)]="displayAircraftDialog" [modal]="true" [style]="{width: '80vw'}" header="Aircraft Details">
        <form [formGroup]="aircraftForm" class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <h5 class="font-semibold text-gray-700 mb-2">Basic Information</h5>
                <div class="grid grid-cols-1 gap-2">
                    <div class="w-full">
                        <label class="block text-sm font-medium text-gray-700">Manufacturer *</label>
                        <p-autoComplete formControlName="manufacturer" [suggestions]="filteredManufacturers"
                            (completeMethod)="searchManufacturer($event)" [minLength]="2" [style]="{
                                    'border': '1px solid #3333',
                                    'border-radius': '0.8rem',
                                    'padding': '0.5rem',
                                    'width': '100%'
                                    
                                }">
                        </p-autoComplete>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Model *</label>
                        <p-dropdown formControlName="model" [options]="filteredModels"
                            [disabled]="!aircraftForm.get('manufacturer')?.value" placeholder="Select Model"
                            optionLabel="label" optionValue="value" (onChange)="searchModel()" [filter]="true" [style]="{
                                    'border': '1px solid #3333',
                                    'border-radius': '0.8rem',
                                    'width': '100%'
                                }" class="w-full">
                        </p-dropdown>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Registration Mark</label>
                        <input pInputText type="text" formControlName="registrationMark"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Operator</label>
                        <input pInputText type="text" formControlName="operator"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Operator Nationality</label>
                        <input pInputText type="text" formControlName="operatorNationality"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    </div>
                </div>
            </div>
            <div>
                <h5 class="font-semibold text-gray-700 mb-2">Operational Details</h5>
                <div class="grid grid-cols-1 gap-2">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Last Departure Point</label>
                        <input pInputText type="text" formControlName="lastDeparturePoint"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Intended Landing Point</label>
                        <input pInputText type="text" formControlName="intendedLandingPoint"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Intended Landing Date/Time</label>
                        <input type="datetime-local" pInputText formControlName="intendedLandingDateTime"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    </div>
                </div>
            </div>
            <div class="">
                <h5 class="font-semibold text-gray-700 mb-2">Crew and Passenger Information</h5>
                <div class="grid grid-cols-2 gap-2">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Crew On Board</label>
                        <input pInputText type="number" formControlName="crewOnBoard"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Crew Injured</label>
                        <input pInputText type="number" formControlName="crewInjured"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Crew Perished</label>
                        <input pInputText type="number" formControlName="crewPerished"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Passengers On Board</label>
                        <input pInputText type="number" formControlName="passengersOnBoard"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Passengers Injured</label>
                        <input pInputText type="number" formControlName="passengersInjured"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Passengers Perished</label>
                        <input pInputText type="number" formControlName="passengersPerished"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    </div>
                </div>
            </div>
            <div class="flex gap-2 mt-4">
                <button class="px-4 py-2 rounded-lg text-white bg-gray-500"
                    (click)="displayAircraftDialog = false">Cancel</button>
                <button class="px-4 py-2 rounded-lg text-white bg-primary-500" (click)="saveAircraft()">Save</button>
            </div>
        </form>
    </p-dialog>

    <!-- Toast for error messages -->
    <p-toast></p-toast>
</div>