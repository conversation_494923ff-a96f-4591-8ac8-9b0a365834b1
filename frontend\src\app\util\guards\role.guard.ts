import { Injectable } from "@angular/core";
import {ActivatedRouteSnapshot, CanActivate, GuardResult, MaybeAsync, Router, RouterStateSnapshot } from "@angular/router";
import { AuthService } from "../../portal/auth/auth.service";

@Injectable({
    providedIn: 'root'
})
export class RoleGuard implements CanActivate {

    constructor(
        private router: Router,
        private authService: AuthService
    ) {}

    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): MaybeAsync<GuardResult> {
        const requiredRole = route.data['role'] as string

        if(this.authService.isLoggedIn() && requiredRole.split(',').includes(this.authService.getRole()??'')){
            return true
        }
        
        this.router.navigate(['**'])
        return false
    }
    
}