"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const _types_1 = require("../utils/@types");
let SettingsService = class SettingsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createContactInfo(createDto) {
        try {
            const emailExists = await this.prisma.contactsInfo.findUnique({
                where: { email: createDto.email }
            });
            if (emailExists) {
                throw new common_1.BadRequestException("Email already exists");
            }
            const contact = await this.prisma.contactsInfo.create({ data: createDto });
            return new _types_1.ApiResponse(true, 'Contact info created successfully', contact);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException(error.message);
        }
    }
    async getAllContactInfo() {
        try {
            const contacts = await this.prisma.contactsInfo.findMany({
                orderBy: {
                    id: "desc"
                }
            });
            return new _types_1.ApiResponse(true, 'Contact info retrieved successfully', contacts);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException(error.message);
        }
    }
    async getContactInfoById(id) {
        try {
            const contact = await this.prisma.contactsInfo.findUnique({ where: { id } });
            if (!contact) {
                throw new common_1.NotFoundException("Contact not found");
            }
            return new _types_1.ApiResponse(true, 'Contact info retrieved successfully', contact);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException(error.message);
        }
    }
    async updateContactInfo(id, updateDto) {
        try {
            const contact = await this.prisma.contactsInfo.update({
                where: { id },
                data: updateDto,
            });
            return new _types_1.ApiResponse(true, 'Contact info updated successfully', contact);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException(error.message);
        }
    }
    async deleteContactInfo(id) {
        try {
            const contact = await this.prisma.contactsInfo.findUnique({ where: { id } });
            if (!contact) {
                throw new common_1.NotFoundException("Contact not found");
            }
            await this.prisma.contactsInfo.delete({ where: { id } });
            return new _types_1.ApiResponse(true, 'Contact info deleted successfully', null);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException(error.message);
        }
    }
    async createOccurrenceCategory(createDto) {
        try {
            const exits = await this.prisma.occurrenceCategory.findUnique({
                where: { category: createDto.category }
            });
            if (exits)
                throw new common_1.BadRequestException("Category with the same name exists");
            const category = await this.prisma.occurrenceCategory.create({ data: createDto });
            return new _types_1.ApiResponse(true, 'Occurrence category created successfully', category);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to create occurrence category: ' + error.message);
        }
    }
    async getAllOccurrenceCategories() {
        try {
            const categories = await this.prisma.occurrenceCategory.findMany({
                orderBy: {
                    id: "desc"
                }
            });
            return new _types_1.ApiResponse(true, 'Occurrence categories retrieved successfully', categories);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to retrieve occurrence categories: ' + error.message);
        }
    }
    async getOccurrenceCategoryById(id) {
        try {
            const category = await this.prisma.occurrenceCategory.findUnique({ where: { id } });
            if (!category) {
                throw new common_1.NotFoundException('Occurrence category not found');
            }
            return new _types_1.ApiResponse(true, 'Occurrence category retrieved successfully', category);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to retrieve occurrence category: ' + error.message);
        }
    }
    async updateOccurrenceCategory(id, updateDto) {
        try {
            const category = await this.prisma.occurrenceCategory.update({
                where: { id },
                data: updateDto,
            });
            return new _types_1.ApiResponse(true, 'Occurrence category updated successfully', category);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to update occurrence category: ' + error.message);
        }
    }
    async deleteOccurrenceCategory(id) {
        try {
            const category = await this.prisma.occurrenceCategory.findUnique({ where: { id } });
            if (!category) {
                throw new common_1.NotFoundException('Occurrence category not found');
            }
            await this.prisma.occurrenceCategory.delete({ where: { id } });
            return new _types_1.ApiResponse(true, 'Occurrence category deleted successfully', null);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to delete occurrence category: ' + error.message);
        }
    }
};
exports.SettingsService = SettingsService;
exports.SettingsService = SettingsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], SettingsService);
//# sourceMappingURL=settings.service.js.map