import { Injectable, NgZone } from '@angular/core';
import { Router } from '@angular/router';
import { fromEvent, merge, Subject } from 'rxjs';
import { takeUntil, debounceTime } from 'rxjs/operators';
import { AuthService } from '../../portal/auth/auth.service';

@Injectable({
    providedIn: 'root'
})
export class UserInactivityService {
    private readonly TIMEOUT_DURATION = 45 * 60 * 1000; // 45 minutes
    private readonly WARNING_BEFORE = 1 * 60 * 1000; // Show warning 1 minute before logout

    private destroy$ = new Subject<void>();
    private userActivity$ = new Subject<void>();
    private timer: ReturnType<typeof setTimeout> | null = null;
    private logoutTimer: ReturnType<typeof setTimeout> | null = null;
    private warningDisplayed = false;

    constructor(
        private router: Router,
        private ngZone: NgZone,
        private authService: AuthService
    ) {}

    initializeInactivityTimer(): void {
        const activityEvents = ['mousedown', 'mousemove', 'keydown', 'scroll', 'touchstart'];

        this.ngZone.runOutsideAngular(() => {
            merge(...activityEvents.map(evt => fromEvent(document, evt)))
                .pipe(debounceTime(1000), takeUntil(this.destroy$))
                .subscribe(() => {
                    this.ngZone.run(() => {
                        this.userActivity$.next();
                    });
                });
        });

        this.userActivity$.pipe(takeUntil(this.destroy$)).subscribe(() => {
            this.resetTimer();
        });

        this.resetTimer();
    }

    private resetTimer(): void {
        if (this.timer) clearTimeout(this.timer);
        if (this.logoutTimer) clearTimeout(this.logoutTimer);
        
        this.warningDisplayed = false;

        this.timer = setTimeout(() => {
            if (!this.warningDisplayed) {
                this.showWarning();
            }
        }, this.TIMEOUT_DURATION - this.WARNING_BEFORE);

        this.logoutTimer = setTimeout(() => {
            if (!this.warningDisplayed) {
                this.logout();
            }
        }, this.TIMEOUT_DURATION);
    }

    private showWarning(): void {
        if (!this.authService.isLoggedIn()) {
            return;
        }

        this.warningDisplayed = true;
        const remainingMinutes = Math.floor(this.WARNING_BEFORE / 60000);

        if (confirm(`You will be logged out in ${remainingMinutes} minute(s) due to inactivity. Do you want to stay logged in?`)) {
            this.userActivity$.next(); // Reset the timer if the user wants to stay
        }
    }

    private logout(): void {
        if (!this.authService.isLoggedIn()) {
            return;
        }
        this.authService.logout();
        this.router.navigate(['/login']);
    }

    destroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
        this.userActivity$.complete();
        if (this.timer) clearTimeout(this.timer);
        if (this.logoutTimer) clearTimeout(this.logoutTimer);
    }
}
