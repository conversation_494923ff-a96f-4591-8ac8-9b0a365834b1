"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SignatureService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const _types_1 = require("../../utils/@types");
let SignatureService = class SignatureService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createSignatureDto) {
        const signatures = await this.prisma.signature.findMany();
        if (signatures.length >= 1) {
            throw new common_1.ConflictException('There can only be one DG signature at time');
        }
        const data = await this.prisma.signature.create({ data: createSignatureDto });
        return new _types_1.ApiResponse(true, 'Signature created successfully', data);
    }
    async findAll(page = 1, pageSize = 10) {
        const data = await this.prisma.signature.findMany();
        const total = await this.prisma.signature.count();
        return new _types_1.ApiResponse(true, 'Users retrieved successfully', {
            data: data,
            total,
            pageSize
        });
    }
    async findOne(id) {
        const data = await this.prisma.signature.findUnique({ where: { id } });
        if (!data) {
            throw new common_1.NotFoundException(`Signature with ID ${id} not found.`);
        }
        return new _types_1.ApiResponse(true, 'Signature fetched successfully', data);
    }
    async update(id, updateSignatureDto) {
        const existingSignature = await this.prisma.signature.findUnique({ where: { id } });
        if (!existingSignature) {
            throw new common_1.NotFoundException(`Signature with ID ${id} not found.`);
        }
        const data = await this.prisma.signature.update({ where: { id }, data: updateSignatureDto });
        return new _types_1.ApiResponse(true, 'Signature updated successfully', data);
    }
    async remove(id) {
        const existingSignature = await this.prisma.signature.findUnique({ where: { id } });
        if (!existingSignature) {
            throw new common_1.NotFoundException(`Signature with ID ${id} not found.`);
        }
        const data = await this.prisma.signature.delete({ where: { id } });
        return new _types_1.ApiResponse(true, 'Signature deleted successfully', data);
    }
};
exports.SignatureService = SignatureService;
exports.SignatureService = SignatureService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], SignatureService);
//# sourceMappingURL=signature.service.js.map