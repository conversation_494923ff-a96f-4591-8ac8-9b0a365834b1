import { Body, Controller, Delete, Get, NotFoundException, Param, Patch, Post, Request, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiParam, ApiTags } from '@nestjs/swagger';
import { ApproveDTO, CreateReportDto, UpdateReportDto } from './dto';
import { ReportsService } from './reports.service';
import { RolesGuard } from 'src/utils/guards/role.guard';
import { Roles } from 'src/utils/decorators/role.decorator';
import { Role } from '@prisma/client';

@ApiTags('Reports')
@Controller('reports')
@UseGuards(RolesGuard)
export class ReportsController {
    constructor(private readonly reportsService: ReportsService) { }

    @Post()
    @ApiOperation({ summary: 'Create a new report' })
    @ApiResponse({ status: 201, description: 'The report has been successfully created.' })
    @ApiResponse({ status: 400, description: 'Bad Request.' })
    async create(@Body() createReportDto: CreateReportDto) {
        return this.reportsService.create(createReportDto);
    }

    @Get()
    @ApiOperation({ summary: 'Get all reports' })
    @ApiResponse({ status: 200, description: 'Return all reports.' })
    async findAll() {
        return this.reportsService.findAll();
    }

    @Get(':id')
    @ApiOperation({ summary: 'Get a report by id' })
    @ApiParam({ name: 'id', description: 'Report ID' })
    @ApiResponse({ status: 200, description: 'Return the report.' })
    @ApiResponse({ status: 404, description: 'Report not found.' })
    async findOne(@Param('id') id: string) {
        return this.reportsService.findOne(id);
    }

    @Patch('submit/:id')
    @ApiOperation({ summary: 'Submit report for approval' })
    async submit(@Param('id') id: string, @Request() req: Request & { user: any }){
        return this.reportsService.submit(id, req)
    }

    @Roles(Role.DG)
    @Patch('approve/:id')
    @ApiOperation({ summary: 'DG: approve a report' })
    async approve(@Param('id') id: string, @Body() data: ApproveDTO, @Request() req: Request & { user: any }){
        return this.reportsService.approve(id, data, req)
    }

    @Roles(Role.DG)
    @Patch('revert/:id')
    @ApiOperation({ summary: 'DG: revert a report' })
    async revert(@Param('id') id: string, @Body() data: ApproveDTO, @Request() req: Request & { user: any }){
        return this.reportsService.revert(id, data, req)
    }

    @Patch(':id')
    @ApiOperation({ summary: 'Update a report' })
    @ApiParam({ name: 'id', description: 'Report ID' })
    @ApiResponse({ status: 200, description: 'The report has been successfully updated.' })
    @ApiResponse({ status: 404, description: 'Report not found.' })
    async update(@Param('id') id: string, @Body() updateReportDto: UpdateReportDto) {
        return this.reportsService.update(id, updateReportDto);
    }

    @Delete(':id')
    @ApiOperation({ summary: 'Delete a report' })
    @ApiParam({ name: 'id', description: 'Report ID' })
    @ApiResponse({ status: 200, description: 'The report has been successfully deleted.' })
    @ApiResponse({ status: 404, description: 'Report not found.' })
    async remove(@Param('id') id: string) {
        return this.reportsService.remove(id);
    }
}
