"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateOccurrenceDto = exports.UpdateAircraftDto = exports.CreateOccurrenceDto = exports.AircraftDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const client_1 = require("@prisma/client");
const swagger_1 = require("@nestjs/swagger");
class AircraftDto {
}
exports.AircraftDto = AircraftDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Boeing 737', description: 'The model of the aircraft.' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AircraftDto.prototype, "model", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Boeing', description: 'The manufacturer of the aircraft.' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AircraftDto.prototype, "manufacturer", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Airline A', description: 'The operator of the aircraft.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AircraftDto.prototype, "operator", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'N12345', description: 'The registration mark of the aircraft.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AircraftDto.prototype, "registrationMark", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'USA', description: 'The nationality of the operator.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AircraftDto.prototype, "operatorNationality", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '2024-11-21T10:30:00Z', description: 'The intended landing date and time.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        return value ? new Date(value).toISOString() : null;
    }),
    __metadata("design:type", Date)
], AircraftDto.prototype, "intendedLandingDateTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'JFK Airport', description: 'The intended landing point.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AircraftDto.prototype, "intendedLandingPoint", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'LAX Airport', description: 'The last departure point.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AircraftDto.prototype, "lastDeparturePoint", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 5, description: 'Number of crew members on board.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], AircraftDto.prototype, "crewOnBoard", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 2, description: 'Number of crew members injured.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], AircraftDto.prototype, "crewInjured", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 1, description: 'Number of crew members perished.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], AircraftDto.prototype, "crewPerished", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 150, description: 'Number of passengers on board.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], AircraftDto.prototype, "passengersOnBoard", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 10, description: 'Number of passengers injured.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], AircraftDto.prototype, "passengersInjured", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 5, description: 'Number of passengers perished.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], AircraftDto.prototype, "passengersPerished", void 0);
class CreateOccurrenceDto {
}
exports.CreateOccurrenceDto = CreateOccurrenceDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'John Doe', description: 'The name of the reporter.' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOccurrenceDto.prototype, "reporterName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '<EMAIL>', description: 'The email address of the reporter.' }),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], CreateOccurrenceDto.prototype, "reporterEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '************', description: 'The phone number of the reporter.' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOccurrenceDto.prototype, "reporterPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Jane Smith', description: 'The name of the pilot in command.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOccurrenceDto.prototype, "pilotInCommandName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '<EMAIL>', description: 'The email address of the pilot in command.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], CreateOccurrenceDto.prototype, "pilotInCommandEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '************', description: 'The phone number of the pilot in command.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOccurrenceDto.prototype, "pilotInCommandPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [AircraftDto], description: 'List of involved aircraft details.' }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => AircraftDto),
    __metadata("design:type", Array)
], CreateOccurrenceDto.prototype, "involvedAircraft", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 0, description: 'Number of ground people perished.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateOccurrenceDto.prototype, "groundPeoplePerished", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 0, description: 'Number of ground people injured.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateOccurrenceDto.prototype, "groundPeopleInjured", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Clear', description: 'General weather conditions at the time of the occurrence.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOccurrenceDto.prototype, "generalWeatherConditions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Few clouds', description: 'Sky coverage at the time of the occurrence.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOccurrenceDto.prototype, "skyCoverage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.MeteologicalCondition, description: 'Meteorological conditions (e.g., IMC or VMC).' }),
    (0, class_validator_1.IsEnum)(client_1.MeteologicalCondition),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateOccurrenceDto.prototype, "meteologicalCondition", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.FlightRules, description: 'Flight rules (e.g., IFR or VFR).' }),
    (0, class_validator_1.IsEnum)(client_1.FlightRules),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateOccurrenceDto.prototype, "flightRules", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '2024-11-21T10:30:00Z', description: 'The date and time of the occurrence.' }),
    (0, class_transformer_1.Transform)(({ value }) => {
        return value ? new Date(value).toISOString() : null;
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Date)
], CreateOccurrenceDto.prototype, "occurrenceTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.OperationType, description: 'Type of operation being conducted.' }),
    (0, class_validator_1.IsEnum)(client_1.OperationType),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateOccurrenceDto.prototype, "operationType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Startn engine(s)', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOccurrenceDto.prototype, "flightPhase", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '34.0522', description: 'Latitude of the occurrence location.' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateOccurrenceDto.prototype, "latitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '-118.2437', description: 'Longitude of the occurrence location.' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateOccurrenceDto.prototype, "longitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Los Angeles, CA', description: 'Specific location of the occurrence.' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateOccurrenceDto.prototype, "occurrenceLocation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'None', description: 'Dangerous goods carried on board.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateOccurrenceDto.prototype, "dangerousGoodCarriedOnBoard", void 0);
class UpdateAircraftDto {
}
exports.UpdateAircraftDto = UpdateAircraftDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], UpdateAircraftDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateAircraftDto.prototype, "manufacturer", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateAircraftDto.prototype, "model", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateAircraftDto.prototype, "registrationMark", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateAircraftDto.prototype, "operator", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateAircraftDto.prototype, "operatorNationality", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateAircraftDto.prototype, "lastDeparturePoint", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateAircraftDto.prototype, "intendedLandingPoint", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", Date)
], UpdateAircraftDto.prototype, "intendedLandingDateTime", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateAircraftDto.prototype, "crewOnBoard", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateAircraftDto.prototype, "crewInjured", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateAircraftDto.prototype, "crewPerished", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateAircraftDto.prototype, "passengersOnBoard", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateAircraftDto.prototype, "passengersInjured", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateAircraftDto.prototype, "passengersPerished", void 0);
class UpdateOccurrenceDto {
}
exports.UpdateOccurrenceDto = UpdateOccurrenceDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ enum: client_1.OccurrenceType }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.OccurrenceType),
    __metadata("design:type", String)
], UpdateOccurrenceDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], UpdateOccurrenceDto.prototype, "occurrenceCategory_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateOccurrenceDto.prototype, "reporterName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], UpdateOccurrenceDto.prototype, "reporterEmail", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateOccurrenceDto.prototype, "reporterPhone", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateOccurrenceDto.prototype, "pilotInCommandName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], UpdateOccurrenceDto.prototype, "pilotInCommandEmail", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateOccurrenceDto.prototype, "pilotInCommandPhone", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateOccurrenceDto.prototype, "occurrenceLocation", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateOccurrenceDto.prototype, "latitude", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateOccurrenceDto.prototype, "longitude", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", Date)
], UpdateOccurrenceDto.prototype, "occurrenceTime", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.OperationType),
    __metadata("design:type", String)
], UpdateOccurrenceDto.prototype, "operationType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.FlightRules),
    __metadata("design:type", String)
], UpdateOccurrenceDto.prototype, "flightRules", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.MeteologicalCondition),
    __metadata("design:type", String)
], UpdateOccurrenceDto.prototype, "meteologicalCondition", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateOccurrenceDto.prototype, "flightPhase", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: [UpdateAircraftDto],
        description: 'List of involved aircraft'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => UpdateAircraftDto),
    __metadata("design:type", Array)
], UpdateOccurrenceDto.prototype, "involvedAircraft", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Occurrence Status',
        enum: client_1.OccurrenceStatus
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.OccurrenceStatus),
    __metadata("design:type", String)
], UpdateOccurrenceDto.prototype, "status", void 0);
//# sourceMappingURL=index.js.map