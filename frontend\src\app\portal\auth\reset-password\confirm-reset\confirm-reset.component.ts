import { Component } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormsModule, ReactiveFormsModule, AbstractControl } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink, RouterModule } from '@angular/router';
import { MessageService } from 'primeng/api';
import { AuthService } from '../../auth.service';
import { CommonModule } from '@angular/common';
import { ToastModule } from 'primeng/toast';

@Component({
  selector: 'app-confirm-reset',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ToastModule,
    RouterModule
  ],
  providers: [MessageService],
  templateUrl: './confirm-reset.component.html',
})
export class ConfirmResetComponent {
  resetForm: FormGroup;
  showPassword = false;
  loading = false;
  token: string | null = null

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private messageService: MessageService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.resetForm = this.fb.group({
      newPassword: ['', [
        Validators.required, 
        Validators.minLength(8),
        Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/)
      ]],
      confirmPassword: ['', [Validators.required]]
    }, { 
      validators: this.passwordMatchValidator 
    });
  }

  passwordMatchValidator(form: AbstractControl) {
    const newPassword = form.get('newPassword');
    const confirmPassword = form.get('confirmPassword');
  
    // Only validate if both fields have values
    if (newPassword?.value && confirmPassword?.value) {
      const passwordsMatch = newPassword.value === confirmPassword.value;
      
      if (!passwordsMatch) {
        confirmPassword.setErrors({ passwordMismatch: true });
      } else {
        confirmPassword.setErrors(null);
      }
    }
  
    return null;
  }

  ngOnInit(): void {
    this.token = this.route.snapshot.queryParamMap.get('token')
    if(!this.token){
      this.router.navigate(['/portal/auth/login'])
    }
  }

  togglePassword(): void {
    this.showPassword = !this.showPassword;
  }

  async onSubmit(): Promise<void> {
    if (this.resetForm.valid && this.token) {
      this.loading = true;
      const { newPassword } = this.resetForm.value;
      try {
        await this.authService.confirmResetPassword(this.token, newPassword);
        
        this.messageService.add({
          severity: "success",
          summary: "Password reset successful",
          detail: "Your password has been reset successfully. Redirecting to login..."
        });
  
        await new Promise(resolve => setTimeout(resolve, 2000));
        this.authService.logout()
      } catch (error: any) {
        const errorMessage = error.response?.data?.message || error.message || 'An unexpected error occurred';
        
        this.messageService.add({
          severity: 'error',
          summary: 'Failed to reset password',
          detail: errorMessage,
        });
      } finally {
        this.loading = false;
      }
    } else {
      Object.keys(this.resetForm.controls).forEach((key) => {
        const control = this.resetForm.get(key);
        if (control?.invalid) {
          control.markAsTouched();
        }
      });
      
      this.messageService.add({
        severity: 'warn',
        summary: 'Form Incomplete',
        detail: 'Please fill in all required fields.',
      });
    }
  }
  
}
