<div class="w-full h-full p-8 flex flex-col gap-4 overflow-scroll">
    <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold text-gray-700">Occurrences</h2>
    </div>
    <div class="pt-12">
        <div class="flex items-center gap-4">
            <div class="relative w-full">
                <i class="pi pi-search text-lg absolute top-2 left-3 text-gray-700"></i>
                <input
                    [(ngModel)]="searchQuery"
                    (input)="search()"
                    class="outline-none bg-transparent py-2 px-3 pl-10 border border-gray-300 rounded-lg  text-gray-700 w-full focus:ring-1 focus:ring-primary-500"
                    type="search" placeholder="Search occurences...">
            </div>
            <!-- <button
                class="border border-gray-300 rounded-lg text-gray-700 flex items-center gap-1 p-2 px-3 active:ring ring-primary-500">
                <i class="pi pi-filter"></i>
                Filter
            </button> -->
        </div>

        <!-- table -->
        <div class="mt-8">
            <p-table [value]="occurrences" [paginator]="true" [rows]="10" [showCurrentPageReport]="true"
                [totalRecords]="totalOccurrences" (onPage)="onPageChange($event)"
                currentPageReportTemplate="Showing {first} to {last} of {totalRecords} occurrences(s)"
                [rowsPerPageOptions]="[10, 25, 50]">
                <ng-template pTemplate="header">
                    <tr>
                        <th class="font-medium text-gray-700">Reference No</th>
                        <th class="font-medium text-gray-700">Submitted On</th>
                        <th class="font-medium text-gray-700">Reporter Details</th>
                        <th class="font-medium text-gray-700">Type</th>
                        <th class="font-medium text-gray-700">Category</th>
                        <th class="font-medium text-gray-700">Status</th>
                        <th class="font-medium text-gray-700">Actions</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-occurrence>
                    <tr class="">
                        <td class="font-normal border-b text-sm">{{occurrence.referenceNumber??"N/A"}}</td>
                        <td class="font-normal border-b">{{formatDate(occurrence.createdAt)}}</td>
                        <td class="font-normal border-b">
                            <div class="flex flex-col gap-1">
                                <p class="text-sm"><b>Name: </b> {{occurrence.reporterName}} </p>
                                <p class="text-sm"><b>Email: </b> {{occurrence.reporterEmail}} </p>
                                <p class="text-sm"><b>Phone: </b> {{occurrence.reporterPhone}} </p>
                            </div>
                        </td>
                        <td class="font-normal border-b">
                            @if (occurrence.type) {
                                @if (occurrence.type === 'INCIDENT') {
                                    Incident
                                }
                                @if (occurrence.type === 'SERIOUS_INCIDENT') {
                                    Serious Incident
                                }
                                @if (occurrence.type === 'INCIDENT_TO_BE_INVESTIGATED') {
                                    Incident to be Investigated
                                }
                                @if (occurrence.type === 'MAJOR_ACCIDENT') {
                                    Major Accident
                                }
                                @if (occurrence.type === 'ABNORMAL_OCCURRENCE') {
                                    Abnomal Occurrence
                                }
                                @if (occurrence.type === 'ACCIDENT') {
                                    Accident
                                }
                            }
                        </td>
                        <td class="font-normal border-b">{{occurrence.occurrenceCategory?.category ||'N/A'}}</td>
                        <td class="font-normal border-b">
                            @if (occurrence.type) {
                                <div class="rounded-full text-center text-sm font-medium px-4 py-2 bg-primary-50 text-primary-400">Classified</div>                               
                            } @else {
                                <div class="rounded-full text-center text-sm font-medium px-4 py-2 bg-primary-50 text-red-400">Unclassified</div>                               
                            }
                        </td>
                        <td class="font-normal border-b " class="flex items-center gap-2">
                            <a href="/dg/occurrences/view/{{occurrence.id}}"
                                class="bg-primary-50 text-primary-500 py-2 px-3 rounded-lg text-lg active:scale-95 active:ring ring-primary-200"
                                title="View occurrence information">
                                <i class="pi pi-info-circle"></i>
                            </a>
                            <a href="/dg/occurrences/edit/{{occurrence.id}}"
                                class="bg-primary-50 text-primary-500 py-2 px-3 rounded-lg text-lg active:scale-95 active:ring ring-primary-200"
                                title="edit occurrence information">
                                <i class="pi pi-user-edit"></i>
                            </a>
                            
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="5">No Occurrences</td>
                    </tr>
                </ng-template>
            </p-table>
            <p-confirmDialog></p-confirmDialog>
            <p-toast position="top-right"></p-toast>
        </div>
    </div>
</div>