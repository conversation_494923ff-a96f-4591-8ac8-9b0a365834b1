<div class="w-full h-full p-8 flex flex-col gap-4 overflow-scroll">
    <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold text-gray-700">System Logs</h2>
        
    </div>
    <div class="pt-12">
        <div class="flex items-center gap-4">
            <div class="relative w-full">
                <i class="pi pi-search text-lg absolute top-2 left-3 text-gray-700"></i>
                <input [(ngModel)]="searchQuery"
                    class="outline-none bg-transparent py-2 px-3 pl-10 border border-gray-300 rounded-lg  text-gray-700 w-full focus:ring-1 focus:ring-primary-500"
                    type="search" (input)="search()" placeholder="Search logs..">
            </div>
            
        </div>

        <!-- table -->
        <div class="mt-8">
            <p-table [value]="logs" [lazy]="true" [paginator]="true" [rows]="pageSize" [showCurrentPageReport]="true"
                [totalRecords]="totalLogs" [first]="first" (onPage)="onPageChange($event)"
                currentPageReportTemplate="Showing {first} to {last} of {totalRecords} logs"
                [rowsPerPageOptions]="[10, 25, 50, 100]">
                <ng-template pTemplate="header">
                    <tr>
                        <th class="font-medium text-gray-700">Date</th>
                        <th class="font-medium text-gray-700">Action</th>
                        <th class="font-medium text-gray-700">Url</th>
                        <th class="font-medium text-gray-700">IP</th>
                        <th class="font-medium text-gray-700">OS</th>
                        <th class="font-medium text-gray-700">Browser</th>
                        <th class="font-medium text-gray-700">URL</th>
                        <th class="font-medium text-gray-700">Method</th>
                        <th class="font-medium text-gray-700">User</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-log>
                    <tr class="">
                        <td class="font-normal border-b">{{formatDate(log.createdAt)}}</td>
                        <td class="font-normal border-b">{{log.action}}</td>
                        <td class="font-normal border-b">{{log.sourceUrl}}</td>
                        <td class="font-normal border-b">{{log.sourceIpAddress}}</td>
                        <td class="font-normal border-b">{{log.sourceOS}}</td>
                        <td class="font-normal border-b">{{log.sourceBrowser}}</td>
                        <td class="font-normal border-b">{{log.url}}</td>
                        <td class="font-normal border-b">{{log.method}}</td>
                        <td class="font-normal border-b">{{log.user?.name??"N/A"}}</td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="5">No logs</td>
                    </tr>
                </ng-template>
            </p-table>
            
            <p-confirmDialog></p-confirmDialog>
            <p-toast position="top-right"></p-toast>
        </div>
    </div>
</div>