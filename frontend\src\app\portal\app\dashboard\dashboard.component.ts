// dashboard.component.ts
import { Component, OnInit } from '@angular/core';
import { ChartModule } from 'primeng/chart';
import { DropdownModule } from 'primeng/dropdown';
import { DashboardService } from './dashboard.service';
import { DashboardData, Occurrence, OccurrenceCategory, OccurrenceType } from '../../../util/@types';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [ChartModule, DropdownModule, FormsModule],
  templateUrl: './dashboard.component.html',
})
export class DashboardComponent implements OnInit {
  yearOptions: any[] = [];
  monthOptions: any[] = [];
  selectedYear: string | null = null;
  selectedMonth: string | null = null;

  pieData: any;
  typeBarData: any;
  categoryBarData: any;
  chartOptions: any;
  categoryChartOptions: any;

  dashboardData: DashboardData | null = null;

  constructor(private dashboardService: DashboardService) { }

  async ngOnInit() {
    await this.loadDashboardData();
    this.initializeChartOptions();
    this.initializeTimeFilters();
    this.updateCharts();
  }

  private initializeTimeFilters() {
    // Extract unique years and months from occurrences
    const years = new Set<string>();
    const months = new Set<string>();
    
    this.dashboardData?.occurrences.forEach(occurrence => {
      const date = new Date(occurrence.createdAt);
      years.add(date.getFullYear().toString());
      months.add(`${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`);
    });

    this.yearOptions = Array.from(years).map(year => ({ label: year, value: year }));
    this.monthOptions = Array.from(months).map(month => {
      const [year, monthNum] = month.split('-');
      const monthName = new Date(parseInt(year), parseInt(monthNum) - 1)
                        .toLocaleString('default', { month: 'long' });
      return { label: `${monthName} ${year}`, value: month };
    });
  }

  private initializeChartOptions() {
    const documentStyle = getComputedStyle(document.documentElement);
    const textColor = documentStyle.getPropertyValue('--text-color');
    
    // Common chart options
    this.chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              boxWidth: 12,
              padding: 20
            }
          }
        },
        scales: {
          x: {
            display: true,
            ticks: {
              autoSkip: false,
              maxRotation: 45,
              minRotation: 45
            },
            grid: {
              display: false // Hide grid lines for X-axis
            }
          },
          y: {
            stacked: true,
            beginAtZero: true,
            ticks: {
              precision: 0
            },
            grid: {
              display: true // Show grid lines for Y-axis
            }
          }
        },
        // Control bar thickness and spacing
        datasets: {
          bar: {
            barPercentage: 0.6, // Adjust bar width (60% of available space)
            categoryPercentage: 0.8 // Adjust spacing between bars (80% of available space)
          }
        }
      };
    // Special options for category chart
    this.categoryChartOptions = {
      ...this.chartOptions,
      scales: {
        ...this.chartOptions.scales,
        x: {
          ticks: {
            color: textColor,
            autoSkip: false,
            maxRotation: 45,
            minRotation: 45
          }
        }
      }
    };
  }

  private initializeYearOptions() {
    if (!this.dashboardData?.occurrences.length) return;
    
    const years = new Set<string>();
    this.dashboardData.occurrences.forEach(occurrence => {
      const date = new Date(occurrence.createdAt);
      years.add(date.getFullYear().toString());
    });

    this.yearOptions = Array.from(years)
      .sort((a, b) => parseInt(b) - parseInt(a))
      .map(year => ({ label: year, value: year }));
  }

  private updateMonthOptions() {
    if (!this.selectedYear || !this.dashboardData?.occurrences.length) {
      this.monthOptions = [];
      this.selectedMonth = null;
      return;
    }

    const months = new Set<string>();
    this.dashboardData.occurrences
      .filter(occurrence => {
        const date = new Date(occurrence.createdAt);
        return date.getFullYear().toString() === this.selectedYear;
      })
      .forEach(occurrence => {
        const date = new Date(occurrence.createdAt);
        months.add(date.getMonth().toString());
      });

    this.monthOptions = Array.from(months)
      .sort((a, b) => parseInt(a) - parseInt(b))
      .map(month => {
        const monthName = new Date(parseInt(this.selectedYear!), parseInt(month))
          .toLocaleString('default', { month: 'long' });
        return { 
          label: monthName, 
          value: `${this.selectedYear}-${(parseInt(month) + 1).toString().padStart(2, '0')}` 
        };
      });
  }

  onYearChange() {
    this.updateMonthOptions();
    this.selectedMonth = null;
    this.updateCharts();
  }

  get filteredOccurrences(): Occurrence[] {
    if (!this.dashboardData?.occurrences) return [];
    
    return this.dashboardData.occurrences.filter(occurrence => {
      const date = new Date(occurrence.createdAt);
      
      if (this.selectedYear && date.getFullYear().toString() !== this.selectedYear) {
        return false;
      }
      
      if (this.selectedMonth) {
        const occurrenceMonth = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
        if (occurrenceMonth !== this.selectedMonth) {
          return false;
        }
      }
      
      return true;
    });
  }

  updateCharts() {
    this.updateTypeCharts();
    this.updateCategoryChart();
  }

  private updateTypeCharts() {
    const typeCounts = new Map<OccurrenceType, number>();
    
    this.filteredOccurrences.forEach(occurrence => {
      if (occurrence.type) {
        typeCounts.set(occurrence.type, (typeCounts.get(occurrence.type) || 0) + 1);
      }
    });

    const typeLabels = Array.from(typeCounts.keys());
    
    // Pie Chart Data
    this.pieData = {
      labels: typeLabels,
      datasets: [{
        data: Array.from(typeCounts.values()),
        backgroundColor: this.getChartColors(typeLabels.length)
      }]
    };

    // Type Bar Chart Data
    this.typeBarData = {
      labels: typeLabels,
      datasets: [{
        label: 'Occurrences',
        data: Array.from(typeCounts.values()),
        backgroundColor: this.getChartColors(typeLabels.length)
      }]
    };
  }

  private updateCategoryChart() {
    const categoryCounts = new Map<string, number>();
    
    this.filteredOccurrences.forEach(occurrence => {
      const rawCategory = occurrence.occurrenceCategory?.category || 'Uncategorized';
      const categoryLabel = rawCategory.split(':')[0].trim();
      categoryCounts.set(categoryLabel, (categoryCounts.get(categoryLabel) || 0) + 1);
    });

    const labels = Array.from(categoryCounts.keys());
    
    this.categoryBarData = {
      labels,
      datasets: [{
        label: 'Occurrences',
        data: Array.from(categoryCounts.values()),
        backgroundColor: this.getChartColors(labels.length)
      }]
    };
  }

  private getChartColors(count: number): string[] {
    const colors = [
      '#3b82f6', '#eab308', '#10b981', '#f97316', '#6366f1', 
      '#ef4444', '#8b5cf6', '#06b6d4', '#64748b', '#f59e0b'
    ];
    return colors.slice(0, count);
  }

  async loadDashboardData() {
    try {
      const response = await this.dashboardService.getAll();
      if (response.success) {
        this.dashboardData = response.data;
      }
    } catch (error) {
      console.error('Error loading dashboard data', error);
    }
  }
}