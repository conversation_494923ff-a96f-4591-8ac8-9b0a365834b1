"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AircraftController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const aircraft_service_1 = require("./aircraft.service");
const dto_1 = require("./dto");
const dto_2 = require("../../users/dto");
let AircraftController = class AircraftController {
    constructor(aircraftService) {
        this.aircraftService = aircraftService;
    }
    create(createAircraftDto) {
        return this.aircraftService.create(createAircraftDto);
    }
    async getAll(page, limit) {
        const pageNumber = parseInt(page, 10) || 1;
        const limitNumber = parseInt(limit, 10) || 10;
        return await this.aircraftService.findAll(pageNumber, limitNumber);
    }
    findOne(id) {
        return this.aircraftService.findOne(id);
    }
    update(id, updateAircraftDto) {
        return this.aircraftService.update(id, updateAircraftDto);
    }
    remove(id) {
        return this.aircraftService.remove(id);
    }
    search(searchDto) {
        const { query, page, limit } = searchDto;
        return this.aircraftService.search(query, page, limit);
    }
    searchManufacturers(query) {
        return this.aircraftService.searchManufacturers(query);
    }
    searchModels(query, manufacturer) {
        return this.aircraftService.searchModels(query, manufacturer);
    }
};
exports.AircraftController = AircraftController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new aircraft' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateAircraftDto]),
    __metadata("design:returntype", void 0)
], AircraftController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'get all aircrafts [paginated]' }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], AircraftController.prototype, "getAll", null);
__decorate([
    (0, common_1.Get)('id/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a single aircraft by ID' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AircraftController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update an aircraft' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateAircraftDto]),
    __metadata("design:returntype", void 0)
], AircraftController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete an aircraft' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AircraftController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, swagger_1.ApiOperation)({ summary: 'Search for aircrafts' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_2.SearchDto]),
    __metadata("design:returntype", void 0)
], AircraftController.prototype, "search", null);
__decorate([
    (0, common_1.Get)('manufacturers'),
    __param(0, (0, common_1.Query)('q')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AircraftController.prototype, "searchManufacturers", null);
__decorate([
    (0, common_1.Get)('models'),
    __param(0, (0, common_1.Query)('q')),
    __param(1, (0, common_1.Query)('manufacturer')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], AircraftController.prototype, "searchModels", null);
exports.AircraftController = AircraftController = __decorate([
    (0, swagger_1.ApiTags)('Aircraft Settings'),
    (0, common_1.Controller)('settings/aircraft'),
    __metadata("design:paramtypes", [aircraft_service_1.AircraftService])
], AircraftController);
//# sourceMappingURL=aircraft.controller.js.map