import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { PrismaService } from 'src/prisma/prisma.service';
import { MailService } from 'src/utils/mail/mail.service';
import { AuthenticationService } from 'src/auth/auth.service';
import { AuthenticationModule } from 'src/auth/auth.module';

@Module({
  imports: [AuthenticationModule],
  providers: [UsersService, PrismaService, AuthenticationService],
  controllers: [UsersController]
})
export class UsersModule {}
