/*
  Warnings:

  - The values [INCID,CINCID,ACCID] on the enum `OccurrenceType` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "OccurrenceType_new" AS ENUM ('ACCIDENT', 'SERIOUS_INCIDENT', 'INCIDENT_TO_BE_INVESTIGATED', 'INCIDENT');
ALTER TABLE "Occurrence" ALTER COLUMN "type" TYPE "OccurrenceType_new" USING ("type"::text::"OccurrenceType_new");
ALTER TYPE "OccurrenceType" RENAME TO "OccurrenceType_old";
ALTER TYPE "OccurrenceType_new" RENAME TO "OccurrenceType";
DROP TYPE "OccurrenceType_old";
COMMIT;
