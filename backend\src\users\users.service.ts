import { BadRequestException, Injectable, NotAcceptableException, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { ActivateAccountDto, CreateUserDto, ResetPasswordConfirmDto, ResetPasswordRequestDto, UpdateUserDto } from './dto';
import * as bcrypt from 'bcrypt';
import { MailService, MailType } from 'src/utils/mail/mail.service';
import { env } from 'src/utils/env';
import { ApiResponse } from 'src/utils/@types';
import { SmsService } from 'src/utils/sms/sms.service';
import { AccountStatus, Role, User } from '@prisma/client';
import { AuthenticationService } from 'src/auth/auth.service';
import { link } from 'fs';
import * as speakeasy from 'speakeasy';
import * as qrcode from 'qrcode';

@Injectable()
export class UsersService {
  constructor(
    private prisma: PrismaService,
    private mailService: MailService,
    private smsService: SmsService,
    private authService: AuthenticationService
  ) { }

  async createUser(
    createUserDto: CreateUserDto,
  ): Promise<ApiResponse<any>> {

    const existingUser = await this.prisma.user.findFirst({
      where: {
        OR: [
          { email: createUserDto.email },
          { telephone: createUserDto.telephone }
        ]
      }
    });

    if (existingUser) {
      throw new BadRequestException('User with this email or phone number already exists');
    }

    const passwordExpiryDate = new Date();
    passwordExpiryDate.setDate(passwordExpiryDate.getDate() + 90);

    const user = await this.prisma.user.create({
      data: {
        ...createUserDto,
        passwordExpiryDate,
        status: AccountStatus.PENDING,
        multiFactorEnabled: false,
      }
    });

    await this.authService.sendVerificationCode(user, 'ACTIVATION');

    delete user.password;
    delete user.resetToken;

    return new ApiResponse(
      true,
      'User created. Activation code sent via email and SMS.',
      { userId: user.id, email: user.email, telephone: user.telephone }
    );
  }

  async updateUser(
    userId: string,
    updateUserDto: UpdateUserDto,
  ): Promise<ApiResponse<any>> {

    const existingUser = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (!existingUser) {
      throw new NotFoundException('User not found');
    }

    if (updateUserDto.email) {
      const emailConflict = await this.prisma.user.findFirst({
        where: {
          email: updateUserDto.email,
          NOT: { id: userId }
        }
      });

      if (emailConflict) {
        throw new BadRequestException('Email is already in use');
      }
    }

    if (updateUserDto.telephone) {
      const phoneConflict = await this.prisma.user.findFirst({
        where: {
          telephone: updateUserDto.telephone,
          NOT: { id: userId }
        }
      });

      if (phoneConflict) {
        throw new BadRequestException('Phone number is already in use');
      }
    }

    const updatedUser = await this.prisma.user.update({
      where: { id: userId },
      data: updateUserDto
    });

    delete updatedUser.password;
    delete updatedUser.resetToken;

    return new ApiResponse(true, 'User updated successfully', updatedUser);
  }

  async activateUserAccount(
    userId: string,
  ): Promise<ApiResponse<any>> {

    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.password) {
      throw new NotAcceptableException('The user must set their password first')
    }

    const activatedUser = await this.prisma.user.update({
      where: { id: userId },
      data: {
        status: AccountStatus.ACTIVE,
        multiFactorEnabled: true
      }
    });

    this.mailService.sendMail(MailType.ACCOUNT_VERIFIED, {
      to: user.email,
      name: user.name,
      subject: 'Account activated | AAID Notification',
      values: {
        name: user.name
      }
    });

    await this.smsService.sendSMS(
      user.telephone,
      `Hello ${user.name},\nYour account has been activated by an administrator.\nAAID Team.`
    );

    return new ApiResponse(true, 'User account activated successfully', null);
  }

  async activateAccount(
    resetPasswordConfirmDto: ActivateAccountDto
  ): Promise<ApiResponse<any>> {
    const user = await this.prisma.user.findFirst({
      where: {
        resetToken: resetPasswordConfirmDto.token,
        resetTokenExpiry: { gt: new Date() }
      }
    });

    if (!user) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    const hashedPassword = await bcrypt.hash(resetPasswordConfirmDto.password, 10);

    await this.prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        resetToken: null,
        resetTokenExpiry: null,
        status: AccountStatus.ACTIVE,
        multiFactorEnabled: true,
        passwordExpiryDate: (() => {
          const newExpiry = new Date();
          newExpiry.setDate(newExpiry.getDate() + 90);
          return newExpiry;
        })()
      }
    });

    return this.generateTwoFactorSecret(user.id)
  }

  async deactivateUserAccount(
    userId: string,
  ): Promise<ApiResponse<any>> {

    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const deactivatedUser = await this.prisma.user.update({
      where: { id: userId },
      data: {
        status: AccountStatus.DEACTIVATED,
        multiFactorEnabled: false
      }
    });

    this.mailService.sendMail(MailType.ACCOUNT_DEACTIVATED, {
      to: user.email,
      name: user.name,
      subject: 'Account Deactivated | AAID Notification',
      values: {
        name: user.name
      }
    });

    await this.smsService.sendSMS(
      user.telephone,
      `Hello ${user.name},\nYour account has been deactivated by an administrator.\nAAID Team.`
    );

    return new ApiResponse(true, 'User account deactivated successfully', null);
  }

  async initiatePasswordReset(
    resetPasswordDto: ResetPasswordRequestDto
  ): Promise<ApiResponse<any>> {
    const user = await this.prisma.user.findUnique({
      where: { email: resetPasswordDto.email }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    await this.authService.sendVerificationCode(user, 'PASSWORD_RESET');

    return new ApiResponse(
      true,
      'Password reset code sent via email and SMS',
      { userId: user.id }
    );
  }

  async confirmPasswordReset(
    resetPasswordConfirmDto: ResetPasswordConfirmDto
  ): Promise<ApiResponse<any>> {
    const user = await this.prisma.user.findFirst({
      where: {
        resetToken: resetPasswordConfirmDto.token,
        resetTokenExpiry: { gt: new Date() }
      }
    });

    if (!user) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    const hashedPassword = await bcrypt.hash(resetPasswordConfirmDto.newPassword, 10);

    await this.prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        resetToken: null,
        resetTokenExpiry: null,
        passwordExpiryDate: (() => {
          const newExpiry = new Date();
          newExpiry.setDate(newExpiry.getDate() + 90);
          return newExpiry;
        })()
      }
    });

    this.mailService.sendMail(MailType.PASSWORD_RESET_CONFIRMATION, {
      to: user.email,
      name: user.name,
      subject: 'Password Reset | AAID Notification',
      values: {
        name: user.name
      }
    });

    await this.smsService.sendSMS(
      user.telephone,
      `Hello ${user.name},\nYour password has been successfully reset.\nAAID Team.`
    );

    return new ApiResponse(true, 'Password reset successful', null);
  }

  async generateTwoFactorSecret(userId: string): Promise<ApiResponse<{ qrCode: string }>> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const secret = speakeasy.generateSecret({
      name: `AAID Portal: ${user.email}`
    });

    await this.prisma.user.update({
      where: { id: userId },
      data: {
        multiFactorSecret: secret.base32,
        multiFactorEnabled: true
      }
    });

    const qrCode = await qrcode.toDataURL(secret.otpauth_url);

    this.mailService.sendMail(MailType.ACCOUNT_VERIFIED, {
      to: user.email,
      name: user.name,
      subject: 'Account activated | AAID Notification',
      values: {
        name: user.name,
        link: `${env.FE_URL}/portal/login`
      }
    });

    await this.smsService.sendSMS(
      user.telephone,
      `Hello ${user.name},\nYour account has been successfully activated.\nAAID Team.`
    );

    return new ApiResponse(true, '2FA secret generated', {
      qrCode,
      secret: secret.base32
    });
  }

  async verifyTwoFactorSetup(
    userId: string,
    token: string
  ): Promise<ApiResponse<null>> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.multiFactorSecret) {
      throw new BadRequestException('2FA not set up');
    }

    const isValid = speakeasy.totp.verify({
      secret: user.multiFactorSecret,
      encoding: 'base32',
      token: token
    });

    if (!isValid) {
      throw new NotAcceptableException('Invalid 2FA token');
    }

    return new ApiResponse(true, '2FA setup verified', null);
  }

  async disableTwoFactor(userId: string): Promise<ApiResponse<null>> {
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        multiFactorSecret: null,
        multiFactorEnabled: false
      }
    });

    return new ApiResponse(true, '2FA disabled', null);
  }

  async getAllUsers(
    filters?: Partial<User>,
    sortBy?: keyof User,
    page: number = 1,
    pageSize: number = 10
  ): Promise<ApiResponse<any>> {

    // Calculate pagination
    const skip = (page - 1) * pageSize;

    const users = await this.prisma.user.findMany({
      where: filters,
      orderBy: sortBy ? { [sortBy]: 'desc' } : { createdAt: 'desc' },
      skip,
      take: pageSize,
      select: {
        id: true,
        name: true,
        email: true,
        telephone: true,
        role: true,
        status: true,
        profilePicture: true,
        createdAt: true
      }
    });

    const totalUsers = await this.prisma.user.count({ where: filters });

    return new ApiResponse(true, 'Users retrieved successfully', {
      data: users,
      total: totalUsers,
      page
    });
  }

  async searchUsers(query: string, page: number, limit: number = 10) {
    console.log("searching")
    const results = await this.prisma.user.findMany({
      where: {
        OR: [
          { name: { contains: query, mode: 'insensitive' } },
          { email: { contains: query, mode: 'insensitive' } },
          { telephone: { contains: query, mode: 'insensitive' } },
        ]
      },
    })

    const total = await this.prisma.user.count({
      where: {
        OR: [
          { name: { contains: query, mode: 'insensitive' } },
          { email: { contains: query, mode: 'insensitive' } },
          { telephone: { contains: query, mode: 'insensitive' } },
        ]
      },
    })

    return new ApiResponse(true, 'Search results retrieved successfully', {
      data: results,
      total,
      page
    });

  }

  async getUser(id: string) {
    const user = await this.prisma.user.findUnique({
      where: {
        id
      }
    })

    if (!user) {
      throw new NotFoundException('User not found')
    }

    delete user.password
    delete user.resetToken
    return new ApiResponse(true, "", user)
  }

  async getInvestigators(){
    const investigators = await this.prisma.user.findMany({
      where: {
        role: Role.INVESTIGATOR
      },
      select: {
        id: true,
        name: true,
        email: true,
        telephone: true,
        role: true,
        createdAt: true,
        updatedAt: true
      }
    })

    return new ApiResponse(true, '', investigators)
  }
}
