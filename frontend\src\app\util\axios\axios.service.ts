import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import axios, { AxiosInstance } from 'axios';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class AxiosService {

  private axiosInstance: AxiosInstance;
  private axiosFilesInstace: AxiosInstance;

  constructor(private router: Router) {
    this.axiosInstance = axios.create({
      baseURL: environment.API_BASE_URL,
      timeout: 5000,
    });

    this.axiosFilesInstace = axios.create({
      baseURL: environment.FILE_SERVER_URL,
      timeout: 5000,
    });

    this.axiosInstance.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('jwt_token');
        if (token) {
          config.headers['Authorization'] = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response && (error.response.status === 401 || error.response.status === 403)) {
          localStorage.removeItem('jwt_token')
          this.router.navigate(['/portal/auth/login']);
        }
        return Promise.reject(error);
      }
    );
  }

  get axios() {
    return this.axiosInstance;
  }

  get fileAxios(){
    return this.axiosFilesInstace
  }
}
