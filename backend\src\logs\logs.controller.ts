import { Controller, Get, Query, ParseIntPipe, DefaultValuePipe, UseGuards } from '@nestjs/common';
import { LogsService } from './logs.service';
import { ApiQuery, ApiTags } from '@nestjs/swagger';
import { HTTP_Method, Logs, Role } from '@prisma/client';
import { RolesGuard } from 'src/utils/guards/role.guard';
import { Roles } from 'src/utils/decorators/role.decorator';

@UseGuards(RolesGuard)
@Roles(Role.ADMIN)
@ApiTags('Logs')
@Controller('logs')
export class LogsController {
  constructor(private readonly logsService: LogsService) {}

  @Get()
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'pageSize', required: false, type: Number })
  @ApiQuery({ name: 'action', required: false })
  @ApiQuery({ name: 'sourceUrl', required: false })
  @ApiQuery({ name: 'sourceIpAddress', required: false })
  @ApiQuery({ name: 'sourceOS', required: false })
  @ApiQuery({ name: 'sourceBrowser', required: false })
  @ApiQuery({ name: 'url', required: false })
  @ApiQuery({ name: 'method', required: false, enum: HTTP_Method })
  @ApiQuery({ name: 'userId', required: false })
  @ApiQuery({ name: 'sortBy', required: false })
  async getLogs(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) pageSize: number,
    @Query('action') action?: string,
    @Query('sourceUrl') sourceUrl?: string,
    @Query('sourceIpAddress') sourceIpAddress?: string,
    @Query('sourceOS') sourceOS?: string,
    @Query('sourceBrowser') sourceBrowser?: string,
    @Query('url') url?: string,
    @Query('method') method?: HTTP_Method,
    @Query('userId') userId?: string,
    @Query('sortBy') sortBy?: keyof Logs,
  ) {
    const filters = {
      action,
      sourceUrl,
      sourceIpAddress,
      sourceOS,
      sourceBrowser,
      url,
      method,
      userId,
    };

    Object.keys(filters).forEach(key => filters[key] === undefined && delete filters[key]);

    return this.logsService.getLogs(filters, sortBy, page, pageSize);
  }


  @Get('search')
  search(@Query('query') query: string){
    return this.logsService.search(query)
  }


  @Get('user/:userId')
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'pageSize', required: false, type: Number })
  @ApiQuery({ name: 'sortBy', required: false })
  async getUserLogs(
    @Query('userId') userId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number,
    @Query('sortBy') sortBy?: keyof Logs,
  ) {
    return this.logsService.getUserLogs(userId, sortBy, page, pageSize);
  }
}