import { PrismaService } from 'src/prisma/prisma.service';
import { ActivateAccountDto, CreateUserDto, ResetPasswordConfirmDto, ResetPasswordRequestDto, UpdateUserDto } from './dto';
import { MailService } from 'src/utils/mail/mail.service';
import { ApiResponse } from 'src/utils/@types';
import { SmsService } from 'src/utils/sms/sms.service';
import { User } from '@prisma/client';
import { AuthenticationService } from 'src/auth/auth.service';
export declare class UsersService {
    private prisma;
    private mailService;
    private smsService;
    private authService;
    constructor(prisma: PrismaService, mailService: MailService, smsService: SmsService, authService: AuthenticationService);
    createUser(createUserDto: CreateUserDto): Promise<ApiResponse<any>>;
    updateUser(userId: string, updateUserDto: UpdateUserDto): Promise<ApiResponse<any>>;
    activateUserAccount(userId: string): Promise<ApiResponse<any>>;
    activateAccount(resetPasswordConfirmDto: ActivateAccountDto): Promise<ApiResponse<any>>;
    deactivateUserAccount(userId: string): Promise<ApiResponse<any>>;
    initiatePasswordReset(resetPasswordDto: ResetPasswordRequestDto): Promise<ApiResponse<any>>;
    confirmPasswordReset(resetPasswordConfirmDto: ResetPasswordConfirmDto): Promise<ApiResponse<any>>;
    generateTwoFactorSecret(userId: string): Promise<ApiResponse<{
        qrCode: string;
    }>>;
    verifyTwoFactorSetup(userId: string, token: string): Promise<ApiResponse<null>>;
    disableTwoFactor(userId: string): Promise<ApiResponse<null>>;
    getAllUsers(filters?: Partial<User>, sortBy?: keyof User, page?: number, pageSize?: number): Promise<ApiResponse<any>>;
    searchUsers(query: string, page: number, limit?: number): Promise<ApiResponse<{
        data: {
            id: string;
            name: string;
            email: string;
            telephone: string;
            password: string | null;
            role: import("@prisma/client").$Enums.Role;
            profilePicture: string | null;
            multiFactorEnabled: boolean;
            multiFactorSecret: string | null;
            resetToken: string | null;
            resetTokenExpiry: Date | null;
            passwordExpiryDate: Date;
            status: import("@prisma/client").$Enums.AccountStatus;
            createdAt: Date;
            updatedAt: Date;
            lastLoginAt: Date | null;
        }[];
        total: number;
        page: number;
    }>>;
    getUser(id: string): Promise<ApiResponse<{
        id: string;
        name: string;
        email: string;
        telephone: string;
        password: string | null;
        role: import("@prisma/client").$Enums.Role;
        profilePicture: string | null;
        multiFactorEnabled: boolean;
        multiFactorSecret: string | null;
        resetToken: string | null;
        resetTokenExpiry: Date | null;
        passwordExpiryDate: Date;
        status: import("@prisma/client").$Enums.AccountStatus;
        createdAt: Date;
        updatedAt: Date;
        lastLoginAt: Date | null;
    }>>;
    getInvestigators(): Promise<ApiResponse<{
        id: string;
        name: string;
        email: string;
        telephone: string;
        role: import("@prisma/client").$Enums.Role;
        createdAt: Date;
        updatedAt: Date;
    }[]>>;
}
