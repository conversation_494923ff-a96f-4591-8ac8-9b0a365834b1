import { Injectable } from '@nestjs/common';
import { OccurrenceType } from '@prisma/client';
import { PrismaService } from 'src/prisma/prisma.service';
import { ApiResponse } from 'src/utils/@types';
import * as dayjs from 'dayjs';

@Injectable()
export class DashboardService {
    constructor(private prisma: PrismaService) {}

    async getAll() {
        // Get total occurrences and counts for the current month
        const currentMonth = dayjs().startOf('month').toDate();
        const lastMonth = dayjs().subtract(1, 'month').startOf('month').toDate();
        const endOfLastMonth = dayjs(lastMonth).endOf('month').toDate();

        const occurrences = await this.prisma.occurrence.count({
        });

        const lastMonthOccurrences = await this.prisma.occurrence.count({
            where: {
                createdAt: {
                    gte: lastMonth,
                    lte: endOfLastMonth,
                },
            },
        });

        const incidents = await this.prisma.occurrence.count({
            where: {
                type: OccurrenceType.INCIDENT,
            },
        });

        const lastMonthIncidents = await this.prisma.occurrence.count({
            where: {
                type: OccurrenceType.INCIDENT,
                createdAt: {
                    gte: lastMonth,
                    lte: endOfLastMonth,
                },
            },
        });

        const seriousIncidents = await this.prisma.occurrence.count({
            where: {
                type: OccurrenceType.SERIOUS_INCIDENT,
            },
        });

        const lastMonthSeriousIncidents = await this.prisma.occurrence.count({
            where: {
                type: OccurrenceType.SERIOUS_INCIDENT,
                createdAt: {
                    gte: lastMonth,
                    lte: endOfLastMonth,
                },
            },
        });

        const incidentsToBeInvestigated = await this.prisma.occurrence.count({
            where: {
                type: OccurrenceType.INCIDENT_TO_BE_INVESTIGATED,
            },
        });

        const lastMonthIncidentsToBeInvestigated = await this.prisma.occurrence.count({
            where: {
                type: OccurrenceType.INCIDENT_TO_BE_INVESTIGATED,
                createdAt: {
                    gte: lastMonth,
                    lte: endOfLastMonth,
                },
            },
        });

        const reports = await this.prisma.report.count()
        const lastMonthReports = await this.prisma.report.count({
            where: {
                createdAt: {
                    gte: lastMonth,
                    lte: endOfLastMonth,
                },
            },
        });

        const accidents = await this.prisma.occurrence.count({
            where: {
               OR: [
                { type: OccurrenceType.ACCIDENT},
                { type: OccurrenceType.MAJOR_ACCIDENT}
               ],
            },
        });

        const lastMonthAccidents = await this.prisma.occurrence.count({
            where: {
               OR: [
                { type: OccurrenceType.ACCIDENT},
                { type: OccurrenceType.MAJOR_ACCIDENT}
               ],
                createdAt: {
                    gte: lastMonth,
                    lte: endOfLastMonth,
                },
            },
        });

        // Calculate percentage increases
        const calculateIncrease = (current: number, previous: number): number => {
            if (previous === 0) return current > 0 ? 100 : 0;
            return Number((((current - previous) / previous) * 100).toFixed(1));
        };

        // Get data for the last 7 months
        const lastSevenMonths = Array.from({ length: 7 }, (_, i) => {
            const startOfMonth = dayjs().subtract(i, 'month').startOf('month').toDate();
            const endOfMonth = dayjs().subtract(i, 'month').endOf('month').toDate();

            return {
                month: dayjs(startOfMonth).format('MMMM'),
                data: {
                    incidents: this.prisma.occurrence.count({
                        where: {
                            type: OccurrenceType.INCIDENT,
                            createdAt: {
                                gte: startOfMonth,
                                lte: endOfMonth,
                            },
                        },
                    }),
                    seriousIncidents: this.prisma.occurrence.count({
                        where: {
                            type: OccurrenceType.SERIOUS_INCIDENT,
                            createdAt: {
                                gte: startOfMonth,
                                lte: endOfMonth,
                            },
                        },
                    }),
                    incidentsToBeInvestigated: this.prisma.occurrence.count({
                        where: {
                            type: OccurrenceType.INCIDENT_TO_BE_INVESTIGATED,
                            createdAt: {
                                gte: startOfMonth,
                                lte: endOfMonth,
                            },
                        },
                    }),
                    accidents: this.prisma.occurrence.count({
                        where: {
                            type: OccurrenceType.ACCIDENT,
                            createdAt: {
                                gte: startOfMonth,
                                lte: endOfMonth,
                            },
                        },
                    }),
                },
            };
        });

        const chartData = await Promise.all(
            lastSevenMonths.map(async ({ month, data }) => {
                return {
                    [month]: {
                        incidents: await data.incidents,
                        seriousIncidents: await data.seriousIncidents,
                        incidentsToBeInvestigated: await data.incidentsToBeInvestigated,
                        accidents: await data.accidents,
                    },
                };
            })
        );

        const _occurrences = await this.prisma.occurrence.findMany({
            include: {
                occurrenceCategory: true
            }
        })

        return new ApiResponse(true, 'Dashboard data fetched successfully.', {
            total: {
                occurrences: {
                    total: occurrences,
                    increase: calculateIncrease(occurrences, lastMonthOccurrences),
                },
                incidents: {
                    total: incidents,
                    increase: calculateIncrease(incidents, lastMonthIncidents),
                },
                seriousIncidents: {
                    total: seriousIncidents,
                    increase: calculateIncrease(seriousIncidents, lastMonthSeriousIncidents),
                },
                incidentsToBeInvestigated: {
                    total: incidentsToBeInvestigated,
                    increase: calculateIncrease(incidentsToBeInvestigated, lastMonthIncidentsToBeInvestigated),
                },
                accidents: {
                    total: accidents,
                    increase: calculateIncrease(accidents, lastMonthAccidents),
                },
                reports: {
                    total: reports,
                    increase: calculateIncrease(reports, lastMonthReports),
                }
            },
            chart: Object.assign({}, ...chartData),
            occurrences: _occurrences 
        });
    }
}
