import { ApiProperty } from '@nestjs/swagger';
import { Role } from '@prisma/client';
import { 
  IsEmail, 
  IsEnum, 
  IsNotEmpty, 
  IsOptional, 
  IsString, 
  IsStrongPassword, 
  <PERSON><PERSON>ength,
  IsPhoneNumber 
} from 'class-validator';

export class CreateUserDto {
  @ApiProperty({ description: 'Name of the user', example: '<PERSON>' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'Email address of the user', example: '<EMAIL>' })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({ description: 'Phone number of the user', example: '+1234567890' })
  @IsNotEmpty()
  @IsPhoneNumber()
  telephone: string;

  @ApiProperty({ 
    description: 'Role assigned to the user', 
    enum: Role, 
    example: Role.INVESTIGATOR 
  })
  @IsNotEmpty()
  @IsEnum(Role)
  role: Role;

  @ApiProperty({ 
    description: 'Profile picture URL', 
    example: 'https://example.com/profile.jpg', 
    required: false 
  })
  @IsOptional()
  @IsString()
  profilePicture?: string;
}

export class UpdateUserDto {
  @ApiProperty({ description: 'Name of the user', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: 'Email address', required: false })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({ description: 'Phone number', required: false })
  @IsOptional()
  @IsPhoneNumber()
  telephone?: string;

  @ApiProperty({ 
    description: 'User role', 
    enum: Role, 
    required: false 
  })
  @IsOptional()
  @IsEnum(Role)
  role?: Role;

  @ApiProperty({ 
    description: 'Profile picture URL', 
    required: false 
  })
  @IsOptional()
  @IsString()
  profilePicture?: string;
}

export class ActivateAccountDto {
  @ApiProperty({ description: 'Activation token' })
  @IsNotEmpty()
  @IsString()
  token: string;

  @ApiProperty({ description: 'New password' })
  @IsNotEmpty()
  @IsStrongPassword()
  password: string;
}

export class ResetPasswordRequestDto {
  @ApiProperty({ description: 'Email to send reset link' })
  @IsNotEmpty()
  @IsEmail()
  email: string;
}

export class ResetPasswordConfirmDto {
  @ApiProperty({ description: 'Reset token' })
  @IsNotEmpty()
  @IsString()
  token: string;

  @ApiProperty({ description: 'New password' })
  @IsNotEmpty()
  @IsStrongPassword()
  newPassword: string;
}

export class SearchDto {
  @ApiProperty({ description: 'Search term', example: 'aircraft' })
  query: string;

  @ApiProperty({ description: 'Page number for pagination', example: 1 })
  page: number;

  @ApiProperty({ description: 'Number of results per page', example: 10 })
  limit: number;
}