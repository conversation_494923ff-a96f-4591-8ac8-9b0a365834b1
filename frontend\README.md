# AAID Notification System Frontend

AAID Notification System Frontend

## Prerequisites

- **Node.js** (>= 16.x)
- **Angular CLI** (>= 15.x): Install globally using:
  ```bash
  pnpm install -g @angular/cli
  ```

---

## Getting Started

### 1. Install dependencies
```bash
pnpm install
```

### 3. Run the development server
```bash
pnpm start
```

The app will be available at [http://localhost:4200](http://localhost:4200).

---

## Scripts

### Development
- **Start Development Server**: 
  ```bash
  ng serve
  ```
- **Run Tests**: 
  ```bash
  ng test
  ```

### Production
- **Build for Production**:
  ```bash
  ng build --prod
  ```
- **Run Linter**: 
  ```bash
  ng lint
  ```

---

## Project Structure

```
src/
├── app/                # Application modules and components
│   ├── components/     # Shared and reusable components
│   ├── services/       # Injectable services
│   └── app.module.ts   # Root application module
├── assets/             # Static assets (images, icons, etc.)
├── environments/       # Environment-specific configuration
├── main.ts             # Application entry point
└── styles.css          # Global styles
```

---

## Environment Variables

The project uses environment files for different configurations:
- `src/environments/environment.ts` (default)
- `src/environments/environment.prod.ts` (production)

---

## Build & Deployment

1. **Build the project**:
   ```bash
   ng build --prod
   ```
2. The compiled files will be in the `dist/` folder.
3. Serve the `dist` folder using any static file server (e.g., Nginx, Apache, or Firebase Hosting).

---