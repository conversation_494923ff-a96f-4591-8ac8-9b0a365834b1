"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const bcrypt = require("bcrypt");
const axios_1 = require("axios");
const categoriesData = require("./data/categories.json");
const prisma = new client_1.PrismaClient();
async function seedAdminUser() {
    const passwordExpiryDate = new Date();
    passwordExpiryDate.setDate(passwordExpiryDate.getDate() + 90);
    const adminData = {
        name: process.env.ADMIN_NAME,
        email: process.env.ADMIN_EMAIL,
        password: await bcrypt.hash(process.env.ADMIN_PASSWORD, 10),
        role: client_1.Role.ADMIN,
        telephone: process.env.ADMIN_TELEPHONE,
        status: client_1.AccountStatus.ACTIVE,
        passwordExpiryDate,
    };
    const existingAdmin = await prisma.user.findUnique({
        where: { email: adminData.email },
    });
    if (!existingAdmin) {
        await prisma.user.create({
            data: adminData,
        });
        console.log('Admin user created successfully.');
    }
    else {
        console.log('Admin user already exists.');
    }
}
async function seedAircraftData() {
    const url = 'https://www4.icao.int/doc8643/External/AircraftTypes';
    try {
        console.log('Fetching aircraft data...');
        const response = await axios_1.default.post(url);
        const data = response.data;
        if (!Array.isArray(data)) {
            console.error('Invalid aircraft data format. Expected an array.');
            return;
        }
        await prisma.aircraftData.deleteMany({});
        console.log('Seeding aircraft data...');
        for (const aircraft of data) {
            try {
                const { ModelFullName, Description, WTC, WTG, Designator, ManufacturerCode, AircraftDescription, EngineCount, EngineType, } = aircraft;
                await prisma.aircraftData.create({
                    data: {
                        modelFullName: ModelFullName,
                        description: Description,
                        wtc: WTC,
                        wtg: WTG,
                        designator: Designator,
                        manufacturerCode: ManufacturerCode,
                        aircraftDescription: AircraftDescription,
                        engineCount: parseInt(EngineCount, 10),
                        engineType: EngineType,
                    },
                });
                console.log(`Seeded aircraft: ${ModelFullName}`);
            }
            catch (error) {
                console.error(`Error seeding aircraft: ${aircraft.ModelFullName}`, error.message);
            }
        }
        console.log('Aircraft data seeding completed successfully.');
    }
    catch (error) {
        console.error('Error fetching aircraft data:', error.message);
    }
}
async function seedCategoriesData() {
    console.log('Seeding categories data');
    for (const item of categoriesData) {
        try {
            const cat = await prisma.occurrenceCategory.upsert({
                create: {
                    category: item.description,
                    description: item.detailed,
                    explanation: item.explanation
                },
                where: {
                    category: item.description
                },
                update: {
                    description: item.detailed,
                    explanation: item.explanation
                }
            });
            console.log(`seeded category: ${cat.category}`);
        }
        catch (error) {
        }
    }
}
async function seedCountriesData() {
    try {
        console.log('fetching from REST countries');
        const response = await axios_1.default.get('https://restcountries.com/v3.1/all?fields=name,flags');
        const data = response.data;
        if (!Array(data)) {
            console.log('Invalid countries data format');
        }
        for (const country of data) {
            try {
                await prisma.countriesInfo.create({
                    data: {
                        name: country.name.common,
                        flag: country.flags.png
                    }
                });
                console.log('Seeded country ' + country.name.common);
            }
            catch (error) {
                console.log('Error: ' + error);
            }
        }
    }
    catch (error) {
        console.log('Error: ' + error);
    }
}
async function main() {
    try {
        await seedAdminUser();
        await seedAircraftData();
        await seedCategoriesData();
        await seedCountriesData();
    }
    catch (error) {
        console.error('Seeding failed:', error.message);
        process.exit(1);
    }
    finally {
        await prisma.$disconnect();
    }
}
main();
//# sourceMappingURL=seed.js.map