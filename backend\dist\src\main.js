"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const swagger_1 = require("@nestjs/swagger");
const express_1 = require("express");
const morgan = require("morgan");
const app_module_1 = require("./app.module");
const auth_guard_1 = require("./auth/auth.guard");
const env_1 = require("./utils/env");
const corsConfig = {
    origin: '*',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    optionsSuccessStatus: 200,
};
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.use((0, express_1.urlencoded)());
    app.use((0, express_1.json)({ limit: '50mb' }));
    app.setGlobalPrefix('api/v1');
    const config = new swagger_1.DocumentBuilder()
        .addBearerAuth({
        type: 'http',
        scheme: 'bearer',
        in: 'Header',
        bearerFormat: 'JWT',
    }, 'Bearer')
        .setTitle('AAID Notification System API')
        .setDescription('API Endpoints for v1 AAID Notification System')
        .setVersion('1.0')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api-docs', app, document);
    app.enableCors(corsConfig);
    app.useGlobalPipes(new common_1.ValidationPipe({
        transform: true,
    }));
    app.useGlobalGuards(new auth_guard_1.AuthGuard());
    app.use(morgan('dev'));
    await app.listen(env_1.env.PORT);
    common_1.Logger.log(`Bootstrap  Server running on port ${env_1.env.PORT}`, 'NestApplication');
}
bootstrap();
//# sourceMappingURL=main.js.map