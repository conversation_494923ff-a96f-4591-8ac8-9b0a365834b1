"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggerMiddleware = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const ua_parser_js_1 = require("ua-parser-js");
const jwt_1 = require("@nestjs/jwt");
const env_1 = require("../utils/env");
let LoggerMiddleware = class LoggerMiddleware {
    constructor(prisma, jwtService) {
        this.prisma = prisma;
        this.jwtService = jwtService;
    }
    determineAction(method, url) {
        const actionMap = [
            { method: 'POST', urlPattern: 'auth/login', action: 'User Login' },
            { method: 'POST', urlPattern: 'auth/verify-2fa', action: '2FA Verification' },
            { method: 'POST', urlPattern: 'occurrence/public/submit', action: 'Submit Occurrence' },
            { method: 'GET', urlPattern: 'occurrence/search', action: 'Seach Occurrenec' },
            { method: 'PUT', urlPattern: 'occurrence/search', action: 'Update Occurrenec' },
            { method: 'DELETE', urlPattern: 'occurrence/search', action: 'Delete Occurrenec' },
            { method: 'POST', urlPattern: 'occurrence/refnumber', action: 'Generate Ref Number' },
            { method: 'POST', urlPattern: 'users/create', action: 'User Registration' },
            { method: 'GET', urlPattern: 'users', action: 'View Users List' },
            { method: 'GET', urlPattern: 'users/', action: 'View User Profile' },
            { method: 'POST', urlPattern: 'users', action: 'Create User' },
            { method: 'PATCH', urlPattern: 'users/', action: 'Update User' },
            { method: 'DELETE', urlPattern: 'users/', action: 'Delete User' },
            { method: 'GET', urlPattern: 'logs', action: 'View logs ' },
            { method: 'GET', urlPattern: 'logs/', action: 'View Logs' },
            { method: 'POST', urlPattern: 'logs', action: 'Create User' },
            { method: 'PATCH', urlPattern: 'logs/', action: 'Update User' },
            { method: 'DELETE', urlPattern: 'logs/', action: 'Delete User' },
            { method: 'POST', urlPattern: 'reports', action: 'Creare report' },
            { method: 'GET', urlPattern: 'reports', action: 'Get report' },
            { method: 'PATCH', urlPattern: 'reports/', action: 'Updare report' },
            { method: 'DELETE', urlPattern: 'reports', action: 'Delete Report' },
            { method: 'PATCH', urlPattern: 'reports/submit', action: 'Submit report' },
            { method: 'PATCH', urlPattern: 'reports/approve', action: 'Approve Report' },
            { method: 'PATCH', urlPattern: 'reports/revert', action: 'Revert Report' },
        ];
        const matchedAction = actionMap.find(pattern => method === pattern.method && url.includes(pattern.urlPattern));
        if (matchedAction) {
            return matchedAction.action;
        }
        return `${method} ${url}`;
    }
    async use(req, res, next) {
        const userAgent = new ua_parser_js_1.UAParser(req.headers['user-agent']);
        const browserInfo = userAgent.getBrowser();
        const osInfo = userAgent.getOS();
        const token = this.extractTokenFromHeader(req);
        let id = null;
        try {
            const payload = await this.jwtService.verifyAsync(token, {
                secret: env_1.env.JWT_SECRET,
            });
            id = payload.id;
        }
        catch (error) {
        }
        try {
            await this.prisma.logs.create({
                data: {
                    action: this.determineAction(req.method, req.originalUrl),
                    sourceUrl: req.get('origin') || req.get('referer') || 'Direct',
                    sourceIpAddress: req.socket.remoteAddress ||
                        req.ip ||
                        req.connection.remoteAddress ||
                        'Unknown',
                    sourceOS: `${osInfo.name || 'Unknown'} ${osInfo.version || ''}`.trim(),
                    sourceBrowser: `${browserInfo.name || 'Unknown'} ${browserInfo.version || ''}`.trim(),
                    url: req.originalUrl,
                    method: req.method,
                    userId: id || null,
                },
            });
        }
        catch (error) {
            console.error('Failed to create log entry:', error);
        }
        next();
    }
    extractTokenFromHeader(request) {
        const [type, token] = request.headers.authorization?.split(' ') ?? [];
        return type === 'Bearer' ? token : undefined;
    }
};
exports.LoggerMiddleware = LoggerMiddleware;
exports.LoggerMiddleware = LoggerMiddleware = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        jwt_1.JwtService])
], LoggerMiddleware);
//# sourceMappingURL=logs.middleware.js.map