services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: aaid-notification
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123456
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5431:5432"
    networks:
      - backend_network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    depends_on:
      - postgres
    env_file:
      - ./backend/.env.production
    ports:
      - "5001:5000"
    networks:
      - backend_network
    entrypoint: ["/app/entrypoint.sh"]


  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "4201:4200"
    depends_on:
      - backend
    networks:
      - backend_network

  file-server:
    build:
      context: ./file-server
      dockerfile: Dockerfile
    environment:
      SERVER_DOMAIN: http://localhost:3016
    volumes:
      - ./file-server/uploads:/app/uploads
    ports:
      - "3016:3016"
    env_file:
      - ./file-server/.env
    depends_on:
      - backend
    networks:
      - backend_network

volumes:
  postgres_data:

networks:
  backend_network:
    driver: bridge
