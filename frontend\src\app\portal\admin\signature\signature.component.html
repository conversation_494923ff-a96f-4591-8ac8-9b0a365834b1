<div class="w-full h-full p-8 flex flex-col gap-4 overflow-scroll">
    <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold text-gray-700">DG Signature Information</h2>
        <button pRipple (click)="openModal('create')"
            class="flex items-center gap-2 py-2 px-3 rounded-lg text-sm font-medium text-text-white bg-primary active:scale-95">
            <i class="pi pi-plus"></i>
            Add new
        </button>
    </div>
    <div class="pt-4">

        <!-- table -->
        <div class="-mt-4">
            <p-table [value]="signatures" [paginator]="true" [rows]="10" [showCurrentPageReport]="true"
                [totalRecords]="totalSignatures" (onPage)="onPageChange($event)"
                currentPageReportTemplate="Showing {first} to {last} of {totalRecords} signatures"
                [rowsPerPageOptions]="[10, 25, 50]">
                <ng-template pTemplate="header">
                    <tr>
                        <th class="font-medium text-gray-700">Name</th>
                        <th class="font-medium text-gray-700">Image</th>
                        <th class="font-medium text-gray-700">Actions</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-signature>
                    <tr class="">
                        <td class="font-normal border-b">{{ signature.name }}</td>
                        <td class="font-normal border-b">
                            <div class="w-[100px] h-[100px] rounded-lg border">
                                @if(signature.imageUrl){
                                    <img class="w-full h-full object-cover" src="{{signature.imageUrl}}" alt="profile picture">
                                }
                            </div>
                        </td>
                        
                        <td class="font-normal border-b " class="flex items-center gap-2">
                            <button (click)="openModal('edit', signature)"
                                class="bg-primary-50 text-primary-500 py-2 px-3 rounded-lg text-lg active:scale-95 active:ring ring-primary-200"
                                title="edit user">
                                <i class="pi pi-user-edit"></i>
                            </button>
                            <button (click)="confirmDelete(signature)"
                                class="bg-red-100 text-red-400 py-2 px-3 rounded-lg text-lg active:scale-95 active:ring ring-red-400"
                                title="Deactivate user">
                                <i class="pi pi-trash"></i>
                            </button>

                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="5">No signatures found</td>
                    </tr>
                </ng-template>
            </p-table>
            <p-dialog [(visible)]="signatureModalVisible" [modal]="true" [header]="isEditMode ? 'Edit Signature' : 'Add New Signature'"
                [style]="{width: '450px'}">
                <ng-template pTemplate="content">
                    <div class="pb-4">
                        <p>Signature image</p>
                        <div class="flex items-center gap-4">
                            <div class="w-[150px] h-[150px] mt-4 rounded-lg border flex items-center justify-center">
                                <input (change)="onFileSelected($event)" id="image" type="file" accept="image/*" hidden>
                                @if(imagePreview){
                                    <img class="w-full h-full object-cover"  [src]="imagePreview" alt="">
                                }@else if (currentSignature.imageUrl){
                                    <img class="w-full h-full object-cover"  [src]="currentSignature.imageUrl" alt="">
                                }
                            </div>
                            <label for="image" class="cursor-pointer p-2 bg-primary-400 text-sm rounded-lg text-white">
                                Upload
                            </label>
                        </div>
                    </div>
                    <div class="flex flex-col gap-4">
                        <div class="flex flex-col gap-2">
                            <label for="name">Name</label>
                            <input placeholder="Full name"
                                class="w-full outline-none border border-gray-300 rounded-md py-2 px-3 text-gray-800 focus:ring-1 focus:ring-primary "
                                id="name" [(ngModel)]="currentSignature.name" type="text" />
                        </div>
                       
                    </div>
                </ng-template>
                <ng-template pTemplate="footer">
                    <button class="p-2 px-5 rounded-lg text-text-white bg-gray-600 active:scale-95"
                        (click)="signatureModalVisible = false">
                        Cancel
                    </button>
                    <button class="p-2 px-4 rounded-lg text-text-white bg-primary-500 ml-4 active:scale-95"
                        (click)="save()">
                        {{isEditMode ? 'Update' : 'Create'}}
                    </button>
                </ng-template>
            </p-dialog>
            <p-confirmDialog></p-confirmDialog>
            <p-toast position="top-right"></p-toast>
        </div>
    </div>
</div>