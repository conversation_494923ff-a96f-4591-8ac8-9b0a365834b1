import { PrismaService } from 'src/prisma/prisma.service';
import { CreateDocumentDto, UpdateDocumentDto } from './dto';
import { ApiResponse } from 'src/utils/@types';
export declare class DocumentsService {
    private prisma;
    constructor(prisma: PrismaService);
    createDocument(data: CreateDocumentDto): Promise<ApiResponse<{
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        type: import("@prisma/client").$Enums.DocumentType;
        occurrenceId: string;
        url: string;
    }>>;
    getDocuments(): Promise<ApiResponse<({
        occurrence: {
            occurrenceCategory: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                description: string;
                category: string;
                explanation: string | null;
            };
            type: import("@prisma/client").$Enums.OccurrenceType;
            referenceNumber: string;
        };
    } & {
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        type: import("@prisma/client").$Enums.DocumentType;
        occurrenceId: string;
        url: string;
    })[]>>;
    getDocument(id: string): Promise<ApiResponse<{
        occurrence: {
            id: string;
            status: import("@prisma/client").$Enums.OccurrenceStatus | null;
            createdAt: Date;
            updatedAt: Date;
            type: import("@prisma/client").$Enums.OccurrenceType | null;
            reporterName: string;
            reporterEmail: string;
            reporterPhone: string;
            pilotInCommandName: string | null;
            pilotInCommandEmail: string | null;
            pilotInCommandPhone: string | null;
            groundPeoplePerished: number | null;
            groundPeopleInjured: number | null;
            generalWeatherConditions: string | null;
            skyCoverage: string | null;
            meteologicalCondition: import("@prisma/client").$Enums.MeteologicalCondition | null;
            flightRules: import("@prisma/client").$Enums.FlightRules | null;
            occurrenceTime: Date | null;
            operationType: import("@prisma/client").$Enums.OperationType | null;
            flightPhase: string | null;
            latitude: string | null;
            longitude: string | null;
            occurrenceLocation: string | null;
            dangerousGoodCarriedOnBoard: string | null;
            occurrenceCategory_id: string | null;
            referenceNumber: string | null;
        };
    } & {
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        type: import("@prisma/client").$Enums.DocumentType;
        occurrenceId: string;
        url: string;
    }>>;
    updateDocument(id: string, data: UpdateDocumentDto): Promise<ApiResponse<{
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        type: import("@prisma/client").$Enums.DocumentType;
        occurrenceId: string;
        url: string;
    }>>;
    remove(id: string): Promise<ApiResponse<{
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        type: import("@prisma/client").$Enums.DocumentType;
        occurrenceId: string;
        url: string;
    }>>;
}
