import { Body, Controller, Post, Request } from '@nestjs/common';
import { AuthenticationService } from './auth.service';
import { ApiTags } from '@nestjs/swagger';
import { LoginDTO, Verify2faDto } from './dto';

@Controller('auth')
@ApiTags('Auth')
export class AuthenticationController {
  constructor(private authService: AuthenticationService) {}

  @Post('login')
  login(@Body() data: LoginDTO, @Request() req: Request) {
    return this.authService.login(data.identifier, data.password, req);
  }

  @Post('verify-2fa')
  verify2fa(@Body() data: Verify2faDto) {
    return this.authService.verifyMultiFactorCode(data.id, data.code)
  }
}
