-- CreateEnum
CREATE TYPE "ReportStatus" AS ENUM ('PENDING', 'APPROVED', 'REVERTED');

-- AlterTable
ALTER TABLE "Report" ADD COLUMN     "comment" TEXT,
ADD COLUMN     "stateOfDesign" TEXT,
ADD COLUMN     "stateOfManufacturer" TEXT,
ADD COLUMN     "stateOfOperator" TEXT,
ADD COLUMN     "stateOfRegistry" TEXT,
ADD COLUMN     "status" "ReportStatus" NOT NULL DEFAULT 'PENDING',
ALTER COLUMN "occurrenceDate" DROP NOT NULL,
ALTER COLUMN "occurrenceTimeUTC" DROP NOT NULL,
ALTER COLUMN "occurrenceTimeLocal" DROP NOT NULL,
ALTER COLUMN "position" DROP NOT NULL,
ALTER COLUMN "aircraftInfo" DROP NOT NULL,
ALTER COLUMN "investigationDelegation" DROP NOT NULL,
ALTER COLUMN "investigationExtent" DROP NOT NULL,
ALTER COLUMN "latitude" DROP NOT NULL,
ALTER COLUMN "longitude" DROP NOT NULL;
