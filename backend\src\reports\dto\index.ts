import { ApiProperty, ApiPropertyOptional, PartialType } from "@nestjs/swagger";
import { AddressedTo, ReportOccurrenceType } from "@prisma/client";
import { Transform, Type } from "class-transformer";
import { IsString, IsNotEmpty, IsEnum, IsDateString, IsBoolean, IsOptional, IsArray, ValidateNested, IsNumber } from "class-validator";

export class AircraftDto {
    @ApiProperty()
    @IsString()
    @IsOptional()
    manufacturer?: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    model?: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    nationality?: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    registrationMarks?: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    serialNumber?: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    owner?: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    operator?: string;

    @ApiPropertyOptional()
    @IsString()
    @IsOptional()
    hirer?: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    pilotQualification?: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    crewNationality?: string;

    @ApiPropertyOptional()
    @IsString()
    @IsOptional()
    passengerNationality?: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    lastDeparturePoint?: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    intendedLandingPoint?: string;

    @ApiProperty()
    @IsOptional()
    casualties?: {
        crew: {
            aboard: number;
            killed: number;
            injured: number;
        };
        passengers: {
            aboard: number;
            killed: number;
            injured: number;
        };
    };
}

export class CreateReportDto {

    @ApiProperty({ type: [AddressedTo], isArray: true })
    @IsArray()
    @IsNotEmpty({ each: true })
    @IsEnum(AddressedTo, { each: true })
    addressedTo: AddressedTo[];

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    referenceNumber: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    stateOfRegistry?: string;
  
    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    stateOfDesign?: string;
  
    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    stateOfManufacturer?: string;
  
    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    stateOfOperator?: string;

    @ApiProperty({ enum: ReportOccurrenceType })
    @IsEnum(ReportOccurrenceType)
    occurrenceType: ReportOccurrenceType;

    @ApiProperty()
    @IsDateString()
    @IsOptional()
    @Transform(({ value }) => {
        return value ? new Date(value).toISOString() : null;
    })
    occurrenceDate?: string;

    @ApiProperty()
    @IsDateString()
    @IsOptional()
    occurrenceTimeUTC: string;

    @ApiProperty()
    @IsDateString()
    @IsOptional()
    occurrenceTimeLocal?: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    position?: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    latitude?: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    longitude?: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    occurrenceDescription: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    damageExtent: string;

    @ApiProperty()
    @IsBoolean()
    dangerousGoodsPresent: boolean;

    @ApiPropertyOptional()
    @IsString()
    @IsOptional()
    dangerousGoodsDescription?: string;

    @ApiProperty({ type: [AircraftDto], isArray: true })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => AircraftDto)
    @IsOptional()
    aircraftInfo?: AircraftDto[];

    @ApiProperty()
    @IsString()
    @IsOptional()
    investigationExtent?: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    investigationDelegation?: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    areaCharacteristics?: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    accessRequirements?: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    originatingAuthority?: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    investigatorName: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    investigatorMobile: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    investigatorEmail: string;

    @ApiProperty()
    @IsNumber()
    @IsOptional()
    groundPeopleInjured?: number;

    @ApiProperty()
    @IsNumber()
    @IsOptional()
    groundPeoplePerished?: number;
}

export class UpdateReportDto extends PartialType(CreateReportDto) { }

export class ApproveDTO {
    @ApiProperty()
    @IsString()
    @IsOptional()
    comment?: string
}