import { Controller, Get } from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

@ApiTags('Dashboard')
@Controller('dashboard')
export class DashboardController {
    constructor(
        private dashboardService: DashboardService
    ){}

    @Get()
    @ApiOperation({description: 'Get dashboard data'})
    getAll(){
        return this.dashboardService.getAll()
    }
}
