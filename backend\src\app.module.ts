import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthenticationModule } from './auth/auth.module';
import { ConfigModule } from '@nestjs/config';
import { OccurrenceModule } from './occurrence/occurrence.module';
import { UsersModule } from './users/users.module';
import { SettingsModule } from './settings/settings.module';
import { PrismaService } from './prisma/prisma.service';
import { MailModule } from './utils/mail/mail.module';
import { SmsModule } from './utils/sms/sms.module';
import { DocumentsModule } from './documents/documents.module';
import { ReportsModule } from './reports/reports.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { LoggerMiddleware } from './logs/logs.middleware';
import { LogsModule } from './logs/logs.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    AuthenticationModule,
    OccurrenceModule,
    UsersModule,
    SettingsModule,
    MailModule,
    SmsModule,
    DocumentsModule,
    ReportsModule,
    DashboardModule,
    LogsModule
  ],
  controllers: [AppController],
  providers: [AppService, PrismaService],
})
export class AppModule implements NestModule{
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(LoggerMiddleware)
      .forRoutes('*'); 
  }
}
